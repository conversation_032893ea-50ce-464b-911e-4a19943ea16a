<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Wildberries System API Configuration
    |--------------------------------------------------------------------------
    |
    | Настройки для системного API Wildberries
    |
    */

    'system_api' => [
        'base_url' => env('WILDBERRIES_SYSTEM_API_URL', 'https://supplies-api.wildberries.ru'),
        'timeout' => env('WILDBERRIES_SYSTEM_API_TIMEOUT', 30),
        'retry_attempts' => env('WILDBERRIES_SYSTEM_API_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('WILDBERRIES_SYSTEM_API_RETRY_DELAY', 1000), // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Wildberries System Warehouses Sync Configuration
    |--------------------------------------------------------------------------
    |
    | Настройки синхронизации системных складов Wildberries
    |
    */

    'system_warehouses' => [
        'sync_enabled' => env('WILDBERRIES_SYSTEM_WAREHOUSES_SYNC_ENABLED', true),
        'sync_schedule' => env('WILDBERRIES_SYSTEM_WAREHOUSES_SYNC_SCHEDULE', '0 */6 * * *'), // Каждые 6 часов
        'batch_size' => env('WILDBERRIES_SYSTEM_WAREHOUSES_BATCH_SIZE', 100),
        'timeout' => env('WILDBERRIES_SYSTEM_WAREHOUSES_TIMEOUT', 300), // 5 минут
        'max_attempts' => env('WILDBERRIES_SYSTEM_WAREHOUSES_MAX_ATTEMPTS', 3),
        
        // Настройки маппинга
        'mapping' => [
            'create_missing' => env('WILDBERRIES_CREATE_MISSING_WAREHOUSES', true),
            'update_existing' => env('WILDBERRIES_UPDATE_EXISTING_WAREHOUSES', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Wildberries Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Настройки логирования для Wildberries
    |
    */

    'logging' => [
        'enabled' => env('WILDBERRIES_LOGGING_ENABLED', true),
        'level' => env('WILDBERRIES_LOGGING_LEVEL', 'info'),
        'channel' => env('WILDBERRIES_LOGGING_CHANNEL', 'daily'),
        'context' => [
            'service' => 'wildberries',
            'component' => 'system_warehouses',
        ],
    ],
]; 