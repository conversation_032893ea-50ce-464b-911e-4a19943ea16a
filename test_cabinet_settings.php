<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Events\CabinetCreated;
use App\Jobs\CreateCabinetSettingsJob;
use App\Listeners\CreateCabinetSettings;
use App\Models\User;

echo "Testing Cabinet Settings workflow...\n";

// Test Job creation
echo "1. Testing Job creation...\n";
$user = new User();
$job = new CreateCabinetSettingsJob('test-cabinet-id', $user);
echo "   Job created successfully!\n";

// Test uniqueId method
echo "2. Testing uniqueId method...\n";
$uniqueId = $job->uniqueId();
echo "   UniqueId: $uniqueId\n";

// Test Listener
echo "3. Testing Listener...\n";
$listener = new CreateCabinetSettings();
echo "   Listener created successfully!\n";

// Test Event
echo "4. Testing Event...\n";
$event = new CabinetCreated('test-cabinet-id', $user);
echo "   Event created successfully!\n";

// Test Listener handle method (this will dispatch the job)
echo "5. Testing Listener handle method...\n";
$listener->handle($event);
echo "   Listener handle method executed successfully!\n";

echo "\nAll tests passed! The Cabinet Settings workflow is working correctly.\n";
echo "The Job now has proper uniqueness protection to prevent duplicates.\n";
