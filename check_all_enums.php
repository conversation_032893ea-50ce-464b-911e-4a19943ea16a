<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Checking all enum values in contractors:' . PHP_EOL;

$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        
        // Check simple parameters
        if (isset($schema['enum'])) {
            echo "- $name: enum[" . implode(',', $schema['enum']) . "]" . PHP_EOL;
        }
        
        // Check array items enum
        if ($schema['type'] === 'array' && isset($schema['items']['enum'])) {
            echo "- $name: array[enum:" . count($schema['items']['enum']) . " values]" . PHP_EOL;
        }
        
        // Check object properties
        if ($param['name'] === 'filters' && $param['schema']['type'] === 'object') {
            if (isset($param['schema']['properties'])) {
                foreach ($param['schema']['properties'] as $filterName => $filterSchema) {
                    if (isset($filterSchema['properties'])) {
                        foreach ($filterSchema['properties'] as $propName => $propSchema) {
                            if (isset($propSchema['enum'])) {
                                echo "  $filterName.$propName: enum[" . implode(',', $propSchema['enum']) . "]" . PHP_EOL;
                            }
                        }
                    }
                }
            }
        }
    }
}
