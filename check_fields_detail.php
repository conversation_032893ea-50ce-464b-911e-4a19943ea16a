<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Detailed fields parameter check:' . PHP_EOL;

$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        if ($param['name'] === 'fields') {
            echo "fields parameter:" . PHP_EOL;
            echo "  type: " . $param['schema']['type'] . PHP_EOL;
            
            if (isset($param['schema']['items'])) {
                echo "  items:" . PHP_EOL;
                echo "    type: " . $param['schema']['items']['type'] . PHP_EOL;
                
                if (isset($param['schema']['items']['enum'])) {
                    $enumCount = count($param['schema']['items']['enum']);
                    echo "    enum: $enumCount values" . PHP_EOL;
                    echo "    first 5: " . implode(',', array_slice($param['schema']['items']['enum'], 0, 5)) . PHP_EOL;
                } else {
                    echo "    enum: NOT SET" . PHP_EOL;
                }
                
                if (isset($param['schema']['items']['format'])) {
                    echo "    format: " . $param['schema']['items']['format'] . PHP_EOL;
                }
            } else {
                echo "  items: NOT SET" . PHP_EOL;
            }
            
            break;
        }
    }
}
