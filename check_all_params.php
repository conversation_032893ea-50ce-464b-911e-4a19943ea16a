<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

echo 'All parameters:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        $required = $param['required'] ?? false ? ' [required]' : ' [optional]';
        
        echo "- $name: ";
        
        if ($schema['type'] === 'object') {
            echo "object$required" . PHP_EOL;
            if (isset($schema['properties'])) {
                foreach ($schema['properties'] as $propName => $propSchema) {
                    echo "  - $propName: " . $propSchema['type'];
                    if ($propSchema['type'] === 'array' && isset($propSchema['items'])) {
                        echo "[" . $propSchema['items']['type'];
                        if (isset($propSchema['items']['format'])) {
                            echo "<" . $propSchema['items']['format'] . ">";
                        }
                        echo "]";
                    }
                    echo PHP_EOL;
                }
            }
        } else {
            echo $schema['type'];
            if (isset($schema['format'])) {
                echo "<" . $schema['format'] . ">";
            }
            if ($schema['type'] === 'array' && isset($schema['items'])) {
                echo "[" . $schema['items']['type'];
                if (isset($schema['items']['format'])) {
                    echo "<" . $schema['items']['format'] . ">";
                }
                echo "]";
            }
            echo $required . PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Total parameters: ' . (isset($acceptancesGet['parameters']) ? count($acceptancesGet['parameters']) : 0) . PHP_EOL;
