<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

echo 'Parameters structure:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        
        echo "- $name: ";
        
        if ($schema['type'] === 'object') {
            echo "object" . PHP_EOL;
            if (isset($schema['properties'])) {
                foreach ($schema['properties'] as $propName => $propSchema) {
                    echo "  - $propName: " . $propSchema['type'];
                    if ($propSchema['type'] === 'object' && isset($propSchema['properties'])) {
                        echo " (object)" . PHP_EOL;
                        foreach ($propSchema['properties'] as $nestedName => $nestedSchema) {
                            echo "    - $nestedName: " . $nestedSchema['type'];
                            if ($nestedSchema['type'] === 'array' && isset($nestedSchema['items'])) {
                                echo "[" . $nestedSchema['items']['type'] . "]";
                            }
                            echo PHP_EOL;
                        }
                    } else {
                        if ($propSchema['type'] === 'array' && isset($propSchema['items'])) {
                            echo "[" . $propSchema['items']['type'] . "]";
                        }
                        echo PHP_EOL;
                    }
                }
            }
        } else {
            echo $schema['type'];
            if ($schema['type'] === 'array' && isset($schema['items'])) {
                echo "[" . $schema['items']['type'] . "]";
            }
            echo PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Total parameters: ' . (isset($acceptancesGet['parameters']) ? count($acceptancesGet['parameters']) : 0) . PHP_EOL;
