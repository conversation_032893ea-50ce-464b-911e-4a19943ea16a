<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test our extension logic
$controllerClass = \App\Http\Controllers\Api\Internal\Contractors\Contractors\ContractorController::class;
$reflection = new \ReflectionClass($controllerClass);
$indexMethod = $reflection->getMethod('index');

echo "ContractorController::index method:" . PHP_EOL;

foreach ($indexMethod->getParameters() as $parameter) {
    $type = $parameter->getType();
    echo "Parameter: " . $parameter->getName() . PHP_EOL;
    
    if ($type && $type instanceof \ReflectionNamedType) {
        $className = $type->getName();
        echo "Type: " . $className . PHP_EOL;
        
        if (class_exists($className)) {
            echo "Class exists: YES" . PHP_EOL;
            
            if (is_subclass_of($className, \Illuminate\Foundation\Http\FormRequest::class)) {
                echo "Is FormRequest: YES" . PHP_EOL;
                
                // Try to create instance
                try {
                    $entity = app(\App\Entities\ContractorEntity::class);
                    $request = new $className($entity);
                    $rules = $request->rules();
                    
                    $excludeIfCount = 0;
                    foreach ($rules as $fieldName => $fieldRules) {
                        $containsExcludeIf = false;
                        
                        if (is_string($fieldRules)) {
                            $containsExcludeIf = str_contains($fieldRules, 'excludeIf') || str_ends_with($fieldRules, '|');
                        } elseif (is_array($fieldRules)) {
                            foreach ($fieldRules as $rule) {
                                if ($rule instanceof \Illuminate\Validation\Rules\ExcludeIf) {
                                    $containsExcludeIf = true;
                                    break;
                                }
                                if (is_string($rule) && str_contains($rule, 'excludeIf')) {
                                    $containsExcludeIf = true;
                                    break;
                                }
                            }
                        }
                        
                        if ($containsExcludeIf) {
                            $excludeIfCount++;
                        }
                    }
                    
                    echo "Rules with excludeIf: $excludeIfCount" . PHP_EOL;
                    echo "Total rules: " . count($rules) . PHP_EOL;
                    
                } catch (\Exception $e) {
                    echo "Error creating instance: " . $e->getMessage() . PHP_EOL;
                }
            } else {
                echo "Is FormRequest: NO" . PHP_EOL;
            }
        } else {
            echo "Class exists: NO" . PHP_EOL;
        }
    }
}
