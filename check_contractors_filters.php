<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Contractors filters object details:' . PHP_EOL;

$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        if ($param['name'] === 'filters' && $param['schema']['type'] === 'object') {
            echo "filters object properties:" . PHP_EOL;
            if (isset($param['schema']['properties'])) {
                foreach ($param['schema']['properties'] as $filterName => $filterSchema) {
                    echo "  $filterName:" . PHP_EOL;
                    if (isset($filterSchema['properties'])) {
                        foreach ($filterSchema['properties'] as $propName => $propSchema) {
                            echo "    - $propName: ";
                            
                            if (is_array($propSchema['type'])) {
                                echo implode('|', $propSchema['type']);
                            } else {
                                echo $propSchema['type'];
                            }
                            
                            if (isset($propSchema['format'])) {
                                echo "<" . $propSchema['format'] . ">";
                            }
                            
                            if (isset($propSchema['enum'])) {
                                echo " enum[" . implode(',', array_slice($propSchema['enum'], 0, 3)) . (count($propSchema['enum']) > 3 ? '...' : '') . "]";
                            }
                            
                            if ($propSchema['type'] === 'array' && isset($propSchema['items'])) {
                                echo "[" . $propSchema['items']['type'];
                                if (isset($propSchema['items']['format'])) {
                                    echo "<" . $propSchema['items']['format'] . ">";
                                }
                                echo "]";
                            }
                            
                            echo PHP_EOL;
                        }
                    } else {
                        echo "    No properties" . PHP_EOL;
                    }
                }
                echo PHP_EOL . "Total filter groups: " . count($param['schema']['properties']) . PHP_EOL;
            } else {
                echo "  No properties found" . PHP_EOL;
            }
            break;
        }
    }
}
