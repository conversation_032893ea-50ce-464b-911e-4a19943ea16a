<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Create instance manually to avoid validation
$entity = app(\App\Entities\ContractorEntity::class);
$request = new \App\Http\Requests\Api\Internal\Contractors\ContractorsIndexRequest($entity);
$rules = $request->rules();

echo "ContractorsIndexRequest rules with excludeIf:" . PHP_EOL;
$count = 0;
foreach ($rules as $fieldName => $fieldRules) {
    $containsExcludeIf = false;
    
    if (is_string($fieldRules)) {
        $containsExcludeIf = str_contains($fieldRules, 'excludeIf') || str_ends_with($fieldRules, '|');
    } elseif (is_array($fieldRules)) {
        foreach ($fieldRules as $rule) {
            if ($rule instanceof \Illuminate\Validation\Rules\ExcludeIf) {
                $containsExcludeIf = true;
                break;
            }
            if (is_string($rule) && str_contains($rule, 'excludeIf')) {
                $containsExcludeIf = true;
                break;
            }
        }
    }
    
    if ($containsExcludeIf) {
        $count++;
        echo "- $fieldName" . PHP_EOL;
        if (str_starts_with($fieldName, 'filters.')) {
            echo "  Is filter: YES" . PHP_EOL;
        } else {
            echo "  Is filter: NO" . PHP_EOL;
        }
    }
}

echo PHP_EOL . "Total rules with excludeIf: $count" . PHP_EOL;
