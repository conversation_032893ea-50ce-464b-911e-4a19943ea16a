<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContractorApiTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Department $department;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);
        $this->department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
            'department_id' => $this->department->id
        ]);
        
        // Создаем связь между пользователем и кабинетом
        CabinetEmployee::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);
        
        $this->actingAs($this->user, 'sanctum');
    }

    public function test_contractor_index_api_works()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        Contractor::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
        ]);

        $response = $this->getJson('/api/internal/contractors?cabinet_id=' . $this->cabinet->id);

        // Проверяем, что запрос не возвращает ошибку
        if ($response->status() !== 200) {
            $this->fail('API returned status ' . $response->status() . ': ' . $response->getContent());
        }

        $response->assertStatus(200);
        
        // Проверяем базовую структуру ответа
        $data = $response->json();
        $this->assertArrayHasKey('data', $data);
        
        // Если есть данные, проверяем их структуру
        if (count($data['data']) > 0) {
            $firstItem = $data['data'][0];
            $this->assertArrayHasKey('id', $firstItem);
            $this->assertArrayHasKey('title', $firstItem);
            $this->assertArrayHasKey('cabinet_id', $firstItem);
        }
    }
}
