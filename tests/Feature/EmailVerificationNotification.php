<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EmailVerificationNotification extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем пользователя
        $this->user = User::factory()->create([
            'email_verified_at' => null
        ]);

        $this->actingAs($this->user, 'sanctum');
    }

    public function test_cannot_send_verification_notification_if_email_already_verified(): void
    {
        // Устанавливаем время верификации
        $this->user->email_verified_at = now();
        $this->user->save();

        $response = $this->postJson('/api/email/verification-notification');

        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Your mail already verified.',
                'errors' => [
                    'code' => ['Your mail already verified.']
                ]
            ]);
    }

    public function test_can_send_verification_notification_in_local_environment(): void
    {
        // Устанавливаем локальное окружение
        app()->detectEnvironment(function () {
            return 'local';
        });

        $response = $this->postJson('/api/email/verification-notification');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'code',
                'expires_at'
            ]);

        // Проверяем что код 4-значный
        $code = $response->json('code');
        $this->assertMatchesRegularExpression('/^\d{4}$/', $code);

        // Проверяем что код сохранился в БД
        $this->assertDatabaseHas('email_verification_codes', [
            'user_id' => $this->user->id,
            'code' => $code
        ]);
    }

    /**
     * @throws \Exception
     */
    public function test_can_send_verification_notification_in_production(): void
    {
        // Устанавливаем production окружение
        app()->detectEnvironment(function () {
            return 'production';
        });

        // Подготавливаем фейковый notification
        Notification::fake();

        $response = $this->postJson('/api/email/verification-notification');

        $response->assertStatus(200)
            ->assertJson(['Send new code']);

        // Проверяем что уведомление было отправлено
        Notification::assertSentTo(
            $this->user,
            \App\Notifications\VerifyEmail::class
        );
    }
}
