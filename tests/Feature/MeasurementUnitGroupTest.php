<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\MeasurementUnitGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Enums\Api\Internal\MeasurementUnitTypeEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;

class MeasurementUnitGroupTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Department $department;
    protected Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_measurement_unit_groups_list(): void
    {
        // Arrange
        $customGroups = MeasurementUnitGroup::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $systemGroups = MeasurementUnitGroup::factory()->count(2)->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups?cabinet_id={$this->cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'name',
                        'tech_type',
                        'is_system',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'meta'
            ])
            ->assertJsonCount(5, 'data');
    }

    public function test_system_groups_are_not_shown_if_copied_to_cabinet(): void
    {
        // Arrange
        $customGroups = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $systemGroup = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        // Создаем запись в cabinet_measurement_system_groups
        DB::table('cabinet_measurement_system_groups')->insert([
            'cabinet_id' => $this->cabinet->id,
            'system_group_id' => $systemGroup->id,
            'id' => $this->faker->uuid,
            'group_id' => $customGroups->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups?cabinet_id={$this->cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_cannot_get_measurement_unit_groups_without_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/measurement-units/groups');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_measurement_unit_groups_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/measurement-units/groups?cabinet_id=invalid-uuid');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_access_other_cabinet_measurement_unit_groups(): void
    {
        // Arrange
        MeasurementUnitGroup::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'is_system' => false
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups?cabinet_id={$this->otherCabinet->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_measurement_unit_group_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => $data['name'],
            'tech_type' => $data['tech_type'],
            'is_system' => false
        ]);
    }

    public function test_cannot_create_measurement_unit_group_without_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units/groups', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'tech_type'
            ]);
    }

    public function test_cannot_create_measurement_unit_group_with_invalid_cabinet_id(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units/groups', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_create_measurement_unit_group_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units/groups', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_create_measurement_unit_group_with_invalid_tech_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'tech_type' => 'invalid_type'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units/groups', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['tech_type']);
    }

    public function test_can_update_measurement_unit_group_with_minimal_fields(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $data = [
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $group->id,
            'name' => $data['name'],
            'tech_type' => $data['tech_type']
        ]);
    }

    public function test_cannot_update_measurement_unit_group_without_required_fields(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $data = [];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'tech_type'
            ]);
    }

    public function test_cannot_update_measurement_unit_group_with_invalid_tech_type(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $data = [
            'name' => $this->faker->word(),
            'tech_type' => 'invalid_type'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['tech_type']);
    }

    public function test_cannot_update_measurement_unit_group_in_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'is_system' => false
        ]);

        $data = [
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_non_existent_measurement_unit_group(): void
    {
        // Arrange
        $data = [
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/" . $this->faker->uuid, $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_system_measurement_unit_group(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        $data = [
            'name' => $this->faker->word(),
            'tech_type' => MeasurementUnitTypeEnum::WEIGHT->value
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_show_measurement_unit_group(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    'id',
                    'cabinet_id',
                    'name',
                    'tech_type',
                    'is_system',
                    'created_at',
                    'updated_at'
            ])
            ->assertJson([
                    'id' => $group->id,
                    'cabinet_id' => $this->cabinet->id,
                    'name' => $group->name,
                    'tech_type' => $group->tech_type,
                    'is_system' => false
            ]);
    }

    public function test_can_show_system_measurement_unit_group(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    'id',
                    'cabinet_id',
                    'name',
                    'tech_type',
                    'is_system',
                    'created_at',
                    'updated_at'
            ])
            ->assertJson([
                    'id' => $group->id,
                    'cabinet_id' => null,
                    'name' => $group->name,
                    'tech_type' => $group->tech_type,
                    'is_system' => true
            ]);
    }

    public function test_cannot_show_measurement_unit_group_from_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'is_system' => false
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_measurement_unit_group(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/groups/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_measurement_unit_group(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('measurement_unit_groups', [
            'id' => $group->id
        ]);
    }

    public function test_cannot_delete_measurement_unit_group_from_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'is_system' => false
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $group->id
        ]);
    }

    public function test_cannot_delete_non_existent_measurement_unit_group(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/groups/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_delete_system_measurement_unit_group(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/groups/{$group->id}");

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $group->id
        ]);
    }
}
