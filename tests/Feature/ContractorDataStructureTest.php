<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use App\Repositories\Contractors\Contractors\ContractorsRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContractorDataStructureTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Department $department;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');
        
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_contractor_repository_returns_correct_data_structure()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
            'title' => 'Test Contractor Repository',
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
        ]);

        $repository = app(ContractorsRepository::class);
        $data = $repository->get($this->cabinet->id);

        $this->assertNotEmpty($data);
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $data);
        
        $firstContractor = $data->first();
        $this->assertNotNull($firstContractor);
        
        // Выводим структуру данных для отладки
        dump('Contractor data structure:', $firstContractor);
        
        $this->assertTrue(true); // Просто проверяем, что тест проходит
    }
}
