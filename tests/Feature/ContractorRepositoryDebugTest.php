<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use App\Services\Api\Internal\Contractors\ContractorsService\ContractorService;
use App\DTO\IndexRequestDTO;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContractorRepositoryDebugTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Department $department;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');
        
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_debug_contractor_service_response()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
            'title' => 'Debug Contractor',
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
        ]);

        $service = app(ContractorService::class);
        
        $dto = new IndexRequestDTO(
            id: $this->cabinet->id,
            filters: [],
            fields: ['*'],
            sortField: null,
            sortDirection: 'asc',
            page: 1,
            perPage: 15
        );
        
        $result = $service->index($dto);
        
        dump('Service result type:', get_class($result));
        dump('Service result count:', $result->count());
        dump('Service result first item:', $result->first());
        
        $this->assertTrue(true);
    }
}
