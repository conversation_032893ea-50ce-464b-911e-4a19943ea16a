<?php

namespace Tests\Feature;

use App\Http\Resources\ContractorCollection;
use App\Http\Resources\ContractorResource;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Tests\TestCase;

class ContractorResourceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Department $department;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);
        $this->department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
            'department_id' => $this->department->id
        ]);

        // Создаем связь между пользователем и кабинетом
        CabinetEmployee::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        $this->actingAs($this->user, 'sanctum');
    }

    public function test_contractor_resource_transforms_data_correctly()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
            'title' => 'Test Contractor',
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
        ]);

        $resource = new ContractorResource($contractor);
        $array = $resource->toArray(request());

        $this->assertEquals($contractor->id, $array['id']);
        $this->assertEquals('Test Contractor', $array['title']);
        $this->assertEquals('+79001234567', $array['phone']);
        $this->assertEquals('<EMAIL>', $array['email']);
        $this->assertEquals($this->cabinet->id, $array['cabinet_id']);
        $this->assertEquals($status->id, $array['status_id']);
    }

    public function test_contractor_collection_transforms_data_correctly()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        $contractors = Contractor::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
        ]);

        $collection = new ContractorCollection($contractors);
        $array = $collection->toArray(request());

        $this->assertArrayHasKey('data', $array);
        $this->assertCount(3, $array['data']);
        
        foreach ($array['data'] as $index => $contractorData) {
            $this->assertEquals($contractors[$index]->id, $contractorData['id']);
            $this->assertEquals($contractors[$index]->title, $contractorData['title']);
        }
    }

    public function test_contractor_index_endpoint_returns_collection()
    {
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        
        Contractor::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
        ]);

        $response = $this->getJson('/api/internal/contractors?cabinet_id=' . $this->cabinet->id);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'title',
                        'cabinet_id',
                        'status_id',
                        'phone',
                        'email',
                    ]
                ]
            ]);
    }
}
