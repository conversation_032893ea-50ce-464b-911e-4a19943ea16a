<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetCreateHandler;
use App\Services\Api\Internal\Workspace\CabinetsService\DTO\CabinetDTO;
use App\Contracts\Repositories\CabinetsRepositoryContract;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;

class CabinetCreationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_cabinet_creation_workflow(): void
    {
        // Создаем пользователя
        $user = User::factory()->create();
        
        // Аутентифицируем пользователя
        Auth::login($user);

        // Устанавливаем кеш для пользователя
        Cache::put("permissions_{$user->id}", ['old' => 'data'], 600);
        Cache::put("employees_{$user->id}", ['old' => 'data'], 600);

        // Проверяем, что кеш существует
        $this->assertTrue(Cache::has("permissions_{$user->id}"));
        $this->assertTrue(Cache::has("employees_{$user->id}"));

        // Фейкаем очередь, чтобы job не выполнялся
        Queue::fake();

        // Создаем DTO для кабинета
        $dto = new CabinetDTO('Test Cabinet', $user->id);

        // Создаем handler
        $repository = app(CabinetsRepositoryContract::class);
        $handler = new CabinetCreateHandler($repository);

        // Выполняем создание кабинета
        $cabinetId = $handler->run($dto);

        // Проверяем, что кабинет создан
        $this->assertNotEmpty($cabinetId);
        
        // Проверяем, что кабинет существует в базе данных
        $cabinet = Cabinet::find($cabinetId);
        $this->assertNotNull($cabinet);
        $this->assertEquals('Test Cabinet', $cabinet->name);
        $this->assertEquals($user->id, $cabinet->user_id);

        // Проверяем, что кеш был очищен после создания кабинета
        // (это происходит в слушателе события CabinetCreated)
        $this->assertFalse(Cache::has("permissions_{$user->id}"));
        $this->assertFalse(Cache::has("employees_{$user->id}"));
    }
}
