<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Country;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CountryTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        // Создаем сотрудника
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Создаем основной кабинет
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем другой кабинет для тестов изоляции
        $this->otherCabinet = Cabinet::factory()->create();

        // Связываем сотрудника с кабинетом
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем отдел
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_countries_list(): void
    {
        // Создаем страны для текущего кабинета
        Country::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем системные страны
        Country::factory()->count(2)->create([
            'cabinet_id' => null
        ]);

        // Создаем страны для другого кабинета
        Country::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'name',
                        'full_name',
                        'code',
                        'iso2',
                        'iso3',
                        'is_common'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили 5 записей (3 из текущего кабинета + 2 системные)
        $response->assertJsonCount(5, 'data');
    }

    public function test_cannot_access_other_cabinet_countries(): void
    {
        // Создаем страны для другого кабинета
        Country::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => 'not-a-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
            'sortField' => 'invalid_field',
            'fields' => ['invalid_field'],
            'filters' => [
                'type' => [
                    'value' => 'invalid_type'
                ],
                'name' => [
                    'value' => ['not-a-string']
                ],
                'full_name' => [
                    'value' => ['not-a-string'],
                    'condition' => 'invalid_condition'
                ],
                'code' => [
                    'value' => ['not-a-string'],
                    'condition' => 'invalid_condition'
                ],
                'iso2' => [
                    'value' => ['not-a-string'],
                    'condition' => 'invalid_condition'
                ],
                'iso3' => [
                    'value' => ['not-a-string'],
                    'condition' => 'invalid_condition'
                ],
                'department_owners' => [
                    'value' => ['not-a-uuid'],
                    'condition' => 'invalid_condition'
                ],
                'employee_owners' => [
                    'value' => ['not-a-uuid'],
                    'condition' => 'invalid_condition'
                ],
                'is_common' => [
                    'value' => 'not-a-boolean'
                ],
                'updated_at' => [
                    'from' => 'invalid-date',
                    'to' => 'invalid-date'
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'sortField',
                'fields.0',
                'filters.type.value',
                'filters.name.value',
                'filters.full_name.value',
                'filters.full_name.condition',
                'filters.code.value',
                'filters.code.condition',
                'filters.iso2.value',
                'filters.iso2.condition',
                'filters.iso3.value',
                'filters.iso3.condition',
                'filters.department_owners.value.0',
                'filters.department_owners.condition',
                'filters.employee_owners.value.0',
                'filters.employee_owners.condition',
                'filters.is_common.value',
                'filters.updated_at.from',
                'filters.updated_at.to'
            ]);
    }

    public function test_index_sort_by_name_asc(): void
    {
        // Создаем страны с разными именами
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'AAA Country'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'BBB Country'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'ZZZ Country'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('AAA Country', $data[0]['name']);
        $this->assertEquals('BBB Country', $data[1]['name']);
        $this->assertEquals('ZZZ Country', $data[2]['name']);
    }

    public function test_index_sort_by_name_desc(): void
    {
        // Создаем страны с разными именами
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'AAA Country'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'BBB Country'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'ZZZ Country'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('ZZZ Country', $data[0]['name']);
        $this->assertEquals('BBB Country', $data[1]['name']);
        $this->assertEquals('AAA Country', $data[2]['name']);
    }

    public function test_index_with_selected_fields(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'name', 'code']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'code'
                    ]
                ]
            ]);

        // Проверяем что другие поля отсутствуют
        $this->assertArrayNotHasKey('created_at', $response->json('data.0'));
        $this->assertArrayNotHasKey('updated_at', $response->json('data.0'));
        $this->assertArrayNotHasKey('full_name', $response->json('data.0'));
    }

    public function test_index_pagination_first_page(): void
    {
        // Создаем 25 стран
        Country::factory()->count(25)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJsonStructure([
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(1, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(3, $response->json('meta.last_page'));
        $this->assertEquals(25, $response->json('meta.total'));
    }

    public function test_index_pagination_last_page(): void
    {
        // Создаем 25 стран
        Country::factory()->count(25)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 3,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(5, 'data')
            ->assertJsonStructure([
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(3, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(3, $response->json('meta.last_page'));
        $this->assertEquals(25, $response->json('meta.total'));
    }

    public function test_index_search_filter(): void
    {
        // Создаем страны с разными названиями
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'United States of America'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'United Kingdom'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'France'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'United'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $country) {
            $this->assertStringContainsString('United', $country['full_name']);
        }
    }

    public function test_index_filter_by_updated_at(): void
    {
        // Создаем страну с определенной датой обновления
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-01-01 12:00:00'
        ]);

        // Создаем еще одну страну с более поздней датой
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-02-01 12:00:00'
        ]);

        // Фильтруем по периоду, который включает только первую страну
        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => '01.01.2024 00:00',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($country->id, $response->json('data.0.id'));
    }

    public function test_index_filter_is_common_true(): void
    {
        // Создаем общую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true
        ]);

        // Создаем не общую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => false
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertTrue($response->json('data.0.is_common'));
    }

    public function test_index_filter_is_common_false(): void
    {
        // Создаем общую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true
        ]);

        // Создаем не общую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => false
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => false
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertFalse($response->json('data.0.is_common'));
    }

    public function test_index_filter_name_in(): void
    {
        // Создаем тестовые страны
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Country'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Another Country'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'name' => [
                    'value' => 'Test'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('Test Country', $response->json('data.0.name'));
    }

    public function test_index_filter_full_name_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Test Full Name'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Another Full Name'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'full_name' => [
                    'value' => 'Test',
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('Test Full Name', $response->json('data.0.full_name'));
    }

    public function test_index_filter_full_name_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Test Full Name'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Another Full Name'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'full_name' => [
                    'value' => 'Test',
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('Another Full Name', $response->json('data.0.full_name'));
    }

    public function test_index_filter_full_name_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Test Full Name'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'full_name' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.full_name'));
    }

    public function test_index_filter_full_name_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'full_name' => 'Test Full Name'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'full_name' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('Test Full Name', $response->json('data.0.full_name'));
    }

    public function test_index_filter_code_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'OTHER'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'TEST',
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('TEST', $response->json('data.0.code'));
    }

    public function test_index_filter_code_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'OTHER'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'TEST',
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('OTHER', $response->json('data.0.code'));
    }

    public function test_index_filter_code_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.code'));
    }

    public function test_index_filter_code_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('TEST', $response->json('data.0.code'));
    }

    public function test_index_filter_employee_owners_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($this->employee->id, $response->json('data.0.employee_id'));
    }

    public function test_index_filter_employee_owners_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        $otherCountry = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($otherCountry->id, $response->json('data.0.id'));
    }

    public function test_index_filter_employee_owners_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.employee_id'));
    }

    public function test_index_filter_employee_owners_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($this->employee->id, $response->json('data.0.employee_id'));
    }

    // Аналогичные тесты для department_owners
    public function test_index_filter_department_owners_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($this->department->id, $response->json('data.0.department_id'));
    }

    public function test_index_filter_department_owners_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);

        $otherCountry = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($otherCountry->id, $response->json('data.0.id'));
    }

    public function test_index_filter_department_owners_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.department_id'));
    }

    public function test_index_filter_department_owners_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => null
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals($this->department->id, $response->json('data.0.department_id'));
    }

    public function test_index_filter_iso2_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'US'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'GB'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso2' => [
                    'value' => 'US',
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('US', $response->json('data.0.iso2'));
    }

    public function test_index_filter_iso2_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'US'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'GB'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso2' => [
                    'value' => 'US',
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('GB', $response->json('data.0.iso2'));
    }

    public function test_index_filter_iso2_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'US'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso2' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.iso2'));
    }

    public function test_index_filter_iso2_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso2' => 'US'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso2' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('US', $response->json('data.0.iso2'));
    }

    public function test_index_filter_iso3_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'USA'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'GBR'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso3' => [
                    'value' => 'USA',
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('USA', $response->json('data.0.iso3'));
    }

    public function test_index_filter_iso3_not_in(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'USA'
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'GBR'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso3' => [
                    'value' => 'USA',
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('GBR', $response->json('data.0.iso3'));
    }

    public function test_index_filter_iso3_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'USA'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso3' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.iso3'));
    }

    public function test_index_filter_iso3_not_empty(): void
    {
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => null
        ]);

        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'iso3' => 'USA'
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'iso3' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals('USA', $response->json('data.0.iso3'));
    }

    public function test_can_filter_system_countries(): void
    {
        // Создаем системную страну (без cabinet_id)
        Country::factory()->create([
            'cabinet_id' => null
        ]);

        // Создаем пользовательскую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'value' => EntitiesTypeEnum::SYSTEM->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertNull($response->json('data.0.cabinet_id'));
    }

    public function test_can_filter_custom_countries(): void
    {
        // Создаем системную страну (без cabinet_id)
        Country::factory()->create([
            'cabinet_id' => null
        ]);

        // Создаем пользовательскую страну
        Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'value' => EntitiesTypeEnum::CUSTOM->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
    }

    public function test_can_create_country(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'full_name' => 'Российская Федерация',
            'code' => 'RU',
            'iso2' => 'RU',
            'iso3' => 'RUS',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true,
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('countries', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'full_name' => 'Российская Федерация',
            'code' => 'RU',
            'iso2' => 'RU',
            'iso3' => 'RUS',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true,
        ]);
    }

    public function test_can_create_country_with_minimal_required_fields(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('countries', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_cannot_create_country_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/countries', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'employee_id',
                'department_id',
            ]);
    }

    public function test_cannot_create_country_with_invalid_data(): void
    {
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '',
            'iso2' => 'RUS', // должно быть 2 символа
            'iso3' => 'RU', // должно быть 3 символа
            'employee_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean'
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'iso2',
                'iso3',
                'employee_id',
                'department_id',
                'is_common'
            ]);
    }

    public function test_cannot_create_country_in_other_cabinet(): void
    {
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Россия',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertStatus(403);

        $this->assertDatabaseMissing('countries', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Россия',
        ]);
    }

    public function test_cannot_create_country_with_employee_from_other_cabinet(): void
    {
        $otherEmployee = Employee::factory()->create();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'employee_id' => $otherEmployee->id,
            'department_id' => $this->department->id,
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertStatus(403);

        $this->assertDatabaseMissing('countries', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'employee_id' => $otherEmployee->id,
        ]);
    }

    public function test_cannot_create_country_with_department_from_other_cabinet(): void
    {
        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'employee_id' => $this->employee->id,
            'department_id' => $otherDepartment->id,
        ];

        $response = $this->postJson('/api/internal/countries', $data);

        $response->assertNotFound();

        $this->assertDatabaseMissing('countries', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Россия',
            'department_id' => $otherDepartment->id,
        ]);
    }

    public function test_index_cannot_access_records_from_other_cabinet(): void
    {
        // Создаем страны в другом кабинете
        $otherCabinetCountries = Country::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем страны в текущем кабинете
        $currentCabinetCountries = Country::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/countries?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200);

        // Проверяем что в ответе нет стран из другого кабинета
        foreach ($otherCabinetCountries as $country) {
            $this->assertNotContains($country->id, collect($response->json('data'))->pluck('id'));
        }

        // Проверяем что все страны текущего кабинета присутствуют
        foreach ($currentCabinetCountries as $country) {
            $this->assertContains($country->id, collect($response->json('data'))->pluck('id'));
        }
    }

    public function test_can_update_country(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->country(),
            'full_name' => $this->faker->country() . ' Republic',
            'code' => $this->faker->countryCode(),
            'iso2' => strtoupper($this->faker->lexify('??')),
            'iso3' => strtoupper($this->faker->lexify('???')),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('countries', [
            'id' => $country->id,
            'name' => $updateData['name'],
            'full_name' => $updateData['full_name'],
            'code' => $updateData['code'],
            'iso2' => $updateData['iso2'],
            'iso3' => $updateData['iso3'],
            'employee_id' => $updateData['employee_id'],
            'department_id' => $updateData['department_id'],
            'is_common' => $updateData['is_common'],
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_update_country_with_minimal_fields(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->country(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('countries', [
            'id' => $country->id,
            'name' => $updateData['name'],
            'employee_id' => $updateData['employee_id'],
            'department_id' => $updateData['department_id'],
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_cannot_update_country_without_required_fields(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'employee_id', 'department_id']);
    }

    public function test_cannot_update_country_with_invalid_data(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'name' => ['invalid'], // должна быть строка
            'iso2' => 'invalid', // должно быть 2 символа
            'iso3' => 'invalid', // должно быть 3 символа
            'employee_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean'
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'iso2',
                'iso3',
                'employee_id',
                'department_id',
                'is_common'
            ]);
    }

    public function test_cannot_update_country_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCountry = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->country(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$otherCabinetCountry->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_country_with_employee_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->country(),
            'employee_id' => $otherEmployee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_country_with_department_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->country(),
            'employee_id' => $this->employee->id,
            'department_id' => $otherDepartment->id
        ];

        // Act
        $response = $this->putJson("/api/internal/countries/{$country->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_country(): void
    {
        // Act
        $response = $this->putJson("/api/internal/countries/" . $this->faker->uuid(), [
            'name' => $this->faker->country(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_country(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Country',
            'full_name' => 'Test Country Republic',
            'code' => 'TC',
            'iso2' => 'TC',
            'iso3' => 'TCR',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true
        ]);

        // Act
        $response = $this->getJson("/api/internal/countries/{$country->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'id' => $country->id,
                'name' => 'Test Country',
                'full_name' => 'Test Country Republic',
                'code' => 'TC',
                'iso2' => 'TC',
                'iso3' => 'TCR',
                'employee_id' => $this->employee->id,
                'department_id' => $this->department->id,
                'is_common' => true,
                'cabinet_id' => $this->cabinet->id
            ]);
    }

    public function test_cannot_show_country_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCountry = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/countries/{$otherCabinetCountry->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_country(): void
    {
        // Act
        $response = $this->getJson("/api/internal/countries/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_country(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/countries/{$country->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertSoftDeleted('countries', [
            'id' => $country->id
        ]);
    }

    public function test_cannot_delete_country_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCountry = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/countries/{$otherCabinetCountry->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('countries', [
            'id' => $otherCabinetCountry->id
        ]);
    }

    public function test_cannot_delete_non_existent_country(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/countries/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
