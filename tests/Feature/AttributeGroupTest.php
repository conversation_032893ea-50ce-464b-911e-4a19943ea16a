<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\AttributeGroup;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AttributeGroupTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_attribute_groups_list(): void
    {
        // Arrange
        // Создаем группы атрибутов для нашего кабинета
        AttributeGroup::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем группу атрибутов для другого кабинета
        AttributeGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'name',
                        'created_at',
                        'updated_at',
                        'description',
                        'sort_order',
                        'status'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только группы атрибутов нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_attribute_groups_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attributes/groups');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_attribute_groups_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_attribute_groups_from_other_cabinet(): void
    {
        // Arrange
        // Создаем группы атрибутов для другого кабинета
        AttributeGroup::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_attribute_groups_with_pagination(): void
    {
        // Arrange
        AttributeGroup::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_can_get_attribute_groups_with_sorting(): void
    {
        // Arrange
        $groups = [
            'A group' => AttributeGroup::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'A group'
            ]),
            'B group' => AttributeGroup::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'B group'
            ]),
            'C group' => AttributeGroup::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'C group'
            ])
        ];

        // Act - получаем отсортированный по имени список
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);

        $names = collect($response->json('data'))->pluck('name')->values();
        $expectedNames = collect(['C group', 'B group', 'A group']);

        $this->assertEquals($expectedNames, $names);
    }

    public function test_can_get_attribute_groups_with_fields(): void
    {
        AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['name']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'name'
                    ]
                ],
                'meta'
            ]);
    }

    public function test_cannot_get_attribute_groups_with_invalid_fields(): void
    {
        AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid-field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0']);
    }

    public function test_can_create_attribute_group(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => true
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attribute_groups', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'description' => $data['description'],
            'sort_order' => $data['sort_order'],
            'status' => $data['status'],
        ]);
    }

    public function test_can_create_attribute_group_with_minimal_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attribute_groups', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_create_attribute_group_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/attributes/groups', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
            ]);
    }

    public function test_cannot_create_attribute_group_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => str_repeat('a', 101), // превышает максимальную длину
            'sort_order' => 'not-an-integer',
            'status' => 'not-a-boolean'
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes/groups', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'sort_order',
                'status'
            ]);
    }

    public function test_cannot_create_attribute_group_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes/groups', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('attribute_groups', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $data['name']
        ]);
    }

    public function test_can_update_attribute_group(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => true
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/groups/{$attributeGroup->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attribute_groups', array_merge(
            ['id' => $attributeGroup->id],
            $data
        ));
    }

    public function test_can_update_attribute_group_with_minimal_fields(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'old description',
            'sort_order' => 1,
            'status' => true
        ]);

        $data = [
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/groups/{$attributeGroup->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attribute_groups', [
            'id' => $attributeGroup->id,
            'name' => $data['name'],
            'description' => $attributeGroup->description,
            'sort_order' => $attributeGroup->sort_order,
            'status' => $attributeGroup->status
        ]);
    }

    public function test_cannot_update_attribute_group_without_required_fields(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/attributes/groups/{$attributeGroup->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_update_attribute_group_with_invalid_data(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => str_repeat('a', 101), // превышает максимальную длину
            'sort_order' => 'not-an-integer',
            'status' => 'not-a-boolean'
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/groups/{$attributeGroup->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'sort_order',
                'status'
            ]);
    }

    public function test_cannot_update_non_existent_attribute_group(): void
    {
        // Act
        $response = $this->putJson("/api/internal/attributes/groups/" . $this->faker->uuid(), [
            'name' => $this->faker->word()
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_attribute_group_from_other_cabinet(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => $this->faker->word()
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/groups/{$attributeGroup->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('attribute_groups', [
            'id' => $attributeGroup->id,
            'name' => $attributeGroup->name // проверяем что имя не изменилось
        ]);
    }

    public function test_can_show_attribute_group(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Group',
            'description' => 'Test Description',
            'sort_order' => 1,
            'status' => true
        ]);

        // Act
        $response = $this->getJson("/api/internal/attributes/groups/{$attributeGroup->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'name',
                'description',
                'sort_order',
                'status',
                'created_at',
                'updated_at'
            ])
            ->assertJson([
                'id' => $attributeGroup->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Group',
                'description' => 'Test Description',
                'sort_order' => 1,
                'status' => true
            ]);
    }

    public function test_cannot_show_non_existent_attribute_group(): void
    {
        // Act
        $response = $this->getJson("/api/internal/attributes/groups/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_attribute_group_from_other_cabinet(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/attributes/groups/{$attributeGroup->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_attribute_group(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attributes/groups/{$attributeGroup->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('attribute_groups', [
            'id' => $attributeGroup->id
        ]);
    }

    public function test_cannot_delete_non_existent_attribute_group(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/attributes/groups/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_delete_attribute_group_from_other_cabinet(): void
    {
        // Arrange
        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attributes/groups/{$attributeGroup->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('attribute_groups', [
            'id' => $attributeGroup->id
        ]);
    }
}
