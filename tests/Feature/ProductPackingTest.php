<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Product;
use App\Models\Cabinet;
use App\Models\Packing;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Barcode;
use App\Models\MeasurementUnit;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Enums\Api\Internal\BarcodeEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use App\Models\ProductPacking;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductPackingTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_product_packings_list(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_type' => ProductPacking::class
        ]);

        $productPacking = DB::table('product_packing')->insert([
            'id' => $barcode->barcodable_id,
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 10,
            'measurement_unit_quantity_id' => $measurementUnit->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Act
        $response = $this->getJson("/api/internal/products/{$product->id}/packings");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'product_id',
                        'packing_id',
                        'quantity',
                        'measurement_unit_quantity_id',
                    ]
            ]);

        $responseData = $response->json('0');
        $this->assertEquals($product->id, $responseData['product_id']);
        $this->assertEquals($packing->id, $responseData['packing_id']);
        $this->assertEquals(10, $responseData['quantity']);
        $this->assertEquals($measurementUnit->id, $responseData['measurement_unit_quantity_id']);
    }

    public function test_cannot_get_product_packings_list_for_other_cabinet_product(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/products/{$product->id}/packings");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_get_product_packings_list_for_non_existent_product(): void
    {
        // Act
        $response = $this->getJson("/api/internal/products/" . $this->faker->uuid() . "/packings");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_get_empty_product_packings_list(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/products/{$product->id}/packings");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(0);
    }

    public function test_can_update_product_packings(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_type' => 'App\Models\ProductPacking'
        ]);

        $productPacking = DB::table('product_packing')->insert([
            'id' => $barcode->barcodable_id,
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 10,
            'measurement_unit_quantity_id' => $measurementUnit->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $barcodeType = $this->faker->randomElement(BarcodeEnum::class)->value;
        $updateData = [
            'packings' => [
                [
                    'id' => $barcode->barcodable_id,
                    'packing_id' => $packing->id,
                    'quantity' => 20,
                    'measurement_unit_quantity_id' => $measurementUnit->id,
                    'barcode' => '123456789',
                    'barcode_type' => $barcodeType
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}/packings", $updateData);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_packing', [
            'id' => $barcode->barcodable_id,
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 20,
            'measurement_unit_quantity_id' => $measurementUnit->id
        ]);

        $this->assertDatabaseHas('barcodes', [
            'barcodable_id' => $barcode->barcodable_id,
            'barcodable_type' => 'product_packing',
            'value' => '123456789',
            'type' => $barcodeType
        ]);
    }

    public function test_can_update_product_packings_with_multiple_packings(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $packings = Packing::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcodes = Barcode::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_type' => 'App\Models\ProductPacking'
        ]);

        foreach ($barcodes as $barcode) {
            DB::table('product_packing')->insert([
                'id' => $barcode->barcodable_id,
                'product_id' => $product->id,
                'packing_id' => $packings[0]->id,
                'quantity' => 10,
                'measurement_unit_quantity_id' => $measurementUnit->id,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        $updateData = [
            'packings' => [
                [
                    'id' => $barcodes[0]->barcodable_id,
                    'packing_id' => $packings[0]->id,
                    'quantity' => 20,
                    'measurement_unit_quantity_id' => $measurementUnit->id,
                    'barcode' => '123456789',
                    'barcode_type' => BarcodeEnum::EAN13->value
                ],
                [
                    'id' => $barcodes[1]->barcodable_id,
                    'packing_id' => $packings[1]->id,
                    'quantity' => 30,
                    'measurement_unit_quantity_id' => $measurementUnit->id,
                    'barcode' => '987654321',
                    'barcode_type' => BarcodeEnum::EAN8->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}/packings", $updateData);

        // Assert
        $response->assertStatus(204);

        foreach ($updateData['packings'] as $packing) {
            $this->assertDatabaseHas('product_packing', [
                'id' => $packing['id'],
                'product_id' => $product->id,
                'packing_id' => $packing['packing_id'],
                'quantity' => $packing['quantity'],
                'measurement_unit_quantity_id' => $packing['measurement_unit_quantity_id']
            ]);

            $this->assertDatabaseHas('barcodes', [
                'barcodable_id' => $packing['id'],
                'barcodable_type' => 'product_packing',
                'value' => $packing['barcode'],
                'type' => $packing['barcode_type']
            ]);
        }
    }

    public function test_cannot_update_product_packings_for_other_cabinet_product(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $packing = Packing::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'barcodable_type' => 'App\Models\ProductPacking'
        ]);

        $productPacking = DB::table('product_packing')->insert([
            'id' => $barcode->barcodable_id,
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 10,
            'measurement_unit_quantity_id' => $measurementUnit->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $updateData = [
            'packings' => [
                [
                    'id' => $barcode->barcodable_id,
                    'packing_id' => $packing->id,
                    'quantity' => 20,
                    'measurement_unit_quantity_id' => $measurementUnit->id,
                    'barcode' => '123456789',
                    'barcode_type' => BarcodeEnum::EAN13->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}/packings", $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_product_packings_for_non_existent_product(): void
    {
        // Arrange
        $updateData = [
            'packings' => [
                [
                    'packing_id' => $this->faker->uuid(),
                    'quantity' => 20,
                    'measurement_unit_quantity_id' => $this->faker->uuid(),
                    'barcode' => '123456789',
                    'barcode_type' => BarcodeEnum::EAN13->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/" . $this->faker->uuid() . "/packings", $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_product_packings_with_minimal_required_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_type' => 'App\Models\ProductPacking'
        ]);

        $productPacking = DB::table('product_packing')->insert([
            'id' => $barcode->barcodable_id,
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 10,
            'measurement_unit_quantity_id' => $measurementUnit->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $updateData = [
            'packings' => [
                [
                    'packing_id' => $packing->id,
                    'measurement_unit_quantity_id' => $measurementUnit->id
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}/packings", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('product_packing', [
            'product_id' => $product->id,
            'packing_id' => $packing->id,
            'quantity' => 1, // Значение по умолчанию
            'measurement_unit_quantity_id' => $measurementUnit->id
        ]);
    }

    public function test_cannot_update_product_packings_with_invalid_data(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'packings' => [
                [
                    'packing_id' => 'invalid-uuid',
                    'quantity' => 'invalid',
                    'measurement_unit_quantity_id' => 'invalid-uuid',
                    'barcode_type' => 'invalid-type'
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}/packings", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'packings.0.packing_id',
                'packings.0.quantity',
                'packings.0.measurement_unit_quantity_id',
                'packings.0.barcode_type'
            ]);
    }
}
