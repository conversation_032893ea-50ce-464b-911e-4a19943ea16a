<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\GlobalCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class GlobalCurrencyTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_global_currencies_list(): void
    {
        // Создаем тестовые валюты
        GlobalCurrency::factory()->count(3)->create();

        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'num_code',
                        'char_code',
                        'short_name',
                        'external_id',
                        'name',
                        'value',
                        'currency_date',
                        'is_default',
                        'pluralization',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили все 3 валюты
        $response->assertJsonCount(3, 'data');
    }

    public function test_index_with_pagination(): void
    {
        // Создаем 20 валют
        GlobalCurrency::factory()->count(20)->create();

        // Запрашиваем первую страницу
        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'page' => 1,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJson([
                'meta' => [
                    'current_page' => 1,
                    'per_page' => 10,
                    'last_page' => 2,
                    'total' => 20
                ]
            ]);

        // Запрашиваем вторую страницу
        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'page' => 2,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJson([
                'meta' => [
                    'current_page' => 2,
                    'per_page' => 10,
                    'last_page' => 2,
                    'total' => 20
                ]
            ]);
    }

    public function test_index_with_sorting(): void
    {
        // Создаем валюты с разными кодами
        GlobalCurrency::factory()->create(['char_code' => 'USD']);
        GlobalCurrency::factory()->create(['char_code' => 'EUR']);
        GlobalCurrency::factory()->create(['char_code' => 'AUD']);

        // Сортировка по возрастанию
        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'sortField' => 'char_code',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(200);
        $codes = collect($response->json('data'))->pluck('char_code')->values();
        $this->assertEquals(['AUD', 'EUR', 'USD'], $codes->all());

        // Сортировка по убыванию
        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'sortField' => 'char_code',
            'sortDirection' => 'desc'
        ]));

        $response->assertStatus(200);
        $codes = collect($response->json('data'))->pluck('char_code')->values();
        $this->assertEquals(['USD', 'EUR', 'AUD'], $codes->all());
    }

    public function test_index_with_selected_fields(): void
    {
        GlobalCurrency::factory()->create();

        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'fields' => ['id', 'char_code']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'char_code',
                    ]
                ]
            ]);

        // Проверяем что другие поля не включены в ответ
        $this->assertArrayNotHasKey('num_code', $response->json('data.0'));
        $this->assertArrayNotHasKey('short_name', $response->json('data.0'));
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/currencies?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'fields' => ['invalid_field'], // Несуществующее поле
            'sortField' => 'invalid_field' // Несуществующее поле для сортировки
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'page',
                'per_page',
                'sortDirection',
                'fields.0',
                'sortField'
            ]);
    }

    public function test_can_show_global_currency(): void
    {
        $currency = GlobalCurrency::factory()->create();

        $response = $this->getJson("/api/internal/currencies/{$currency->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'num_code',
                'char_code',
                'short_name',
                'external_id',
                'name',
                'value',
                'is_default',
                'pluralization',
            ]);

        // Проверяем что получили правильную валюту
        $this->assertEquals($currency->id, $response->json('id'));
        $this->assertEquals($currency->code, $response->json('code'));
    }

    public function test_cannot_show_non_existent_currency(): void
    {
        $response = $this->getJson("/api/internal/currencies/" . $this->faker->uuid());

        $response->assertStatus(404);
    }
}
