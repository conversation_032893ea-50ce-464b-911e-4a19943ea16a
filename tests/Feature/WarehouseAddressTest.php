<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\Country;
use App\Models\WarehouseAddress;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseAddressTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_warehouse_addresses_list(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 адреса для нашего кабинета
        WarehouseAddress::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        // Создаем адрес для другого кабинета
        WarehouseAddress::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'country_id',
                        'postcode',
                        'region',
                        'city',
                        'street',
                        'house',
                        'office',
                        'other',
                        'comment'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_warehouse_addresses(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем адреса для другого кабинета
        WarehouseAddress::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_returns_empty_collection_when_no_addresses(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_index_with_selected_fields(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/addresses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'city', 'street']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'city',
                        'street'
                    ]
                ]
            ]);

        // Проверяем что в ответе только запрошенные поля
        $responseData = $response->json('data.0');
        $this->assertCount(3, array_keys($responseData));
    }

    public function test_can_create_warehouse_address(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id,
            'postcode' => $this->faker->postcode(),
            'region' => $this->faker->country,
            'city' => $this->faker->city(),
            'street' => $this->faker->streetName(),
            'house' => $this->faker->buildingNumber(),
            'office' => $this->faker->address,
            'other' => $this->faker->text(50),
            'comment' => $this->faker->text(100)
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'country_id' => $data['country_id'],
            'postcode' => $data['postcode'],
            'region' => $data['region'],
            'city' => $data['city'],
            'street' => $data['street'],
            'house' => $data['house'],
            'office' => $data['office'],
            'other' => $data['other'],
            'comment' => $data['comment']
        ]);
    }

    public function test_can_create_warehouse_address_with_minimal_data(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'country_id' => $data['country_id']
        ]);
    }

    public function test_cannot_create_warehouse_address_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'country_id'
            ]);
    }

    public function test_cannot_create_warehouse_address_with_invalid_data(): void
    {
        // Arrange
        $invalidData = [
            'cabinet_id' => 'not-a-uuid',
            'country_id' => 'invalid-uuid',
            'postcode' => ['invalid-type'],
            'region' => ['invalid-type'],
            'city' => ['invalid-type'],
            'street' => ['invalid-type'],
            'house' => ['invalid-type'],
            'office' => ['invalid-type'],
            'other' => ['invalid-type'],
            'comment' => ['invalid-type']
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'country_id',
                'postcode',
                'region',
                'city',
                'street',
                'house',
                'office',
                'other',
                'comment'
            ]);
    }

    public function test_cannot_create_warehouse_address_in_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id,
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('warehouse_addresses', [
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);
    }

    public function test_cannot_create_warehouse_address_with_non_existent_country(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $this->faker->uuid(),
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_warehouse_address_with_country_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCountry = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $otherCabinetCountry->id,
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/addresses', $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouse_addresses', [
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $otherCabinetCountry->id
        ]);
    }

    public function test_can_update_warehouse_address(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        $newCountry = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'country_id' => $newCountry->id,
            'postcode' => $this->faker->postcode(),
            'region' => $this->faker->country,
            'city' => $this->faker->city(),
            'street' => $this->faker->streetName(),
            'house' => $this->faker->buildingNumber(),
            'office' => $this->faker->address,
            'other' => $this->faker->text(50),
            'comment' => $this->faker->text(100)
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_addresses', array_merge(
            ['id' => $address->id],
            $updateData
        ));
    }

    public function test_can_update_warehouse_address_partial(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id,
            'city' => 'Старый город',
            'street' => 'Старая улица'
        ]);

        $updateData = [
            'country_id' => $country->id,
            'city' => 'Новый город'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $address->id,
            'city' => 'Новый город',
        ]);
    }

    public function test_cannot_update_warehouse_address_without_required_fields(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['country_id']);
    }

    public function test_cannot_update_warehouse_address_with_invalid_data(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        $invalidData = [
            'country_id' => 'invalid-uuid',
            'postcode' => ['invalid-type'],
            'region' => ['invalid-type'],
            'city' => ['invalid-type'],
            'street' => ['invalid-type'],
            'house' => ['invalid-type'],
            'office' => ['invalid-type'],
            'other' => ['invalid-type'],
            'comment' => ['invalid-type']
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'country_id',
                'postcode',
                'region',
                'city',
                'street',
                'house',
                'office',
                'other',
                'comment'
            ]);
    }

    public function test_cannot_update_non_existent_warehouse_address(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'country_id' => $country->id,
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/" . $this->faker->uuid(), $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_warehouse_address_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);

        $updateData = [
            'country_id' => $country->id,
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $address->id,
            'city' => $address->city
        ]);
    }

    public function test_cannot_update_warehouse_address_with_country_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        $otherCabinetCountry = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'country_id' => $otherCabinetCountry->id,
            'city' => $this->faker->city()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/addresses/{$address->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $address->id,
            'country_id' => $country->id
        ]);
    }

    public function test_can_show_warehouse_address(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id,
            'postcode' => $this->faker->postcode(),
            'region' => $this->faker->country,
            'city' => $this->faker->city(),
            'street' => $this->faker->streetName(),
            'house' => $this->faker->buildingNumber(),
            'office' => $this->faker->address,
            'other' => $this->faker->text(50),
            'comment' => $this->faker->text(100)
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/addresses/{$address->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'country_id',
                'postcode',
                'region',
                'city',
                'street',
                'house',
                'office',
                'other',
                'comment'
            ])
            ->assertJson([
                'id' => $address->id,
                'cabinet_id' => $address->cabinet_id,
                'country_id' => $address->country_id,
                'postcode' => $address->postcode,
                'region' => $address->region,
                'city' => $address->city,
                'street' => $address->street,
                'house' => $address->house,
                'office' => $address->office,
                'other' => $address->other,
                'comment' => $address->comment
            ]);
    }

    public function test_cannot_show_non_existent_warehouse_address(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/addresses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_warehouse_address_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/addresses/{$address->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse_address(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/addresses/{$address->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_addresses', [
            'id' => $address->id
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse_address(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/addresses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_delete_warehouse_address_from_other_cabinet(): void
    {
        // Arrange
        $country = Country::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $address = WarehouseAddress::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'country_id' => $country->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/addresses/{$address->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('warehouse_addresses', [
            'id' => $address->id
        ]);
    }
}
