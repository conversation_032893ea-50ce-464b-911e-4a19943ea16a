<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\Role;
use App\Models\Permission;
use App\Enums\Api\Internal\PermissionScopeEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RoleTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->otherCabinet = Cabinet::factory()->create();

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_roles_list(): void
    {
        // Arrange
        $systemRole = Role::factory()->create([
            'is_system' => true,
            'cabinet_id' => null
        ]);

        $cabinetRole = Role::factory()->create([
            'is_system' => false,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/roles?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'name',
                    'cabinet_id',
                    'is_system',
                ],
                ],
                'meta'
            ]);

        $data = $response->json('data');
        $this->assertCount(2, $data); // Системная роль + роль кабинета
        $this->assertTrue(collect($data)->contains('id', $systemRole->id));
        $this->assertTrue(collect($data)->contains('id', $cabinetRole->id));
    }

    public function test_cannot_get_roles_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/roles');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_roles_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/roles?' . http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_roles_from_other_cabinet(): void
    {
        // Arrange
        // Создаем роли в нашем кабинете
        Role::factory()->create([
            'is_system' => false,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Our Cabinet Role'
        ]);

        // Создаем роли в другом кабинете
        Role::factory()->create([
            'is_system' => false,
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Other Cabinet Role'
        ]);

        // Act
        $response = $this->getJson('/api/internal/roles?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_roles_with_system_roles_from_any_cabinet(): void
    {
        // Arrange
        // Создаем системные роли
        $systemRole1 = Role::factory()->create([
            'is_system' => true,
            'cabinet_id' => null,
            'name' => 'System Role 1'
        ]);

        $systemRole2 = Role::factory()->create([
            'is_system' => true,
            'cabinet_id' => null,
            'name' => 'System Role 2'
        ]);

        // Создаем пользовательскую роль в нашем кабинете
        $cabinetRole = Role::factory()->create([
            'is_system' => false,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Our Cabinet Role'
        ]);

        // Act
        $response = $this->getJson('/api/internal/roles?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'name',
                        'cabinet_id',
                        'is_system',
                    ],
                ],
                'meta'
            ]);

        $data = $response->json('data');
        $this->assertCount(3, $data); // Две системные роли + одна роль кабинета
        $this->assertTrue(collect($data)->contains('id', $systemRole1->id));
        $this->assertTrue(collect($data)->contains('id', $systemRole2->id));
        $this->assertTrue(collect($data)->contains('id', $cabinetRole->id));
    }

    public function test_can_store_role(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('roles', [
            'id' => $response->json('id'),
            'name' => 'Test Role',
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);

        $this->assertDatabaseHas('role_permissions', [
            'role_id' => $response->json('id'),
            'permission_id' => $permission->id,
            'scope' => PermissionScopeEnum::SCOPE_ALL->value
        ]);
    }

    public function test_cannot_store_role_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/roles', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
            ]);
    }

    public function test_cannot_store_role_with_invalid_permission(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => 'invalid-uuid',
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions.0.id']);
    }

    public function test_cannot_store_role_with_invalid_scope(): void
    {
        // Arrange
        $permission = Permission::factory()->create();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => 'invalid-scope'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions.0.scope']);
    }

    public function test_cannot_store_role_in_other_cabinet(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_store_role_with_duplicate_name_in_same_cabinet(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);

        // Создаем первую роль
        Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role'
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(500)
            ->assertJsonFragment(['error' => 'An error occurred while processing your request. Role with this name already exists']);
    }

    public function test_can_store_role_with_same_name_in_different_cabinets(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);

        // Создаем роль в другом кабинете
        Role::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Role'
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/roles', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('roles', [
            'id' => $response->json('id'),
            'name' => 'Test Role',
            'cabinet_id' => $this->cabinet->id,
            'is_system' => false
        ]);
    }

    public function test_can_update_role(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Role'
        ]);

        $data = [
            'name' => 'Updated Role',
            'permissions' => [
                [
                    'permission_id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id,
            'name' => 'Updated Role'
        ]);

        $this->assertDatabaseHas('role_permissions', [
            'role_id' => $role->id,
            'permission_id' => $permission->id,
            'scope' => PermissionScopeEnum::SCOPE_ALL->value
        ]);
    }

    public function test_cannot_update_role_without_required_fields(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_update_role_with_invalid_permission(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => 'Updated Role',
            'permissions' => [
                [
                    'id' => $this->faker->uuid(),
                    'permission_id' => 'invalid-uuid',
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions.0.permission_id']);
    }

    public function test_cannot_update_role_with_invalid_scope(): void
    {
        // Arrange
        $permission = Permission::factory()->create();
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => 'Updated Role',
            'permissions' => [
                [
                    'id' => $this->faker->uuid(),
                    'permission_id' => $permission->id,
                    'scope' => 'invalid-scope'
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions.0.scope']);
    }

    public function test_cannot_update_role_from_other_cabinet(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);
        $role = Role::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Other Cabinet Role'
        ]);

        $data = [
            'name' => 'Updated Role',
            'permissions' => [
                [
                    'id' => $this->faker->uuid(),
                    'permission_id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id,
            'name' => 'Other Cabinet Role'
        ]);
    }

    public function test_cannot_update_role_with_duplicate_name_in_same_cabinet(): void
    {
        // Arrange
        $existingRole = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Existing Role'
        ]);

        $roleToUpdate = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Role To Update'
        ]);

        $data = [
            'name' => 'Existing Role'
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$roleToUpdate->id}", $data);

        // Assert
        $response->assertStatus(500);
    }

    public function test_can_update_role_with_same_name_in_different_cabinets(): void
    {
        // Arrange
        $otherRole = Role::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Common Name'
        ]);

        $roleToUpdate = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Name'
        ]);

        $data = [
            'name' => 'Common Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$roleToUpdate->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('roles', [
            'id' => $roleToUpdate->id,
            'name' => 'Common Name'
        ]);
    }

    public function test_can_update_role_permissions(): void
    {
        // Arrange
        $oldPermission = Permission::factory()->create(['require_scope' => true]);
        $newPermission = Permission::factory()->create(['require_scope' => true]);

        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем начальное разрешение
        $this->postJson("/api/internal/roles", [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Role',
            'permissions' => [
                [
                    'id' => $oldPermission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ]);

        $data = [
            'name' => 'Updated Role',
            'permissions' => [
                [
                    'id' => $this->faker->uuid(),
                    'permission_id' => $newPermission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/roles/{$role->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('role_permissions', [
            'role_id' => $role->id,
            'permission_id' => $oldPermission->id
        ]);

        $this->assertDatabaseHas('role_permissions', [
            'role_id' => $role->id,
            'permission_id' => $newPermission->id,
            'scope' => PermissionScopeEnum::SCOPE_ALL->value
        ]);
    }

    public function test_can_show_role(): void
    {
        // Arrange
        $permission = Permission::factory()->create(['require_scope' => true]);
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role'
        ]);

        // Создаем разрешение для роли
        $this->postJson("/api/internal/roles", [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role',
            'permissions' => [
                [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/roles/{$role->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'name',
                'cabinet_id',
                'is_system',
                'permissions' => [
                    '*' => [
                        'id',
                        'role_id',
                        'permission_id',
                        'scope'
                    ]
                ]
            ]);

        $this->assertEquals($role->id, $response->json('id'));
        $this->assertEquals('Test Role', $response->json('name'));
        $this->assertEquals($this->cabinet->id, $response->json('cabinet_id'));
        $this->assertFalse($response->json('is_system'));
    }

    public function test_cannot_show_role_from_other_cabinet(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Other Cabinet Role'
        ]);

        // Act
        $response = $this->getJson("/api/internal/roles/{$role->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_role(): void
    {
        // Act
        $response = $this->getJson("/api/internal/roles/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_role(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Role'
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/roles/{$role->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('roles', [
            'id' => $role->id
        ]);
    }

    public function test_cannot_delete_system_role(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => null,
            'is_system' => true,
            'name' => 'System Role'
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/roles/{$role->id}");

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id
        ]);
    }

    public function test_cannot_delete_role_from_other_cabinet(): void
    {
        // Arrange
        $role = Role::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Other Cabinet Role'
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/roles/{$role->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id
        ]);
    }

    public function test_cannot_delete_non_existent_role(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/roles/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }
}
