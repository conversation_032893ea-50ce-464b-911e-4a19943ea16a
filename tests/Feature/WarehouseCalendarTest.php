<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\WarehouseWorkSchedule;
use App\Models\WarehouseCalendar;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseCalendarTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_show_warehouse_calendar(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем несколько записей календаря
        $calendarItems = WarehouseCalendar::factory()->count(3)->create([
            'schedule_id' => $schedule->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouse-calendars/{$schedule->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'schedule_id',
                    'date',
                    'is_working_day',
                    'is_holiday',
                    'template_reference',
                    'created_at',
                    'updated_at'
                ]
            ]);

        // Проверяем что получили все записи для данного расписания
        $response->assertJsonCount(3);
        foreach ($response->json() as $item) {
            $this->assertEquals($schedule->id, $item['schedule_id']);
        }
    }

    public function test_cannot_show_warehouse_calendar_from_other_cabinet(): void
    {
        // Arrange
        $otherSchedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        WarehouseCalendar::factory()->count(2)->create([
            'schedule_id' => $otherSchedule->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouse-calendars/{$otherSchedule->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_warehouse_calendar(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouse-calendars/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_show_warehouse_calendar_returns_empty_collection_when_no_dates(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouse-calendars/{$schedule->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(0);
    }

    public function test_show_warehouse_calendar_with_specific_date_range(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем записи календаря с разными датами
        WarehouseCalendar::factory()->create([
            'schedule_id' => $schedule->id,
            'date' => now()->subDays(2),
            'is_working_day' => true
        ]);

        WarehouseCalendar::factory()->create([
            'schedule_id' => $schedule->id,
            'date' => now(),
            'is_working_day' => false
        ]);

        WarehouseCalendar::factory()->create([
            'schedule_id' => $schedule->id,
            'date' => now()->addDays(2),
            'is_working_day' => true
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouse-calendars/{$schedule->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3);

        $dates = collect($response->json())->pluck('date')->sort()->values();
        $expectedDates = collect([
            now()->subDays(2)->format('Y-m-d'),
            now()->format('Y-m-d'),
            now()->addDays(2)->format('Y-m-d')
        ])->sort()->values();

        $this->assertEquals($expectedDates, $dates);
    }
}
