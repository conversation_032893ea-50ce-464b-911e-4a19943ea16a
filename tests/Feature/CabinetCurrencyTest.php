<?php

namespace Tests\Feature;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetCurrency;
use App\Models\GlobalCurrency;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Testing\TestResponse;
use App\Enums\Api\Internal\CurrencyTypeEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CabinetCurrencyTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'use_bin' => false
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_cabinet_currencies(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();

        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'currency_id' => $globalCurrency->id,
            'is_other' => false,
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'archived_at',
                        'currency_id',
                        'cabinet_id',
                        'is_accouting',
                        'external_id',
                        'num_code',
                        'char_code',
                        'short_name',
                        'name',
                        'type',
                        'markup',
                        'nominal',
                        'value',
                        'is_reverse',
                        'pluralization',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'is_other',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals($globalCurrency->char_code, $response->json('data.0.char_code'));
        $this->assertEquals($globalCurrency->short_name, $response->json('data.0.short_name'));
    }

    public function test_can_get_cabinet_currencies_with_custom_values(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'is_other' => true,
            'char_code' => 'CUST',
            'short_name' => 'Custom Currency',
            'value' => '123.450000',
        ];

        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create($data);

        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonFragment([
                'char_code' => $data['char_code'],
                'short_name' => $data['short_name'],
                'value' => $data['value'],
            ]);
    }

    public function test_cannot_get_cabinet_currencies_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_can_sort_cabinet_currencies(): void
    {
        // Arrange
        $currency1 = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'char_code' => 'AAA',
            'is_other' => true,
        ]);
        $currency2 = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'char_code' => 'ZZZ',
            'is_other' => true,
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'char_code',
            'sortDirection' => 'asc',
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'data' => [
                [
                    'cabinet_id' => $this->cabinet->id
                ]
            ]
        ]);

        $charCodes = collect($response->json('data'))->pluck('char_code')->values();
        $this->assertEquals(collect(['AAA', 'ZZZ']), $charCodes);
    }

    public function test_can_filter_cabinet_currencies_by_char_code(): void
    {
        // Arrange
        $currency1 = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'num_code' => 'USD',
            'is_other' => true,
        ]);

        $this->createTestCurrencies();

        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'num_code' => [
                    'value' => 'USD'
                ],
            ],
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    [
                        'num_code' => 'USD',
                        'cabinet_id' => $this->cabinet->id
                    ]
                ]
            ]);
    }

    public function test_can_filter_cabinet_currencies_by_is_common(): void
    {
        // Arrange
        $currency1 = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true,
            'is_other' => true,
        ]);

        $this->createTestCurrencies();


        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ],
            ],
        ]));

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    [
                        'is_common' => true,
                        'cabinet_id' => $this->cabinet->id
                    ]
                ]
            ]);

        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
            ]);
    }

    public function test_index_filter_updated_at(): void
    {
        // Arrange
        $currency = CabinetCurrency::factory()->create(
            [
                'cabinet_id' => $this->cabinet->id,
                'updated_at' => '01.01.2024 00:10'
                ]
        );

        $this->createTestCurrencies();

        // Act
        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'updated_at' => [
                        'from' => '01.01.2024 00:00',
                        'to' => '01.01.2024 23:59'
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertJsonStructureSnapshot($response);
        foreach ($response->json('data') as $data) {
            $this->assertTrue(
                $data['updated_at'] >= Carbon::parse('01.01.2024 00:00')->toDateTimeString()
                &&
                $data['updated_at'] <= Carbon::parse('01.01.2024 23:59')->toDateTimeString()
            );
        }
    }

    public function test_index_filter_name_in(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'TEST_NAME',
            'is_other' => true
        ];
        $currency = CabinetCurrency::factory()->create($data);
        $this->createTestCurrencies();

        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'name' => [
                        'value' => $data['name'],
                        'condition' => 'IN'
                    ]
                ]
            ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $response->assertJson([
            'data' => [
                [
                    'name' => $data['name']
                ]
            ]
        ]);
    }

    public function test_index_filter_employee_owners_in(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'is_other' => true
        ];
        $currency = CabinetCurrency::factory()->create($data);
        $this->createTestCurrencies();

        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'employee_owners' => [
                        'value' => [$data['employee_id']],
                        'condition' => 'IN'
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $this->assertJsonStructureSnapshot($response);
        $response->assertJson([
            'data' => [
                [
                    'employee_id' => $data['employee_id']
                ]
            ]
        ]);
    }

    public function test_index_filter_department_owners_in(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'is_other' => true
        ];
        $currency = CabinetCurrency::factory()->create($data);
        $this->createTestCurrencies();

        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'department_owners' => [
                        'value' => [$data['department_id']],
                        'condition' => 'IN'
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $this->assertJsonStructureSnapshot($response);
        $response->assertJson([
            'data' => [
                [
                    'department_id' => $data['department_id']
                ]
            ]
        ]);
    }

    public function test_index_filter_is_common(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true,
            'is_other' => true
        ];
        $currency = CabinetCurrency::factory()->create($data);
        $this->createTestCurrencies();

        $response = $this->getJson('/api/internal/cabinet-currencies?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'is_common' => [
                        'value' => true
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $this->assertJsonStructureSnapshot($response);
        $response->assertJson([
            'data' => [
                [
                    'is_common' => true
                ]
            ]
        ]);
    }

    public function test_can_store_cabinet_currency(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_common' => true,
            'currency_id' => $globalCurrency->id,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'name' => 'US Dollar',
            'type' => CurrencyTypeEnum::AUTO->value,
            'markup' => 0.0,
            'nominal' => 1,
            'value' => 1.0,
            'is_reverse' => false,
            'pluralization' => [
                ['gender' => 'm', 'single' => 'dollar', 'two' => 'dollars', 'five' => 'dollars']
            ],
            'is_other' => false,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_currencies', [
            'cabinet_id' => $data['cabinet_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
            'currency_id' => $data['currency_id'],
            'num_code' => $data['num_code'],
            'char_code' => $data['char_code'],
            'short_name' => $data['short_name'],
            'name' => $data['name'],
            'type' => $data['type'],
            'markup' => $data['markup'],
            'nominal' => $data['nominal'],
            'value' => $data['value'],
            'is_reverse' => $data['is_reverse'],
            'is_other' => $data['is_other'],
        ]);
    }

    public function test_store_cabinet_currency_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'num_code',
                'char_code',
                'short_name',
                'type',
                'nominal',
                'value',
            ]);
    }

    public function test_store_cabinet_currency_requires_currency_id_for_auto_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::AUTO->value,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'nominal' => 1,
            'value' => 1.0,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['currency_id']);
    }

    public function test_store_cabinet_currency_validates_nominal_and_value_for_non_auto_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'currency_id' => null,
            'nominal' => null,
            'value' => null,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['nominal', 'value']);
    }

    public function test_store_cabinet_currency_validates_pluralization(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'nominal' => 1,
            'value' => 1.0,
            'pluralization' => [
                ['gender' => 'x', 'single' => 'dollar', 'two' => 'dollars', 'five' => 'dollars']
            ],
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['pluralization.0.gender']);
    }

    public function test_cannot_store_cabinet_currency_for_other_cabinet(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_common' => true,
            'currency_id' => $globalCurrency->id,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'name' => 'US Dollar',
            'type' => CurrencyTypeEnum::AUTO->value,
            'markup' => 0.0,
            'nominal' => 1,
            'value' => 1.0,
            'is_reverse' => false,
            'pluralization' => [
                ['gender' => 'm', 'single' => 'dollar', 'two' => 'dollars', 'five' => 'dollars']
            ],
            'is_other' => false,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_store_cabinet_currency_with_minimal_required_fields(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'num_code' => '840',
            'char_code' => 'USD',
            'short_name' => 'Dollar',
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'nominal' => 1,
            'value' => 1.0,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_currencies', [
            'cabinet_id' => $data['cabinet_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
            'num_code' => $data['num_code'],
            'char_code' => $data['char_code'],
            'short_name' => $data['short_name'],
            'type' => $data['type'],
            'nominal' => $data['nominal'],
            'value' => $data['value'],
        ]);
    }

    public function test_can_update_cabinet_currency(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'currency_id' => $globalCurrency->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_common' => true,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'name' => 'Euro',
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'markup' => 0.0,
            'nominal' => 1,
            'value' => 1.0,
            'is_reverse' => false,
            'pluralization' => [
                ['gender' => 'm', 'single' => 'euro', 'two' => 'euros', 'five' => 'euros']
            ],
            'is_other' => false,
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_currencies', [
            'id' => $cabinetCurrency->id,
            'department_id' => $updateData['department_id'],
            'employee_id' => $updateData['employee_id'],
            'num_code' => $updateData['num_code'],
            'char_code' => $updateData['char_code'],
            'short_name' => $updateData['short_name'],
            'name' => $updateData['name'],
            'type' => $updateData['type'],
            'markup' => $updateData['markup'],
            'nominal' => $updateData['nominal'],
            'value' => $updateData['value'],
            'is_reverse' => $updateData['is_reverse'],
            'is_other' => $updateData['is_other'],
        ]);
    }

    public function test_update_cabinet_currency_validation_errors(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'department_id',
                'employee_id',
                'num_code',
                'char_code',
                'short_name',
                'type',
                'nominal',
                'value',
            ]);
    }

    public function test_update_cabinet_currency_requires_currency_id_for_auto_type(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::AUTO->value,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'nominal' => 1,
            'value' => 1.0,
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['currency_id']);
    }

    public function test_update_cabinet_currency_validates_nominal_and_value_for_non_auto_type(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'currency_id' => null,
            'nominal' => null,
            'value' => null,
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['nominal', 'value']);
    }

    public function test_update_cabinet_currency_validates_pluralization(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'nominal' => 1,
            'value' => 1.0,
            'pluralization' => [
                ['gender' => 'x', 'single' => 'euro', 'two' => 'euros', 'five' => 'euros']
            ],
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['pluralization.0.gender']);
    }

    public function test_cannot_update_cabinet_currency_for_other_cabinet(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'nominal' => 1,
            'value' => 1.0,
            'name' => 'test'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_cabinet_currency_with_minimal_required_fields(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'num_code' => '978',
            'char_code' => 'EUR',
            'short_name' => 'Euro',
            'name' => 'name',
            'type' => CurrencyTypeEnum::MANUALLY->value,
            'nominal' => 1,
            'value' => 1.0,
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_currencies', [
            'id' => $cabinetCurrency->id,
            'department_id' => $updateData['department_id'],
            'employee_id' => $updateData['employee_id'],
            'num_code' => $updateData['num_code'],
            'char_code' => $updateData['char_code'],
            'short_name' => $updateData['short_name'],
            'type' => $updateData['type'],
            'nominal' => $updateData['nominal'],
            'value' => $updateData['value'],
        ]);
    }

    public function test_can_show_cabinet_currency(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'currency_id',
                'cabinet_id',
                'num_code',
                'char_code',
                'short_name',
                'name',
                'type',
                'markup',
                'nominal',
                'value',
                'is_reverse',
                'pluralization',
                'employee_id',
                'department_id',
                'is_common',
                'is_other',
            ]);

        $this->assertEquals($cabinetCurrency->id, $response->json('id'));
    }

    public function test_cannot_show_cabinet_currency_from_other_cabinet(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_cabinet_currency(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinet-currencies/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_cabinet_currency(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('cabinet_currencies', [
            'id' => $cabinetCurrency->id
        ]);
    }

    public function test_cannot_delete_cabinet_currency_from_other_cabinet(): void
    {
        // Arrange
        $cabinetCurrency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinet-currencies/{$cabinetCurrency->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('cabinet_currencies', [
            'id' => $cabinetCurrency->id
        ]);
    }

    public function test_cannot_delete_non_existent_cabinet_currency(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/cabinet-currencies/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_bulk_delete_cabinet_currencies(): void
    {
        // Arrange
        $currencies = CabinetCurrency::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $ids = $currencies->pluck('id')->toArray();

        // Act
        $response = $this->deleteJson('/api/internal/cabinet-currencies/bulk-delete', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $ids,
        ]);

        // Assert
        $response->assertStatus(204);

        foreach ($ids as $id) {
            $this->assertDatabaseMissing('cabinet_currencies', ['id' => $id]);
        }
    }

    public function test_cannot_bulk_delete_cabinet_currencies_from_other_cabinet(): void
    {
        // Arrange
        $currencies = CabinetCurrency::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $ids = $currencies->pluck('id')->toArray();

        // Act
        $response = $this->deleteJson('/api/internal/cabinet-currencies/bulk-delete', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $ids,
        ]);

        // Assert
        $response->assertStatus(404);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('cabinet_currencies', ['id' => $id]);
        }
    }

    public function test_cannot_bulk_delete_with_empty_ids(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/cabinet-currencies/bulk-delete', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [],
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_can_archive_cabinet_currencies(): void
    {
        // Arrange
        $currencies = CabinetCurrency::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $ids = $currencies->pluck('id')->toArray();

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies/archive', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $ids,
        ]);

        // Assert
        $response->assertStatus(204);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('cabinet_currencies', [
                'id' => $id,
                'archived_at' => now(),
            ]);
        }
    }

    public function test_can_unarchive_cabinet_currencies(): void
    {
        // Arrange
        $currencies = CabinetCurrency::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => now(),
        ]);

        $ids = $currencies->pluck('id')->toArray();

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies/unarchive', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $ids,
        ]);

        // Assert
        $response->assertStatus(204);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('cabinet_currencies', [
                'id' => $id,
                'archived_at' => null,
            ]);
        }
    }

    public function test_can_set_accouting_for_cabinet_currency(): void
    {
        // Arrange
        $globalCurrency = GlobalCurrency::factory()->create();
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'global_currency_id' => $globalCurrency->id,
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies/set-accouting', $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_currencies', [
            'cabinet_id' => $data['cabinet_id'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id'],
            'currency_id' => $data['global_currency_id'],
            'is_accouting' => true,
        ]);
    }

    public function test_set_accouting_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/cabinet-currencies/set-accouting', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'global_currency_id',
            ]);
    }

    private function assertJsonStructureSnapshot(TestResponse $response): void
    {
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'archived_at',
                    'currency_id',
                    'cabinet_id',
                    'is_accouting',
                    'external_id',
                    'num_code',
                    'char_code',
                    'short_name',
                    'name',
                    'type',
                    'markup',
                    'nominal',
                    'value',
                    'is_reverse',
                    'pluralization',
                    'employee_id',
                    'department_id',
                    'is_common',
                    'is_other',
                ]
            ],
            'meta' => [
                'current_page',
                'per_page',
                'last_page',
                'total'
            ]
        ]);
    }

    private function createTestCurrencies(): void
    {
        CabinetCurrency::factory()
            ->count(10)
            ->create([
                'cabinet_id' => $this->cabinet->id
            ]);
    }
}
