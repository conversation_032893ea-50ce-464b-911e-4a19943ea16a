<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Document;
use App\Models\Department;
use App\Models\Contractor;
use App\Models\LegalEntity;
use App\Models\IncomingPayment;
use App\Models\IncomingPaymentItem;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\CabinetCurrency;
use App\Models\Acceptance;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class IncomingPaymentItemTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_incoming_payment_items_list(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Создаем тестовые items
        IncomingPaymentItem::factory()->count(3)->create([
            'incoming_payment_id' => $payment->id
        ]);

        // Создаем item для другого платежа
        $otherPayment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);
        IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $otherPayment->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'incoming_payment_id',
                        'document_id',
                        'paid_in'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только items нашего платежа
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($payment->id, $item['incoming_payment_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_incoming_payment_items_without_incoming_payment_id(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments/items');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['incoming_payment_id']);
    }

    public function test_cannot_get_incoming_payment_items_with_invalid_incoming_payment_id(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => 'invalid-uuid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['incoming_payment_id']);
    }

    public function test_cannot_access_other_cabinet_incoming_payment_items(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(404);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'incoming_payment_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'incoming_payment_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Создаем items с разными суммами
        $item1 = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'paid_in' => 100
        ]);
        $item2 = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'paid_in' => 200
        ]);
        $item3 = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'paid_in' => 150
        ]);

        // Act - получаем отсортированный по сумме список
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'sortField' => 'paid_in',
            'sortDirection' => 'asc',
            'fields' => ['id', 'paid_in']
        ]));

        // Assert
        $response->assertStatus(200);

        $paidIn = collect($response->json('data'))->pluck('paid_in')->values();
        $expectedPaidIn = collect([$item1->paid_in, $item3->paid_in, $item2->paid_in]);

        $this->assertEquals($expectedPaidIn, $paidIn);
    }

    public function test_index_with_field_selection(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        IncomingPaymentItem::factory()->count(2)->create([
            'incoming_payment_id' => $payment->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'fields' => ['id', 'paid_in']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'paid_in',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только запрошенные поля
        foreach ($response->json('data') as $item) {
            $this->assertArrayHasKey('id', $item);
            $this->assertArrayHasKey('paid_in', $item);
            $this->assertArrayNotHasKey('created_at', $item);
            $this->assertArrayNotHasKey('updated_at', $item);
        }
    }

    public function test_index_pagination(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Создаем 15 items
        IncomingPaymentItem::factory()->count(15)->create([
            'incoming_payment_id' => $payment->id
        ]);

        // Act - запрашиваем первую страницу с 10 элементами
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'page' => 1,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'incoming_payment_id',
                        'document_id',
                        'paid_in'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем пагинацию
        $this->assertEquals(1, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(2, $response->json('meta.last_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(10, $response->json('data'));

        // Act - запрашиваем вторую страницу
        $response = $this->getJson('/api/internal/incoming-payments/items?' . http_build_query([
            'incoming_payment_id' => $payment->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_can_create_incoming_payment_item(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ];

        // Act
        $response = $this->postJson('/api/internal/incoming-payments/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('incoming_payment_items', [
            'id' => $response->json('id'),
            'incoming_payment_id' => $data['incoming_payment_id'],
            'document_id' => $data['document_id'],
            'paid_in' => $data['paid_in']
        ]);
    }

    public function test_cannot_create_incoming_payment_item_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/incoming-payments/items', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'incoming_payment_id',
                'document_id'
            ]);
    }

    public function test_cannot_create_incoming_payment_item_with_invalid_data(): void
    {
        $response = $this->postJson('/api/internal/incoming-payments/items', [
            'incoming_payment_id' => 'invalid-uuid',
            'document_id' => 'invalid-uuid',
            'paid_in' => 'not-a-number'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'incoming_payment_id',
                'document_id',
                'paid_in'
            ]);
    }

    public function test_cannot_create_incoming_payment_item_for_other_cabinet_payment(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $data = [
            'incoming_payment_id' => $payment->id,
            'document_id' => $acceptance->id,
            'paid_in' => 1000.50
        ];

        // Act
        $response = $this->postJson('/api/internal/incoming-payments/items', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_create_incoming_payment_item_with_document_from_other_cabinet(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->otherCabinet->id]);


        $data = [
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ];

        // Act
        $response = $this->postJson('/api/internal/incoming-payments/items', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_create_incoming_payment_item_without_paid_in(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);


        $data = [
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id
        ];

        // Act
        $response = $this->postJson('/api/internal/incoming-payments/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('incoming_payment_items', [
            'id' => $response->json('id'),
            'incoming_payment_id' => $data['incoming_payment_id'],
            'document_id' => $data['document_id'],
            'paid_in' => 0
        ]);
    }

    public function test_can_update_incoming_payment_item(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        $newDocument = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'paid_in' => 2000.75
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('incoming_payment_items', [
            'id' => $item->id,
            'paid_in' => $data['paid_in']
        ]);
    }

    public function test_cannot_update_incoming_payment_item_with_invalid_data(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        $data = [
            'paid_in' => 'not-a-number'
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'paid_in'
            ]);
    }

    public function test_cannot_update_incoming_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        $newDocument = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'document_id' => $newDocument->documentable_id,
            'paid_in' => 2000.75
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('incoming_payment_items', [
            'id' => $item->id,
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);
    }

    public function test_cannot_update_non_existent_incoming_payment_item(): void
    {
        // Arrange
        $data = [
            'document_id' => $this->faker->uuid(),
            'paid_in' => 2000.75
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/items/{$this->faker->uuid()}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_incoming_payment_item(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        // Act
        $response = $this->getJson("/api/internal/incoming-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'incoming_payment_id',
                'document_id',
                'paid_in'
            ]);

        $this->assertEquals($item->id, $response->json('id'));
        $this->assertEquals($payment->id, $response->json('incoming_payment_id'));
        $this->assertEquals($document->documentable_id, $response->json('document_id'));
        $this->assertEquals(1000.50, $response->json('paid_in'));
    }

    public function test_cannot_show_incoming_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        // Act
        $response = $this->getJson("/api/internal/incoming-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_incoming_payment_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/incoming-payments/items/{$this->faker->uuid()}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_incoming_payment_item(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('incoming_payment_items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_delete_incoming_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $document = Document::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $item = IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment->id,
            'document_id' => $document->documentable_id,
            'paid_in' => 1000.50
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('incoming_payment_items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_delete_non_existent_incoming_payment_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/items/{$this->faker->uuid()}");

        // Assert
        $response->assertStatus(404);
    }
}
