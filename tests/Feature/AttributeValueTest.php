<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AttributeValueTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем атрибут для тестов
        $this->attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем атрибут в другом кабинете
        $this->otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);
    }

    public function test_can_get_attribute_values_list(): void
    {
        // Arrange
        // Создаем значения атрибутов для нашего атрибута
        AttributeValue::factory()->count(3)->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Создаем значение атрибута для другого атрибута
        AttributeValue::factory()->create([
            'attribute_id' => $this->otherAttribute->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->attribute->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'attribute_id',
                        'value',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только значения нашего атрибута
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->attribute->id, $item['attribute_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_attribute_values_without_attribute_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attribute_values');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['attribute_id']);
    }

    public function test_cannot_get_attribute_values_with_invalid_attribute_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['attribute_id']);
    }

    public function test_cannot_get_attribute_values_from_other_cabinet(): void
    {
        // Arrange
        // Создаем значения атрибутов для атрибута из другого кабинета
        AttributeValue::factory()->count(2)->create([
            'attribute_id' => $this->otherAttribute->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->otherAttribute->id
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_can_get_attribute_values_with_pagination(): void
    {
        // Arrange
        AttributeValue::factory()->count(15)->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->attribute->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_can_get_attribute_values_with_sorting(): void
    {
        // Arrange
        $values = [
            'A value' => AttributeValue::factory()->create([
                'attribute_id' => $this->attribute->id,
                'value' => 'A value'
            ]),
            'B value' => AttributeValue::factory()->create([
                'attribute_id' => $this->attribute->id,
                'value' => 'B value'
            ]),
            'C value' => AttributeValue::factory()->create([
                'attribute_id' => $this->attribute->id,
                'value' => 'C value'
            ])
        ];

        // Act - получаем отсортированный по значению список
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->attribute->id,
            'sortField' => 'value',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);

        $values = collect($response->json('data'))->pluck('value')->values();
        $expectedValues = collect(['C value', 'B value', 'A value']);

        $this->assertEquals($expectedValues, $values);
    }

    public function test_can_get_attribute_values_with_fields(): void
    {
        AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->attribute->id,
            'fields' => ['value']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'value'
                    ]
                ],
                'meta'
            ]);
    }

    public function test_cannot_get_attribute_values_with_invalid_fields(): void
    {
        AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attribute_values?' . http_build_query([
            'attribute_id' => $this->attribute->id,
            'fields' => ['invalid-field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0']);
    }

    public function test_can_create_attribute_value(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'attribute_id' => $this->attribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->postJson('/api/internal/attribute_values', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attribute_values', [
            'id' => $response->json('id'),
            'attribute_id' => $data['attribute_id'],
            'value' => $data['value']
        ]);
    }

    public function test_cannot_create_attribute_value_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/attribute_values', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'attribute_id',
                'value'
            ]);
    }

    public function test_cannot_create_attribute_value_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'attribute_id' => 'not-a-uuid',
            'value' => str_repeat('a', 101) // превышает максимальную длину в 100 символов
        ];

        // Act
        $response = $this->postJson('/api/internal/attribute_values', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'attribute_id',
                'value'
            ]);
    }

    public function test_cannot_create_attribute_value_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'attribute_id' => $this->attribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->postJson('/api/internal/attribute_values', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('attribute_values', [
            'attribute_id' => $data['attribute_id'],
            'value' => $data['value']
        ]);
    }

    public function test_cannot_create_attribute_value_for_attribute_from_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'attribute_id' => $this->otherAttribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->postJson('/api/internal/attribute_values', $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('attribute_values', [
            'attribute_id' => $data['attribute_id'],
            'value' => $data['value']
        ]);
    }

    public function test_cannot_create_duplicate_attribute_value(): void
    {
        // Arrange
        $existingValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id,
            'value' => 'Existing Value'
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'attribute_id' => $this->attribute->id,
            'value' => $existingValue->value
        ];

        // Act
        $response = $this->postJson('/api/internal/attribute_values', $data);

        // Assert
        $response->assertStatus(422);
    }

    public function test_can_update_attribute_value(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        $data = [
            'attribute_id' => $this->attribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$attributeValue->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attribute_values', [
            'id' => $attributeValue->id,
            'attribute_id' => $data['attribute_id'],
            'value' => $data['value']
        ]);
    }

    public function test_cannot_update_attribute_value_without_required_fields(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$attributeValue->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'attribute_id',
                'value'
            ]);
    }

    public function test_cannot_update_attribute_value_with_invalid_data(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        $data = [
            'attribute_id' => 'not-a-uuid',
            'value' => str_repeat('a', 101) // превышает максимальную длину в 100 символов
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$attributeValue->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'attribute_id',
                'value'
            ]);
    }

    public function test_cannot_update_non_existent_attribute_value(): void
    {
        // Arrange
        $data = [
            'attribute_id' => $this->attribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/" . $this->faker->uuid(), $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_attribute_value_from_other_cabinet(): void
    {
        // Arrange
        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->otherAttribute->id
        ]);

        $data = [
            'attribute_id' => $this->otherAttribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$otherAttributeValue->id}", $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('attribute_values', [
            'id' => $otherAttributeValue->id,
            'value' => $otherAttributeValue->value // проверяем что значение не изменилось
        ]);
    }

    public function test_cannot_update_attribute_value_to_attribute_from_other_cabinet(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        $data = [
            'attribute_id' => $this->otherAttribute->id,
            'value' => $this->faker->word()
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$attributeValue->id}", $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('attribute_values', [
            'id' => $attributeValue->id,
            'attribute_id' => $this->attribute->id // проверяем что attribute_id не изменился
        ]);
    }

    public function test_cannot_update_attribute_value_to_duplicate_value(): void
    {
        // Arrange
        $existingValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id,
            'value' => 'ExistingValue'
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id,
            'value' => 'OriginalValue'
        ]);

        $data = [
            'attribute_id' => $this->attribute->id,
            'value' => $existingValue->value
        ];

        // Act
        $response = $this->putJson("/api/internal/attribute_values/{$attributeValue->id}", $data);

        // Assert
        $response->assertStatus(422);

        $this->assertDatabaseHas('attribute_values', [
            'id' => $attributeValue->id,
            'value' => 'OriginalValue' // проверяем что значение не изменилось
        ]);
    }

    public function test_can_show_attribute_value(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id,
            'value' => 'Test Value'
        ]);

        // Act
        $response = $this->getJson("/api/internal/attribute_values/{$attributeValue->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'attribute_id',
                'value',
            ])
            ->assertJson([
                'id' => $attributeValue->id,
                'attribute_id' => $this->attribute->id,
                'value' => 'Test Value'
            ]);
    }

    public function test_cannot_show_non_existent_attribute_value(): void
    {
        // Act
        $response = $this->getJson("/api/internal/attribute_values/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_attribute_value_from_other_cabinet(): void
    {
        // Arrange
        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->otherAttribute->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/attribute_values/{$otherAttributeValue->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_delete_attribute_value(): void
    {
        // Arrange
        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->attribute->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attribute_values/{$attributeValue->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('attribute_values', [
            'id' => $attributeValue->id
        ]);
    }

    public function test_cannot_delete_non_existent_attribute_value(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/attribute_values/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_delete_attribute_value_from_other_cabinet(): void
    {
        // Arrange
        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $this->otherAttribute->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attribute_values/{$otherAttributeValue->id}");

        // Assert
        $response->assertStatus(403);

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('attribute_values', [
            'id' => $otherAttributeValue->id
        ]);
    }
}
