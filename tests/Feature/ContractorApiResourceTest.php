<?php

namespace Tests\Feature;

use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContractorApiResourceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Department $department;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_contractors_index_returns_api_resource_collection()
    {
        // Arrange
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
            'title' => 'Test Contractor API',
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                            'cabinet_id',
                            'status_id',
                            'phone',
                            'email',
                        ]
                    ]
                ]
            ]);

        // Выводим структуру ответа для отладки
        dump('API Response:', $response->json());

        // Проверяем, что данные контрагента присутствуют в ответе
        $responseContent = $response->getContent();
        $this->assertStringContainsString('Test Contractor API', $responseContent);
        $this->assertStringContainsString('+79001234567', $responseContent);
        $this->assertStringContainsString('<EMAIL>', $responseContent);
    }
}
