<?php

namespace Tests\Unit\Actions\Currencies;

use Illuminate\Support\Facades\Http;
use App\Actions\Currencies\FetchCurrenciesAction;
use App\Actions\Currencies\UpdateCurrenciesAction;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UpdateCurrenciesActionTest extends TestCase
{
    use RefreshDatabase;

    private UpdateCurrenciesAction $action;
    private FetchCurrenciesAction $fetchAction;
    private string $mockXmlResponse = '<?xml version="1.0" encoding="UTF-8"?>
            <ValCurs Date="01.01.2024" name="Foreign Currency Market">
                <Valute ID="R01010">
                  <NumCode>036</NumCode>
                  <CharCode>AUD</CharCode>
                  <Nominal>1</Nominal>
                  <Name>Австралийский доллар</Name>
                  <Value>52,5853</Value>
                  <VunitRate>52,5853</VunitRate>
                </Valute>
                <Valute ID="R01020A">
                  <NumCode>944</NumCode>
                  <CharCode>AZN</CharCode>
                  <Nominal>1</Nominal>
                  <Name>Азербайджанский манат</Name>
                  <Value>49,2243</Value>
                  <VunitRate>49,2243</VunitRate>
                </Valute>
            </ValCurs>';

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = new UpdateCurrenciesAction();
        $this->fetchAction = new FetchCurrenciesAction();
    }

    public function test_it_updates_existing_currencies(): void
    {
        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($this->mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Мокаем проверку существования записей
        DB::shouldReceive('table->where->exists')
            ->once()
            ->andReturn(false);

        // Мокаем обновление в global_currencies
        DB::shouldReceive('table->where->update')
            ->times(2)
            ->andReturn(true);

        // Мокаем вставку в global_currencies_history
        DB::shouldReceive('table->insert')
            ->times(2)
            ->andReturn(true);

        $this->action->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_skips_update_if_records_exist(): void
    {
        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($this->mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Мокаем проверку существования записей
        DB::shouldReceive('table->where->exists')
            ->once()
            ->andReturn(true);

        // Проверяем, что методы обновления и вставки не вызываются
        DB::shouldNotReceive('table->where->update');
        DB::shouldNotReceive('table->insert');

        $this->action->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_skips_update_if_date_in_future(): void
    {
        $futureDate = Carbon::now()->addDays(1)->format('d.m.Y');
        $mockXmlResponse = str_replace('01.01.2024', $futureDate, $this->mockXmlResponse);

        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Мокаем проверку существования записей
        DB::shouldReceive('table->where->exists')
            ->once()
            ->andReturn(false);

        // Проверяем, что методы обновления и вставки не вызываются
        DB::shouldNotReceive('table->where->update');
        DB::shouldNotReceive('table->insert');

        $this->action->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_converts_currency_values_correctly(): void
    {
        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($this->mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Мокаем проверку существования записей
        DB::shouldReceive('table->where->exists')
            ->once()
            ->andReturn(false);

        // Мокаем обновление в global_currencies
        DB::shouldReceive('table->where->update')
            ->times(2)
            ->andReturnUsing(function ($data) {
                if ($data['num_code'] === '036') { // AUD
                    $this->assertEquals(52.5853, $data['value']);
                }
                if ($data['num_code'] === '944') { // AZN
                    $this->assertEquals(49.2243, $data['value']);
                }
                return true;
            });

        // Мокаем вставку в global_currencies_history
        DB::shouldReceive('table->insert')
            ->times(2)
            ->andReturn(true);

        $this->action->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_generates_valid_uuid(): void
    {
        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($this->mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Мокаем проверку существования записей
        DB::shouldReceive('table->where->exists')
            ->once()
            ->andReturn(false);

        // Мокаем обновление в global_currencies
        DB::shouldReceive('table->where->update')
            ->times(2)
            ->andReturnUsing(function ($data) {
                $this->assertMatchesRegularExpression(
                    '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i',
                    $data['id']
                );
                return true;
            });

        // Мокаем вставку в global_currencies_history
        DB::shouldReceive('table->insert')
            ->times(2)
            ->andReturn(true);

        $this->action->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_handles_http_error(): void
    {
        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response('Error', 500, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        $this->expectException(\Exception::class);

        $this->action->handle();
    }
}
