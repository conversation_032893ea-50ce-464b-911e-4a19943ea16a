<?php

namespace Tests\Unit\Console\Commands;

use Tests\TestCase;
use Mockery;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\WithoutErrorHandler;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class FillMeasurementUnitsTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем тестовые данные
        $this->createMockDataFile();

        // По умолчанию мокаем DB для успешного выполнения
        // Это упрощает тесты, чтобы не повторять код в каждом методе
        DB::shouldReceive('table')->andReturnSelf()->byDefault();
        DB::shouldReceive('insert')->andReturn(true)->byDefault();
    }

    protected function createMockDataFile(): void
    {
        // Создаем простой набор тестовых данных
        $mockData = [
            'Длина' => [
                [
                    'name' => 'Метр',
                    'short_name' => 'м',
                    'code' => 'MTR',
                    'conversion_factor' => 1.0
                ]
            ]
        ];

        // Мокаем base_path
        $this->app->bind('path.base', function () {
            return __DIR__;
        });

        // Создаем директории и файл данных
        if (!file_exists(__DIR__ . '/app/Data')) {
            mkdir(__DIR__ . '/app/Data', 0777, true);
        }

        file_put_contents(
            __DIR__ . '/app/Data/DefaultMeasurementUnits.php',
            '<?php return ' . var_export($mockData, true) . ';'
        );
    }

    protected function tearDown(): void
    {
        // Удаляем тестовые файлы
        $dataPath = __DIR__ . '/app/Data';
        $appPath = __DIR__ . '/app';

        // Удаляем файл единиц измерения
        if (file_exists($dataPath . '/DefaultMeasurementUnits.php')) {
            unlink($dataPath . '/DefaultMeasurementUnits.php');
        }

        // Удаляем директорию Data
        if (is_dir($dataPath)) {
            // Убедимся, что директория пуста
            $files = scandir($dataPath);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    if (is_file($dataPath . '/' . $file)) {
                        unlink($dataPath . '/' . $file);
                    }
                }
            }
            rmdir($dataPath);
        }

        // Удаляем директорию app
        if (is_dir($appPath)) {
            // Убедимся, что директория пуста
            $files = scandir($appPath);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    if (is_dir($appPath . '/' . $file)) {
                        rmdir($appPath . '/' . $file);
                    } elseif (is_file($appPath . '/' . $file)) {
                        unlink($appPath . '/' . $file);
                    }
                }
            }
            rmdir($appPath);
        }

        Mockery::close();
        parent::tearDown();
    }

    #[WithoutErrorHandler] public function test_it_should_fill_measurement_units_successfully(): void
    {
        // Тестируем только успешный вызов и вывод команды
        $this->artisan('app:fill-measurement-units')
            ->assertExitCode(0)
            ->expectsOutput('Получаем данные по умолчанию...')
            ->expectsOutput('Заполняем таблицу "measurement_units"...')
            ->expectsOutput('Заполнение таблицы "measurement_units" завершено.');
    }
}
