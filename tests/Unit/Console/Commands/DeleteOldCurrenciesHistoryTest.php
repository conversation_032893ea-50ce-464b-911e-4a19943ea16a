<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\DeleteOldCurrenciesHistory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class DeleteOldCurrenciesHistoryTest extends TestCase
{
    use DatabaseTransactions;

    private DeleteOldCurrenciesHistory $command;
    private Carbon $now;

    protected function setUp(): void
    {
        parent::setUp();
        $this->command = new DeleteOldCurrenciesHistory();
        $this->now = Carbon::now();
        Carbon::setTestNow($this->now);
    }

    public function test_it_deletes_old_currencies_history(): void
    {
        // Создаем тестовые данные
        $oldRecord = [
            'id' => 1,
            'num_code' => '840',
            'char_code' => 'USD',
            'value' => 91.2345,
            'currency_date' => $this->now->copy()->subDays(91)->format('Y-m-d'),
            'created_at' => $this->now->copy()->subDays(91),
            'updated_at' => $this->now->copy()->subDays(91),
        ];

        $recentRecord = [
            'id' => 2,
            'num_code' => '978',
            'char_code' => 'EUR',
            'value' => 99.8765,
            'currency_date' => $this->now->copy()->subDays(89)->format('Y-m-d'),
            'created_at' => $this->now->copy()->subDays(89),
            'updated_at' => $this->now->copy()->subDays(89),
        ];

        // Мокаем DB фасад
        DB::shouldReceive('table')
            ->once()
            ->with('global_currencies_history')
            ->andReturnSelf();

        DB::shouldReceive('where')
            ->once()
            ->with('currency_date', '<', $this->now->copy()->subDays(90)->format('Y-m-d'))
            ->andReturnSelf();

        // Проверяем, что удаляется только старая запись
        DB::shouldReceive('delete')
            ->once()
            ->andReturnUsing(function () use ($oldRecord) {
                // Проверяем, что удаляется только запись старше 90 дней
                $this->assertEquals($oldRecord['currency_date'], $this->now->copy()->subDays(91)->format('Y-m-d'));
                return true;
            });

        // Выполняем команду
        $this->command->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_returns_void(): void
    {
        // Мокаем DB фасад
        DB::shouldReceive('table->where->delete')
            ->once()
            ->andReturn(true);

        // Проверяем, что метод handle возвращает void
        $this->assertNull($this->command->handle());
    }

    public function test_it_does_not_delete_recent_records(): void
    {
        // Создаем тестовые данные
        $recentRecord = [
            'id' => 1,
            'num_code' => '840',
            'char_code' => 'USD',
            'value' => 91.2345,
            'currency_date' => $this->now->copy()->subDays(89)->format('Y-m-d'),
            'created_at' => $this->now->copy()->subDays(89),
            'updated_at' => $this->now->copy()->subDays(89),
        ];

        // Мокаем DB фасад
        DB::shouldReceive('table')
            ->once()
            ->with('global_currencies_history')
            ->andReturnSelf();

        DB::shouldReceive('where')
            ->once()
            ->with('currency_date', '<', $this->now->copy()->subDays(90)->format('Y-m-d'))
            ->andReturnSelf();

        // Проверяем, что недавние записи не удаляются
        DB::shouldReceive('delete')
            ->once()
            ->andReturnUsing(function () use ($recentRecord) {
                // Проверяем, что запись не старше 90 дней
                $this->assertGreaterThan(
                    $this->now->copy()->subDays(90)->format('Y-m-d'),
                    $recentRecord['currency_date']
                );
                return true;
            });

        // Выполняем команду
        $this->command->handle();

        // Проверяем, что все методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_has_correct_signature(): void
    {
        $this->assertEquals('app:delete-old-currencies-history', $this->command->getName());
    }

    public function test_it_has_description(): void
    {
        $this->assertEquals('Command description', $this->command->getDescription());
    }
}
