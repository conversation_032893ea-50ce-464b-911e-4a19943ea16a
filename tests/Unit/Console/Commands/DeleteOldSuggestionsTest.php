<?php

namespace Tests\Unit\Console\Commands;

use App\Enums\Api\Internal\SuggestTypeEnum;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class DeleteOldSuggestionsTest extends TestCase
{
    use DatabaseTransactions;

    private Carbon $now;

    protected function setUp(): void
    {
        parent::setUp();

        $this->now = Carbon::now();
        Carbon::setTestNow($this->now);

        // Clear the suggestions table before each test
        DB::table('suggestions')->truncate();
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Reset Carbon's test time
        Carbon::setTestNow();
    }

    public function test_it_should_delete_bank_suggestions_older_than_30_days(): void
    {
        // Arrange
        $this->createSuggestion(
            type: SuggestTypeEnum::BANK->value,
            daysOld: 31
        );

        // Act
        $this->artisan('app:delete-old-suggestions')->assertSuccessful();

        // Assert
        $this->assertDatabaseEmpty('suggestions');
    }

    public function test_it_should_delete_party_suggestions_older_than_30_days(): void
    {
        // Arrange
        $this->createSuggestion(
            type: SuggestTypeEnum::PARTY->value,
            daysOld: 31
        );

        // Act
        $this->artisan('app:delete-old-suggestions')->assertSuccessful();

        // Assert
        $this->assertDatabaseEmpty('suggestions');
    }

    public function test_it_should_keep_recent_bank_and_party_suggestions(): void
    {
        // Arrange
        $this->createSuggestion(
            type: SuggestTypeEnum::BANK->value,
            daysOld: 29
        );
        $this->createSuggestion(
            type: SuggestTypeEnum::PARTY->value,
            daysOld: 29
        );

        // Act
        $this->artisan('app:delete-old-suggestions')->assertSuccessful();

        // Assert
        $this->assertDatabaseCount('suggestions', 2);
        $this->assertDatabaseHas('suggestions', [
            'type' => SuggestTypeEnum::BANK,
            'created_at' => $this->now->copy()->subDays(29)
        ]);
        $this->assertDatabaseHas('suggestions', [
            'type' => SuggestTypeEnum::PARTY,
            'created_at' => $this->now->copy()->subDays(29)
        ]);
    }

    public function test_it_should_not_delete_other_type_suggestions_regardless_of_age(): void
    {
        // Arrange
        $this->createSuggestion(
            type: 'OTHER_TYPE',
            daysOld: 31
        );

        // Act
        $this->artisan('app:delete-old-suggestions')->assertSuccessful();

        // Assert
        $this->assertDatabaseCount('suggestions', 1);
        $this->assertDatabaseHas('suggestions', [
            'type' => 'OTHER_TYPE',
            'created_at' => $this->now->copy()->subDays(31)
        ]);
    }

    public function test_it_should_handle_mixed_scenarios_correctly(): void
    {
        // Arrange
        // Old suggestions that should be deleted
        $this->createSuggestion(type: SuggestTypeEnum::BANK->value, daysOld: 31);
        $this->createSuggestion(type: SuggestTypeEnum::PARTY->value, daysOld: 31);

        // Recent suggestions that should be kept
        $this->createSuggestion(type: SuggestTypeEnum::BANK->value, daysOld: 29);
        $this->createSuggestion(type: SuggestTypeEnum::PARTY->value, daysOld: 29);

        // Other type suggestions that should be kept regardless of age
        $this->createSuggestion(type: 'OTHER_TYPE', daysOld: 31);
        $this->createSuggestion(type: 'OTHER_TYPE', daysOld: 29);

        // Act
        $this->artisan('app:delete-old-suggestions')->assertSuccessful();

        // Assert
        $this->assertDatabaseCount('suggestions', 4); // 2 recent + 2 other type

        // Verify old suggestions were deleted
        $this->assertDatabaseMissing('suggestions', [
            'type' => SuggestTypeEnum::BANK,
            'created_at' => $this->now->copy()->subDays(31)
        ]);
        $this->assertDatabaseMissing('suggestions', [
            'type' => SuggestTypeEnum::PARTY,
            'created_at' => $this->now->copy()->subDays(31)
        ]);

        // Verify recent suggestions were kept
        $this->assertDatabaseHas('suggestions', [
            'type' => SuggestTypeEnum::BANK,
            'created_at' => $this->now->copy()->subDays(29)
        ]);
        $this->assertDatabaseHas('suggestions', [
            'type' => SuggestTypeEnum::PARTY,
            'created_at' => $this->now->copy()->subDays(29)
        ]);

        // Verify other type suggestions were kept
        $this->assertDatabaseHas('suggestions', [
            'type' => 'OTHER_TYPE',
            'created_at' => $this->now->copy()->subDays(31)
        ]);
        $this->assertDatabaseHas('suggestions', [
            'type' => 'OTHER_TYPE',
            'created_at' => $this->now->copy()->subDays(29)
        ]);
    }

    /**
     * Helper method to create a suggestion record
     */
    private function createSuggestion(string $type, int $daysOld): void
    {
        DB::table('suggestions')->insert([
            'type' => $type,
            'created_at' => $this->now->copy()->subDays($daysOld),
            'updated_at' => $this->now->copy()->subDays($daysOld),
            'query' => 'query',
            'content' => json_encode('content')
        ]);
    }
}
