<?php

namespace Tests\Unit\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class DeleteExpiresVerificationCodesTest extends TestCase
{
    use DatabaseTransactions;

    private Carbon $now;

    protected function setUp(): void
    {
        parent::setUp();

        $this->now = Carbon::now();
        Carbon::setTestNow($this->now);

        // Clear the verification codes table before each test
        DB::table('email_verification_codes')->truncate();
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Reset Carbon's test time
        Carbon::setTestNow();
    }

    public function test_it_should_delete_expired_verification_codes(): void
    {
        // Arrange
        $this->createVerificationCode(
            expiresInMinutes: -60 // expired 1 hour ago
        );

        // Act
        $this->artisan('app:delete-expires-verification-codes')->assertSuccessful();

        // Assert
        $this->assertDatabaseEmpty('email_verification_codes');
    }

    public function test_it_should_keep_non_expired_verification_codes(): void
    {
        // Arrange
        $this->createVerificationCode(
            expiresInMinutes: 60 // expires in 1 hour
        );

        // Act
        $this->artisan('app:delete-expires-verification-codes')->assertSuccessful();

        // Assert
        $this->assertDatabaseCount('email_verification_codes', 1);
        $this->assertDatabaseHas('email_verification_codes', [
            'expires_at' => $this->now->copy()->addMinutes(60)
        ]);
    }

    public function test_it_should_handle_mixed_scenarios_correctly(): void
    {
        // Arrange
        // Create expired codes
        $this->createVerificationCode(expiresInMinutes: -120); // expired 2 hours ago
        $this->createVerificationCode(expiresInMinutes: -60);  // expired 1 hour ago

        // Create non-expired codes
        $this->createVerificationCode(expiresInMinutes: 30);   // expires in 30 minutes
        $this->createVerificationCode(expiresInMinutes: 60);   // expires in 1 hour

        // Act
        $this->artisan('app:delete-expires-verification-codes')->assertSuccessful();

        // Assert
        $this->assertDatabaseCount('email_verification_codes', 2); // only non-expired should remain

        // Verify expired codes were deleted
        $this->assertDatabaseMissing('email_verification_codes', [
            'expires_at' => $this->now->copy()->subMinutes(120)
        ]);
        $this->assertDatabaseMissing('email_verification_codes', [
            'expires_at' => $this->now->copy()->subMinutes(60)
        ]);

        // Verify non-expired codes were kept
        $this->assertDatabaseHas('email_verification_codes', [
            'expires_at' => $this->now->copy()->addMinutes(30)
        ]);
        $this->assertDatabaseHas('email_verification_codes', [
            'expires_at' => $this->now->copy()->addMinutes(60)
        ]);
    }

    public function test_it_should_handle_codes_expiring_exactly_now(): void
    {
        // Arrange
        $this->createVerificationCode(
            expiresInMinutes: -1 // expires exactly now
        );

        // Act
        $this->artisan('app:delete-expires-verification-codes')->assertSuccessful();

        // Assert
        $this->assertDatabaseEmpty('email_verification_codes');
    }

    /**
     * Helper method to create a verification code record
     */
    private function createVerificationCode(int $expiresInMinutes): void
    {
        DB::table('email_verification_codes')->insert([
            'code' => '1234',
            'user_id' => User::factory()->create()->id,
            'expires_at' => $this->now->copy()->addMinutes($expiresInMinutes),
            'created_at' => $this->now,
            'updated_at' => $this->now
        ]);
    }
}
