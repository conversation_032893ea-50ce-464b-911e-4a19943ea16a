<?php

namespace Tests\Unit\Console\Commands;

use Mockery;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Contracts\Services\Internal\Directories\CountriesServiceContract;

class FillCountriesTest extends TestCase
{
    use DatabaseTransactions;
    private CountriesServiceContract $countriesService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock for CountriesService
        $this->countriesService = Mockery::mock(CountriesServiceContract::class);

        // Bind mock to container
        $this->app->instance(CountriesServiceContract::class, $this->countriesService);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Clean up Mockery
        Mockery::close();
    }

    public function test_it_should_call_fill_system_method_on_countries_service(): void
    {
        // Arrange
        $this->countriesService
            ->shouldReceive('fillSystem')
            ->once()
            ->andReturn(null);

        // Act
        $this->artisan('app:fill-countries')->assertSuccessful();

        // Assert
        // Mockery will automatically verify that fillSystem was called exactly once
    }

    public function test_it_should_handle_service_exceptions_gracefully(): void
    {
        // Arrange
        $this->countriesService
            ->shouldReceive('fillSystem')
            ->once()
            ->andThrow(new \Exception('Service error'));

        // Act & Assert
        $this->artisan('app:fill-countries')
            ->assertFailed()
            ->expectsOutput('Error filling countries: Service error');
    }

    public function test_it_should_handle_binding_resolution_exception(): void
    {
        // Arrange
        $this->app->bind(CountriesServiceContract::class, function () {
            throw new BindingResolutionException('Could not resolve CountriesService');
        });

        // Act & Assert
        $this->artisan('app:fill-countries')
            ->assertFailed()
            ->expectsOutput('Error resolving CountriesService');
    }
}
