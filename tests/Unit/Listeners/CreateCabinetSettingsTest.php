<?php

namespace Tests\Unit\Listeners;

use Tests\TestCase;
use Mockery;
use App\Events\CabinetCreated;
use App\Models\SalesChannelType;
use App\Models\User;
use App\Models\GlobalCurrency;
use App\Listeners\CreateCabinetSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;

class CreateCabinetSettingsTest extends TestCase
{
    use RefreshDatabase;

    private string $cabinetId;
    private User $user;
    private CreateCabinetSettings $listener;
    private $cabinetSettingRepository;
    private $employeeRepository;
    private $cabinetEmployeeRepository;
    private $departmentsRepository;
    private $vatRatesRepository;
    private $taxRatesRepository;
    private $currenciesRepository;
    private $cabinetCurrenciesRepository;
    private $salesChannelsRepository;
    private $salesChannelTypesRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cabinetId = 'test-cabinet-id';

        // Создаем реального пользователя для тестов
        $this->user = User::factory()->create([
            'id' => 123,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => '<EMAIL>'
        ]);
        $this->user->id = 'test-user-id';
        $this->user->lastname = 'Test';
        $this->user->firstname = 'User';
        $this->user->patronymic = 'Patronymic';
        $this->user->email = '<EMAIL>';

        // Создаем моки для всех репозиториев
        $this->cabinetSettingRepository = Mockery::mock(CabinetSettingRepositoryContract::class);
        $this->employeeRepository = Mockery::mock(EmployeeRepositoryContract::class);
        $this->cabinetEmployeeRepository = Mockery::mock(CabinetEmployeeRepositoryContract::class);
        $this->departmentsRepository = Mockery::mock(DepartmentsRepositoryContract::class);
        $this->vatRatesRepository = Mockery::mock(VatRatesRepositoryContract::class);
        $this->taxRatesRepository = Mockery::mock(ProfitTaxRatesRepositoryContract::class);
        $this->currenciesRepository = Mockery::mock(GlobalCurrenciesRepositoryContract::class);
        $this->cabinetCurrenciesRepository = Mockery::mock(CabinetCurrenciesRepositoryContract::class);
        $this->salesChannelsRepository = Mockery::mock(SalesChannelsRepositoryContract::class);
        $this->salesChannelTypesRepository = Mockery::mock(SaleChannelTypesRepositoryContract::class);

        $this->listener = new CreateCabinetSettings(
            $this->cabinetSettingRepository,
            $this->employeeRepository,
            $this->cabinetEmployeeRepository,
            $this->departmentsRepository,
            $this->vatRatesRepository,
            $this->taxRatesRepository,
            $this->currenciesRepository,
            $this->cabinetCurrenciesRepository,
            $this->salesChannelsRepository,
            $this->salesChannelTypesRepository
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_creates_cabinet_settings_successfully(): void
    {
        // Подготавливаем тестовые данные
        $event = new CabinetCreated($this->cabinetId, $this->user);

        // Мокаем проверку существующих настроек
        $this->cabinetSettingRepository->shouldReceive('show')
            ->with($this->cabinetId)
            ->once()
            ->andReturn(null);

        // Мокаем получение типов каналов продаж
        $salesChannelType = new SalesChannelType();
        $salesChannelType->id = 'test-sales-channel-type-id';
        $salesChannelType->name = 'Розничные продажи';
        $this->salesChannelTypesRepository->shouldReceive('get')
            ->with(['id', 'name'])
            ->once()
            ->andReturn(collect([$salesChannelType]));

        // Мокаем получение валюты
        $currency = new GlobalCurrency();
        $currency->id = 'test-currency-id';
        $this->currenciesRepository->shouldReceive('getByExternalId')
            ->with('R00000')
            ->once()
            ->andReturn($currency);

        // Настраиваем ожидания для всех репозиториев
        $this->cabinetSettingRepository->shouldReceive('insert')->once();
        $this->departmentsRepository->shouldReceive('insert')->once();
        $this->departmentsRepository->shouldReceive('update')->once();
        $this->employeeRepository->shouldReceive('insert')->once();
        $this->cabinetEmployeeRepository->shouldReceive('insert')->once();
        $this->salesChannelsRepository->shouldReceive('insert')->once();
        $this->vatRatesRepository->shouldReceive('insert')->once();
        $this->taxRatesRepository->shouldReceive('insert')->once();
        $this->cabinetCurrenciesRepository->shouldReceive('insert')->once();

        // Мокаем очистку кеша
        Cache::shouldReceive('delete')
            ->with("permissions_123")
            ->once();
        Cache::shouldReceive('delete')
            ->with("employees_123")
            ->once();

        // Выполняем слушатель
        $this->listener->handle($event);

        // Проверяем, что все ожидаемые методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_handles_repository_exceptions_gracefully(): void
    {
        // Подготавливаем тестовые данные
        $event = new CabinetCreated($this->cabinetId, $this->user);

        // Мокаем исключение в одном из репозиториев
        $this->cabinetSettingRepository->shouldReceive('insert')
            ->andThrow(new \Exception('Database error'));

        // Проверяем, что исключение пробрасывается выше
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        // Выполняем слушатель
        $this->listener->handle($event);
    }
}
