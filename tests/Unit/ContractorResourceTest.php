<?php

namespace Tests\Unit;

use App\Http\Resources\ContractorCollection;
use App\Http\Resources\ContractorResource;
use Illuminate\Http\Request;
use PHPUnit\Framework\TestCase;

class ContractorResourceTest extends TestCase
{
    public function test_contractor_resource_handles_array_data()
    {
        // Создаем тестовые данные в виде массива
        $data = [
            'id' => '9f653454-12a9-4e36-9c3c-91818cf3f994',
            'title' => 'Test Contractor',
            'created_at' => '2025-07-15 09:03:04',
            'updated_at' => '2025-07-15 09:03:04',
            'deleted_at' => null,
            'archived_at' => null,
            'cabinet_id' => '9f653454-055b-4433-8555-18bc41be1157',
            'shared_access' => true,
            'employee_id' => '9f653454-09dd-4ab6-9713-7aa2a1a2b1d8',
            'department_id' => '9f653454-0d52-49e0-8362-e07433dc3a9f',
            'status_id' => '9f653452-b6b2-4a5c-9ba8-8911404c6c9d',
            'is_buyer' => true,
            'is_supplier' => true,
            'phone' => '88005553535',
            'fax' => 'test',
            'email' => '<EMAIL>',
            'description' => 'Test Description',
            'code' => 'TEST',
            'external_code' => 'EXT',
            'discounts_and_prices' => 'test',
            'discount_card_number' => '1234567890',
            'is_default' => false,
            'status' => [
                'id' => '9f653452-b6b2-4a5c-9ba8-8911404c6c9d',
                'name' => 'New',
                'color' => 'fff',
                'type_id' => '9f653452-b50f-4f4f-b8d6-c890e7182128',
                'cabinet_id' => null,
                'created_at' => null,
                'deleted_at' => null,
                'updated_at' => null
            ],
            'details' => null
        ];

        // Создаем ресурс с массивом данных
        $resource = new ContractorResource($data);
        
        // Преобразуем ресурс в массив
        $result = $resource->toArray(new Request());
        
        // Проверяем, что все ключи присутствуют
        $this->assertEquals($data['id'], $result['id']);
        $this->assertEquals($data['title'], $result['title']);
        $this->assertEquals($data['email'], $result['email']);
        $this->assertEquals($data['phone'], $result['phone']);
        
        // Проверяем, что связанные данные тоже присутствуют
        $this->assertEquals($data['status'], $result['status']);
    }

    public function test_contractor_collection_handles_array_data()
    {
        // Создаем коллекцию тестовых данных
        $data = [
            [
                'id' => '9f653454-12a9-4e36-9c3c-91818cf3f994',
                'title' => 'Test Contractor 1',
                'email' => '<EMAIL>',
                'phone' => '88005553535'
            ],
            [
                'id' => '9f653454-12a9-4e36-9c3c-91818cf3f995',
                'title' => 'Test Contractor 2',
                'email' => '<EMAIL>',
                'phone' => '88005553536'
            ]
        ];

        // Создаем коллекцию ресурсов
        $collection = new ContractorCollection(collect($data));
        
        // Преобразуем коллекцию в массив
        $result = $collection->toArray(new Request());
        
        // Проверяем структуру
        $this->assertArrayHasKey('data', $result);
        $this->assertCount(2, $result['data']);
        
        // Проверяем данные
        $this->assertEquals($data[0]['id'], $result['data'][0]['id']);
        $this->assertEquals($data[0]['title'], $result['data'][0]['title']);
        $this->assertEquals($data[1]['id'], $result['data'][1]['id']);
        $this->assertEquals($data[1]['title'], $result['data'][1]['title']);
    }
}
