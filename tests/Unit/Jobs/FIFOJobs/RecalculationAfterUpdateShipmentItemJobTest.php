<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateShipmentItemJob;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;

class RecalculationAfterUpdateShipmentItemJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private object $oldItem;
    private ShipmentItemDTO $newItem;
    private FifoServiceContract $fifoService;
    private ShipmentsRepositoryContract $shipmentsRepository;
    private ShipmentItemsRepositoryContract $shipmentItemsRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oldItem = (object)[
            'id' => $this->faker()->uuid,
            'shipment_id' => $this->faker()->uuid,
            'price' => 100,
            'quantity' => 10,
            'total_cost' => 800 // Предполагаем, что себестоимость меньше цены
        ];

        $this->newItem = new ShipmentItemDTO(
            cabinetId: $this->faker()->uuid,
            shipmentId: $this->oldItem->shipment_id,
            productId: $this->faker()->uuid,
            vat_rate_id: $this->faker()->uuid,
            price: 100,
            quantity: 10,
            resourceId: $this->oldItem->id
        );
        $this->newItem->totalCost = $this->oldItem->total_cost;

        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        $this->shipmentsRepository = Mockery::mock(ShipmentsRepositoryContract::class);
        $this->shipmentItemsRepository = Mockery::mock(ShipmentItemsRepositoryContract::class);

        $this->app->instance(FifoServiceContract::class, $this->fifoService);
        $this->app->instance(ShipmentsRepositoryContract::class, $this->shipmentsRepository);
        $this->app->instance(ShipmentItemsRepositoryContract::class, $this->shipmentItemsRepository);
    }

    public function test_updates_sums_when_only_price_changes(): void
    {
        // Arrange
        $this->newItem = new ShipmentItemDTO(
            cabinetId: $this->faker()->uuid,
            shipmentId: $this->oldItem->shipment_id,
            productId: $this->faker()->uuid,
            vat_rate_id: $this->faker()->uuid,
            price: 150, // Изменяем цену
            quantity: 10, // Количество то же
            resourceId: $this->oldItem->id
        );
        $this->newItem->totalCost = $this->oldItem->total_cost;

        $shipmentTotals = (object)[
            'total_price_sum' => 2000,
            'profit_sum' => 500
        ];

        $this->shipmentItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) {
                $expectedTotalPrice = $this->newItem->price * $this->newItem->quantity;
                $expectedProfit = $expectedTotalPrice - $this->newItem->totalCost;

                return $id === $this->oldItem->id
                    && $data['price'] === 150
                    && $data['discount'] === 0
                    && $data['total_price'] === $expectedTotalPrice
                    && $data['profit'] === $expectedProfit;
            });

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentTotalsById')
            ->once()
            ->with($this->oldItem->shipment_id)
            ->andReturn($shipmentTotals);

        $this->shipmentsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($shipmentTotals) {
                return $id === $this->oldItem->shipment_id
                    && $data['total_cost'] === $shipmentTotals->total_price_sum
                    && $data['profit'] === $shipmentTotals->profit_sum;
            });

        $this->fifoService->shouldNotReceive('handle');

        // Act
        $job = new RecalculationAfterUpdateShipmentItemJob($this->oldItem, $this->newItem);
        $job->handle();
    }

    public function test_recalculates_fifo_when_quantity_changes(): void
    {
        // Arrange
        $this->newItem = new ShipmentItemDTO(
            cabinetId: $this->faker()->uuid,
            shipmentId: $this->oldItem->shipment_id,
            productId: $this->faker()->uuid,
            vat_rate_id: $this->faker()->uuid,
            price: 100, // Цена та же
            quantity: 15, // Изменяем количество
            resourceId: $this->oldItem->id
        );
        $this->newItem->totalCost = $this->oldItem->total_cost;

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($this->oldItem->id);

        $this->shipmentItemsRepository->shouldNotReceive('update');
        $this->shipmentItemsRepository->shouldNotReceive('getShipmentTotalsById');
        $this->shipmentsRepository->shouldNotReceive('update');

        // Act
        $job = new RecalculationAfterUpdateShipmentItemJob($this->oldItem, $this->newItem);
        $job->handle();
    }

    public function test_skips_update_when_nothing_changes(): void
    {
        // Arrange
        $this->fifoService->shouldNotReceive('handle');
        $this->shipmentItemsRepository->shouldNotReceive('update');
        $this->shipmentItemsRepository->shouldNotReceive('getShipmentTotalsById');
        $this->shipmentsRepository->shouldNotReceive('update');

        // Act
        $job = new RecalculationAfterUpdateShipmentItemJob($this->oldItem, $this->newItem);
        $job->handle();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
