<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Jobs\FIFOJobs\RecalculateAcceptanceItemsRecidualJob;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceJob;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\DTO\AcceptanceDto;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Tests\TestCase;

class RecalculationAfterUpdateAcceptanceJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private object $oldResource;
    private AcceptanceDto $newResource;
    private FifoServiceContract $fifoService;
    private ShipmentsRepositoryContract $shipmentsRepository;
    private WarehouseItemsRepositoryContract $warehouseItemsRepository;
    private AcceptanceItemsRepositoryContract $acceptanceItemsRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oldResource = (object)[
            'id' => $this->faker()->uuid,
            'date_from' => '2024-01-01',
            'warehouse_id' => '1',
            'held' => true
        ];

        $this->newResource = new AcceptanceDto(
            cabinetId: $this->faker()->uuid,
            legalEntityId: $this->faker()->uuid,
            contractorId: $this->faker()->uuid,
            warehouseId: '1',
            departmentId: $this->faker()->uuid,
            employeeId: $this->faker()->uuid,
            userId: 1,
            currencyId: $this->faker()->uuid,
            currencyValue: 1.0,
            dateFrom: '2024-01-01',
            held: true,
            resourceId: $this->oldResource->id
        );

        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        $this->shipmentsRepository = Mockery::mock(ShipmentsRepositoryContract::class);
        $this->warehouseItemsRepository = Mockery::mock(WarehouseItemsRepositoryContract::class);
        $this->acceptanceItemsRepository = Mockery::mock(AcceptanceItemsRepositoryContract::class);

        $this->app->instance(FifoServiceContract::class, $this->fifoService);
        $this->app->instance(ShipmentsRepositoryContract::class, $this->shipmentsRepository);
        $this->app->instance(WarehouseItemsRepositoryContract::class, $this->warehouseItemsRepository);
        $this->app->instance(AcceptanceItemsRepositoryContract::class, $this->acceptanceItemsRepository);
    }

    public function test_updates_items_when_date_changes(): void
    {
        // Arrange
        $this->newResource = new AcceptanceDto(
            cabinetId: $this->faker()->uuid,
            legalEntityId: $this->faker()->uuid,
            contractorId: $this->faker()->uuid,
            warehouseId: '1',
            departmentId: $this->faker()->uuid,
            employeeId: $this->faker()->uuid,
            userId: 1,
            currencyId: $this->faker()->uuid,
            currencyValue: 1.0,
            dateFrom: '2024-01-02',
            held: true,
            resourceId: $this->oldResource->id
        );

        $items = collect([
            (object)[
                'product_id' => $this->faker()->uuid
            ]
        ]);

        $shipmentItem = $this->faker()->uuid;

        $this->acceptanceItemsRepository
            ->shouldReceive('get')
            ->once()
            ->with($this->oldResource->id)
            ->andReturn($items);

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) use ($items) {
                return $data['received_at'] === '2024-01-02'
                    && $data['warehouse_id'] === '1'
                    && $id === $this->oldResource->id
                    && $productId === $items[0]->product_id;
            });

        $this->shipmentsRepository
            ->shouldReceive('findShipmentItemIdByProductAndDate')
            ->once()
            ->with($items[0]->product_id, '2024-01-01')
            ->andReturn($shipmentItem);

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($shipmentItem);

        Queue::fake();

        // Act
        $job = new RecalculationAfterUpdateAcceptanceJob($this->oldResource, $this->newResource);
        $job->handle();

        // Assert
        Queue::assertPushed(RecalculateAcceptanceItemsRecidualJob::class, function ($job) use ($items) {
            return get_class($job) === RecalculateAcceptanceItemsRecidualJob::class
                && $this->getJobConstructorParams($job) === [
                    'acceptance' => $this->oldResource,
                    'productId' => $items[0]->product_id
                ];
        });
    }

    public function test_updates_items_when_warehouse_changes(): void
    {
        // Arrange
        $this->newResource = new AcceptanceDto(
            cabinetId: $this->faker()->uuid,
            legalEntityId: $this->faker()->uuid,
            contractorId: $this->faker()->uuid,
            warehouseId: '2',
            departmentId: $this->faker()->uuid,
            employeeId: $this->faker()->uuid,
            userId: 1,
            currencyId: $this->faker()->uuid,
            currencyValue: 1.0,
            dateFrom: '2024-01-01',
            held: true,
            resourceId: $this->oldResource->id
        );

        $items = collect([
            (object)[
                'product_id' => $this->faker()->uuid
            ]
        ]);

        $shipmentItem = $this->faker()->uuid;

        $this->acceptanceItemsRepository
            ->shouldReceive('get')
            ->once()
            ->with($this->oldResource->id)
            ->andReturn($items);

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) use ($items) {
                return $data['received_at'] === '2024-01-01'
                    && $data['warehouse_id'] === '2'
                    && $id === $this->oldResource->id
                    && $productId === $items[0]->product_id;
            });

        $this->shipmentsRepository
            ->shouldReceive('findShipmentItemIdByProductAndDate')
            ->once()
            ->with($items[0]->product_id, '2024-01-01')
            ->andReturn($shipmentItem);

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($shipmentItem);

        Queue::fake();

        // Act
        $job = new RecalculationAfterUpdateAcceptanceJob($this->oldResource, $this->newResource);
        $job->handle();

        // Assert
        Queue::assertPushed(RecalculateAcceptanceItemsRecidualJob::class, function ($job) use ($items) {
            return get_class($job) === RecalculateAcceptanceItemsRecidualJob::class
                && $this->getJobConstructorParams($job) === [
                    'acceptance' => $this->oldResource,
                    'productId' => $items[0]->product_id
                ];
        });
    }

    public function test_updates_items_when_held_changes(): void
    {
        // Arrange
        $this->newResource = new AcceptanceDto(
            cabinetId: $this->faker()->uuid,
            legalEntityId: $this->faker()->uuid,
            contractorId: $this->faker()->uuid,
            warehouseId: '1',
            departmentId: $this->faker()->uuid,
            employeeId: $this->faker()->uuid,
            userId: 1,
            currencyId: $this->faker()->uuid,
            currencyValue: 1.0,
            dateFrom: '2024-01-01',
            held: false,
            resourceId: $this->oldResource->id
        );

        $items = collect([
            (object)[
                'product_id' => $this->faker()->uuid
            ]
        ]);

        $shipmentItem = $this->faker()->uuid;

        $this->acceptanceItemsRepository
            ->shouldReceive('get')
            ->once()
            ->with($this->oldResource->id)
            ->andReturn($items);

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) use ($items) {
                return $data['received_at'] === '2024-01-01'
                    && $data['warehouse_id'] === '1'
                    && $id === $this->oldResource->id
                    && $productId === $items[0]->product_id;
            });

        $this->shipmentsRepository
            ->shouldReceive('findShipmentItemIdByProductAndDate')
            ->once()
            ->with($items[0]->product_id, '2024-01-01')
            ->andReturn($shipmentItem);

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($shipmentItem);

        Queue::fake();

        // Act
        $job = new RecalculationAfterUpdateAcceptanceJob($this->oldResource, $this->newResource);
        $job->handle();

        // Assert
        Queue::assertPushed(RecalculateAcceptanceItemsRecidualJob::class, function ($job) use ($items) {
            return get_class($job) === RecalculateAcceptanceItemsRecidualJob::class
                && $this->getJobConstructorParams($job) === [
                    'acceptance' => $this->oldResource,
                    'productId' => $items[0]->product_id
                ];
        });
    }

    public function test_skips_update_when_nothing_changes(): void
    {
        // Arrange
        $this->newResource = new AcceptanceDto(
            cabinetId: $this->faker()->uuid,
            legalEntityId: $this->faker()->uuid,
            contractorId: $this->faker()->uuid,
            warehouseId: '1',
            departmentId: $this->faker()->uuid,
            employeeId: $this->faker()->uuid,
            userId: 1,
            currencyId: $this->faker()->uuid,
            currencyValue: 1.0,
            dateFrom: '2024-01-01',
            held: true,
            resourceId: $this->oldResource->id
        );

        $this->acceptanceItemsRepository->shouldNotReceive('get');
        $this->warehouseItemsRepository->shouldNotReceive('update');
        $this->shipmentsRepository->shouldNotReceive('findShipmentItemIdByProductAndDate');
        $this->fifoService->shouldNotReceive('handle');

        Queue::fake();

        // Act
        $job = new RecalculationAfterUpdateAcceptanceJob($this->oldResource, $this->newResource);
        $job->handle();

        // Assert
        Queue::assertNotPushed(RecalculateAcceptanceItemsRecidualJob::class);
    }

    private function getJobConstructorParams($job): array
    {
        $reflection = new \ReflectionClass($job);
        $constructor = $reflection->getConstructor();
        $params = [];

        foreach ($constructor->getParameters() as $param) {
            $property = $reflection->getProperty($param->getName());
            $property->setAccessible(true);
            $params[$param->getName()] = $property->getValue($job);
        }

        return $params;
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
