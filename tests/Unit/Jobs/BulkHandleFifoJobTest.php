<?php

namespace Tests\Unit\Jobs;

use App\Jobs\BulkHandleFifoJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\Services\Internal\FifoServiceContract;
use Illuminate\Support\Collection;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;
use Illuminate\Contracts\Container\BindingResolutionException;

class BulkHandleFifoJobTest extends TestCase
{
    use RefreshDatabase;

    use WithFaker;

    private Collection $shipmentItems;
    private FifoServiceContract $fifoService;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем тестовые данные
        $this->shipmentItems = collect([
            (object)[
                'cabinet_id' => 1,
                'product_id' => 1,
                'shipment_item_id' => 1
            ],
            (object)[
                'cabinet_id' => 1,
                'product_id' => 1,
                'shipment_item_id' => 2
            ],
            (object)[
                'cabinet_id' => 2,
                'product_id' => 1,
                'shipment_item_id' => 3
            ],
            (object)[
                'cabinet_id' => 2,
                'product_id' => 2,
                'shipment_item_id' => 4
            ]
        ]);

        // Мокаем FifoService
        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        app()->instance(FifoServiceContract::class, $this->fifoService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_groups_items_by_cabinet_and_product(): void
    {
        // Arrange
        $this->fifoService->shouldReceive('handleBulk')
            ->with([1, 2], false)
            ->once();

        $this->fifoService->shouldReceive('handleBulk')
            ->with([3], false)
            ->once();

        $this->fifoService->shouldReceive('handleBulk')
            ->with([4], false)
            ->once();

        // Act
        $job = new BulkHandleFifoJob($this->shipmentItems);
        $job->handle();

        $this->assertTrue(true);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_passes_delete_flag_to_service(): void
    {
        // Arrange
        $this->fifoService->shouldReceive('handleBulk')
            ->with([1, 2], true)
            ->once();

        $this->fifoService->shouldReceive('handleBulk')
            ->with([3], true)
            ->once();

        $this->fifoService->shouldReceive('handleBulk')
            ->with([4], true)
            ->once();

        // Act
        $job = new BulkHandleFifoJob($this->shipmentItems, true);
        $job->handle();

        $this->assertTrue(true);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_handles_empty_collection(): void
    {
        // Arrange
        $emptyItems = collect([]);
        $this->fifoService->shouldNotReceive('handleBulk');

        // Act
        $job = new BulkHandleFifoJob($emptyItems);
        $job->handle();

        $this->assertTrue(true);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_handles_single_item(): void
    {
        // Arrange
        $singleItem = collect([
            (object)[
                'cabinet_id' => 1,
                'product_id' => 1,
                'shipment_item_id' => 1
            ]
        ]);

        $this->fifoService->shouldReceive('handleBulk')
            ->with([1], false)
            ->once();

        // Act
        $job = new BulkHandleFifoJob($singleItem);
        $job->handle();

        $this->assertTrue(true);
    }
}
