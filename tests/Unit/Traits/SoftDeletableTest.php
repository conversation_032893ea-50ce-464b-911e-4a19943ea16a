<?php

namespace Tests\Unit\Traits;

use App\Traits\SoftDeletable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\Services\Internal\BinServiceContract;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Mockery;

class SoftDeletableTest extends TestCase
{
    use RefreshDatabase;

    private $testClass;
    private $mockBinService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a concrete class that uses the trait
        $this->testClass = new class () {
            use SoftDeletable;
            public const TABLE = 'test_table';

            public function show(string $id): ?object
            {
                return DB::table(static::TABLE)
                    ->where('id', $id)
                    ->first();
            }
        };

        // Mock BinService
        $this->mockBinService = Mockery::mock(BinServiceContract::class);
        app()->instance(BinServiceContract::class, $this->mockBinService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_performs_soft_delete_when_enabled(): void
    {
        $id = 'test-id';
        $cabinetId = 'cabinet-1';
        $record = (object)[
            'id' => $id,
            'cabinet_id' => $cabinetId,
            'name' => 'Test Record'
        ];

        $this->mockBinService
            ->shouldReceive('shouldUseSoftDelete')
            ->with($cabinetId)
            ->andReturn(true);

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'first' => $record,
                    'update' => 1
                ])
            ]));

        DB::shouldReceive('table')
            ->with('bin_items')
            ->andReturn(Mockery::mock([
                'insert' => true
            ]));

        DB::shouldReceive('transaction')
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        $result = $this->testClass->delete($id);

        $this->assertEquals(1, $result);
    }

    public function test_it_performs_force_delete_when_soft_delete_disabled(): void
    {
        $id = 'test-id';
        $cabinetId = 'cabinet-1';
        $record = (object)[
            'id' => $id,
            'cabinet_id' => $cabinetId
        ];

        $this->mockBinService
            ->shouldReceive('shouldUseSoftDelete')
            ->with($cabinetId)
            ->andReturn(false);

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'first' => $record,
                    'delete' => 1
                ])
            ]));

        $result = $this->testClass->delete($id);

        $this->assertEquals(1, $result);
    }

    public function test_it_performs_bulk_soft_delete_when_enabled(): void
    {
        $ids = ['id-1', 'id-2'];
        $cabinetId = 'cabinet-1';
        $records = collect([
            (object)['id' => 'id-1', 'cabinet_id' => $cabinetId, 'name' => 'Record 1'],
            (object)['id' => 'id-2', 'cabinet_id' => $cabinetId, 'name' => 'Record 2']
        ]);

        $this->mockBinService
            ->shouldReceive('shouldUseSoftDelete')
            ->with($cabinetId)
            ->andReturn(true);

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'whereIn' => Mockery::mock([
                    'get' => $records,
                    'update' => 2
                ])
            ]));

        DB::shouldReceive('table')
            ->with('bin_items')
            ->andReturn(Mockery::mock([
                'insert' => true
            ]));

        DB::shouldReceive('transaction')
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        $result = $this->testClass->bulkDelete($ids);

        $this->assertEquals(1, $result);
    }

    public function test_it_performs_bulk_force_delete_when_soft_delete_disabled(): void
    {
        $ids = ['id-1', 'id-2'];
        $cabinetId = 'cabinet-1';
        $records = collect([
            (object)['id' => 'id-1', 'cabinet_id' => $cabinetId],
            (object)['id' => 'id-2', 'cabinet_id' => $cabinetId]
        ]);

        $this->mockBinService
            ->shouldReceive('shouldUseSoftDelete')
            ->with($cabinetId)
            ->andReturn(false);

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'whereIn' => Mockery::mock([
                    'get' => $records,
                    'delete' => 1
                ])
            ]));

        $result = $this->testClass->bulkDelete($ids);

        $this->assertEquals(1, $result);
    }

    public function test_it_deletes_related_documents_for_document_tables(): void
    {
        $id = 'test-id';

        // Create a class that uses a document table
        $documentClass = new class () {
            use SoftDeletable;
            public const TABLE = 'shipments';

            public function show(string $id): ?object
            {
                return DB::table(static::TABLE)
                    ->where('id', $id)
                    ->first();
            }
        };

        DB::shouldReceive('table')
            ->with('documents')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'delete' => 1
                ])
            ]));

        DB::shouldReceive('table')
            ->with('shipments')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'delete' => 1
                ])
            ]));

        $result = $documentClass->forceDelete($id);

        $this->assertEquals(1, $result);
    }

    public function test_it_returns_false_when_record_not_found(): void
    {
        $id = 'non-existent-id';

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'first' => null
                ])
            ]));

        $result = $this->testClass->show($id);

        $this->assertNull($result);
    }

    public function test_it_returns_zero_when_deleting_non_existent_record(): void
    {
        $id = 'non-existent-id';

        DB::shouldReceive('table')
            ->with('test_table')
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'first' => null
                ])
            ]));

        $result = $this->testClass->delete($id);

        $this->assertEquals(0, $result);
    }
}
