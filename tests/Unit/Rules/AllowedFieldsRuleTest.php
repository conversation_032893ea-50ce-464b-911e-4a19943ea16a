<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use Mockery;
use App\Rules\AllowedFieldsRule;
use App\Entities\BaseEntity;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AllowedFieldsRuleTest extends TestCase
{
    use RefreshDatabase;

    private BaseEntity $entity;
    private AllowedFieldsRule $rule;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем мок сущности с разрешенными полями
        $this->entity = Mockery::mock(BaseEntity::class);
        $this->entity->shouldReceive('getAllowedFields')
            ->andReturn(['id', 'name', 'email', 'profile.address']);

        $this->rule = new AllowedFieldsRule($this->entity);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_validates_single_allowed_field(): void
    {
        $validationPassed = true;
        $this->rule->validate('fields', 'name', function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for allowed field');
    }

    public function test_it_validates_multiple_allowed_fields(): void
    {
        $validationPassed = true;
        $this->rule->validate('fields', 'id,name,email', function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for multiple allowed fields');
    }

    public function test_it_validates_nested_allowed_field(): void
    {
        $validationPassed = true;
        $this->rule->validate('fields', 'profile.address', function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for nested allowed field');
    }

    public function test_it_fails_for_disallowed_field(): void
    {
        $errorMessage = '';
        $this->rule->validate('fields', 'password', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertEquals("The field 'password' is not allowed.", $errorMessage);
    }

    public function test_it_fails_for_disallowed_nested_field(): void
    {
        $errorMessage = '';
        $this->rule->validate('fields', 'profile.password', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertEquals("The field 'profile.password' is not allowed.", $errorMessage);
    }

    public function test_it_fails_for_mixed_fields(): void
    {
        $errorMessage = '';
        $this->rule->validate('fields', 'name,password,email', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertEquals("The field 'password' is not allowed.", $errorMessage);
    }

    public function test_it_handles_empty_value(): void
    {
        $validationPassed = true;
        $this->rule->validate('fields', '', function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for empty value');
    }

    public function test_it_handles_whitespace(): void
    {
        $validationPassed = true;
        $this->rule->validate('fields', ' id , name ', function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass with whitespace');
    }

    public function test_it_validates_case_sensitive_fields(): void
    {
        $errorMessage = '';
        $this->rule->validate('fields', 'NAME', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertEquals("The field 'NAME' is not allowed.", $errorMessage);
    }

    public function test_it_validates_partial_matches(): void
    {
        $errorMessage = '';
        $this->rule->validate('fields', 'name_extra', function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertEquals("The field 'name_extra' is not allowed.", $errorMessage);
    }
}
