<?php

namespace App\Enums\Api\Internal;

enum BarcodeEnum: int
{
    case EAN8       = 1;  // EAN8
    case EAN13      = 2;  // EAN13
    case CODE128    = 3;  // CODE128
    case GTIN       = 4;  // GTIN
    case UPC        = 5;  // UPC

    public function getStatus(): string
    {
        return match($this) {
            self::EAN8     =>  __('EAN8'),
            self::EAN13    =>  __('EAN13'),
            self::CODE128  =>  __('Code128'),
            self::GTIN     =>  __('GTIN'),
            self::UPC      =>  __('UPC'),
        };
    }

    public static function fromInt(int $value): ?self
    {
        return self::tryFrom($value);
    }

}
