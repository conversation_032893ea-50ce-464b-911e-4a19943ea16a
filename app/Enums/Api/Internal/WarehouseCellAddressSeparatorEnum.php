<?php

namespace App\Enums\Api\Internal;

enum WarehouseCellAddressSeparatorEnum: string
{
    case DOT = '.';      // Точка
    case DASH = '-';    // Тире
    case LONGDASH = '—'; // Тире
    case SPACE = ' ';  // Пробел
    case NONE = '';    // Нет

    public function getSeparator(): string
    {
        return match($this) {
            self::DOT      =>  '.',
            self::DASH    =>  '-',
            self::LONGDASH     => '—',
            self::SPACE      =>  __('пробел'),
            self::NONE      =>  __('нет'),
        };
    }
}
