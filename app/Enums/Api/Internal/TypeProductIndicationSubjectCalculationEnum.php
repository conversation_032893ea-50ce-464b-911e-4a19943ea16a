<?php

namespace App\Enums\Api\Internal;

enum TypeProductIndicationSubjectCalculationEnum: string
{
    case PRODUCT                            = 'product';
    case EXCISABLE_GOODS                    = 'excisable_goods';
    case INTEGRAL_PART_OF_CALCULATOIN       = 'integral_part_of_calculation';
    case OTHER_SUBJECT_OF_CALCULATOIN       = 'other_subject_of_calculation';

    public function getValue(): string
    {
        return match($this) {
            self::PRODUCT                        =>  __('Товар'),
            self::EXCISABLE_GOODS                =>  __('Подакцизный товар'),
            self::INTEGRAL_PART_OF_CALCULATOIN   =>  __('Составной предмет расчёта'),
            self::OTHER_SUBJECT_OF_CALCULATOIN   =>  __('Иной предмет расчёта'),
        };
    }
}
