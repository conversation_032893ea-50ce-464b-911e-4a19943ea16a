<?php

namespace App\Enums\Api\Internal;

enum AgeCategoryEnum: string
{
    case ADULT          = 'adult';          // Взрослое
    case CHILD          = 'child';          // Детское
    case WITHOUT_AGE    = 'without_age';    // Без возраста

    public function getAgeCategory(): string
    {
        return match($this) {
            self::ADULT         =>  __('Взрослое'),
            self::CHILD         =>  __('Детское'),
            self::WITHOUT_AGE   =>  __('Без возраста'),
        };
    }
}
