<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WildberriesOrderDeliveryInfo extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'wildberries_order_id',
        'full_address',
        'longitude',
        'latitude',
        'post_code',
        'country',
        'region',
        'city',
        'street',
        'house',
        'apartment',
    ];

    /**
     * Получить заказ
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(WildberriesOrder::class, 'wildberries_order_id');
    }
}
