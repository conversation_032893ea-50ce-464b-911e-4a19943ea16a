<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class LegalEntity extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'cabinet_id',
        'short_name',
        'code',
        'phone',
        'fax',
        'email',
        'vat',
        'discount_card',
    ];

    public function address(): HasOne
    {
        return $this->hasOne(LegalAddress::class);
    }

    public function head(): HasOne
    {
        return $this->hasOne(LegalHead::class);
    }

    public function detail(): HasOne
    {
        return $this->hasOne(LegalDetail::class);
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(LegalAccount::class);
    }
}
