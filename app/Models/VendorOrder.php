<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VendorOrder extends Model
{
    use HasFactory;
    use HasUuids;
    use SoftDeletes;

    protected $fillable = [
        'cabinet_id',
        'number',
        'date_from',
        'status_id',
        'held',
        'waiting',
        'legal_entity_id',
        'contractor_id',
        'plan_date',
        'warehouse_id',
        'total_price',
        'employee_id',
        'department_id',
        'is_default',
        'is_common',
        'comment',
        'has_vat',
        'price_includes_vat'
    ];

    protected $casts = [
        'date_from' => 'datetime',
        'plan_date' => 'date',
        'held' => 'boolean',
        'waiting' => 'boolean',
        'is_default' => 'boolean',
        'is_common' => 'boolean',
        'has_vat' => 'boolean',
        'price_includes_vat' => 'boolean',
    ];
}
