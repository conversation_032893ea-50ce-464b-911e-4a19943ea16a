<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    use HasFactory;
    use HasUuids;


    protected $fillable = [
        'cabinet_id', 'employee_id', 'number',
        'date_from', 'status_id', 'held',
        'legal_entity_id', 'contractor_id',
        'warehouse_id', 'sales_channel_id',
        'currency_id', 'consignee_id',
        'transporter_id', 'cargo_name',
        'shipper_instructions','venicle',
        'venicle_number',
        'total_seats','goverment_contract_id',
        'comment','price_includes_vat',
        'overhead_cost','total_cost','profit'
    ];
}
