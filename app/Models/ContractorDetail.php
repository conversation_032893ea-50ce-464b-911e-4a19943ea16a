<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractorDetail extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'contractor_id',
        'taxation_type',
        'tax_rate',
        'vat_rate',
        'type',
        'inn',
        'kpp',
        'ogrn',
        'okpo',
        'full_name',
        'firstname',
        'patronymic',
        'lastname',
        'ogrnip',
        'certificate_number',
        'certificate_date',
        'postcode',
        'country_id',
        'region',
        'city',
        'street',
        'house',
        'office',
        'other',
        'address_comment',
    ];
}
