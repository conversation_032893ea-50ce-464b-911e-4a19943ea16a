<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WarehouseGroup extends Model
{
    /** @use HasFactory<\Database\Factories\WarehouseGroupFactory> */
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'name',
        'cabinet_id',
        'parent_id'
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(WarehouseGroup::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(WarehouseGroup::class, 'parent_id');
    }

    public function warehouses(): HasMany
    {
        return $this->hasMany(Warehouse::class, 'group_id');
    }
}
