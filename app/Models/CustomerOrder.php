<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerOrder extends Model
{
    use HasFactory;
    use HasUuids;
    use SoftDeletes;

    protected $fillable = [
        'cabinet_id',
        'employee_id',
        'department_id',
        'is_common',
        'number',
        'date_from',
        'payment_status',
        'status_id',
        'held',
        'reserve',
        'legal_entity_id',
        'contractor_id',
        'plan_date',
        'sales_channel_id',
        'warehouse_id',
        'currency_id',
        'currency_value',
        'total_price',
        'comment',
        'has_vat',
        'price_includes_vat',
    ];

    protected $casts = [
        'has_vat' => 'boolean',
        'price_includes_vat' => 'boolean',
    ];
}
