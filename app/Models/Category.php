<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Category extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'product_categories';
    protected $fillable = [
        'cabinet_id',
        'parent_id',
        'name',
        'created_at',
        'updated_at',
    ];


    /**
     *  @return HasMany<Category>
     */
    public function childrenCategories(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }

    /**
     *  @return HasMany<Product>
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
    * @return BelongsTo<Cabinet, Category>
    */
    public function cabinet(): BelongsTo
    {
        return $this->belongsTo(Cabinet::class, 'foreign_key');
    }

    /**
    * @return BelongsToMany<Attribute>
    */
    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'attribute_category', 'category_id', 'attribute_id');
    }

}
