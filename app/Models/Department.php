<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Department extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'departments';
    public $timestamps = false;

    /**
     * @var array<int, string>
     */
    protected $fillable = [
        'cabinet_id',
        'title',
        'sorting',
    ];

    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'department_permission', 'departament_id', 'permission_id');
    }


}
