<?php

namespace App\Providers;

use Dedoc\Scramble\Scramble;
use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Route;

class ScrambleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Scramble::routes(function (Route $route) {
            // Включаем только внутренние API маршруты
            return str_starts_with($route->uri, 'api/internal/');
        });

        // Настройка для лучшего распознавания типов
        Scramble::extendOpenApi(function ($openApi) {
            $openApi->secure(
                \Dedoc\Scramble\Support\Generator\SecurityScheme::http('bearer', 'JWT')
            );
        });
    }
} 