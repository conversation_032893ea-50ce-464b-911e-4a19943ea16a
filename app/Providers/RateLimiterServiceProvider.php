<?php

namespace App\Providers;

use App\Clients\WB\APIToken;
use Illuminate\Support\ServiceProvider;
use App\Services\Api\RateLimiter\RateLimiterService;

class RateLimiterServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(RateLimiterService::class, function ($app) {
            // Определяем, находимся ли мы в тестовом режиме
            $isTest = false;
            
            // Если есть токен в конфигурации, проверяем, является ли он тестовым
            if ($app->has('config') && $app->make('config')->has('wildberries.token')) {
                $token = $app->make('config')->get('wildberries.token');
                if ($token) {
                    try {
                        $apiToken = new APIToken($token);
                        $isTest = $apiToken->isTest();
                    } catch (\Exception $e) {
                        // Игнорируем ошибки при создании APIToken
                    }
                }
            }
            
            return new RateLimiterService($isTest);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
