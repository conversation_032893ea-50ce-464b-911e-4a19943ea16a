<?php

namespace App\Extensions\Scramble;

use <PERSON>doc\Scramble\Extensions\OperationExtension;
use <PERSON>doc\Scramble\Infer\Scope\Scope;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\Types\ArrayType;
use Dedoc\Scramble\Support\Generator\Types\BooleanType;
use Dedoc\Scramble\Support\Generator\Types\StringType;
use Dedoc\Scramble\Support\Generator\Types\Type;
use Dedoc\Scramble\Support\RouteInfo;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use ReflectionClass;
use ReflectionMethod;

class ExcludeIfRuleExtension extends OperationExtension
{
    public function handle(Operation $operation, RouteInfo $routeInfo, Scope $scope): void
    {
        $requestClass = $routeInfo->getFormRequestClass();
        
        if (!$requestClass || !class_exists($requestClass)) {
            return;
        }

        try {
            $reflection = new ReflectionClass($requestClass);
            $rulesMethod = $reflection->getMethod('rules');
            
            if (!$rulesMethod->isPublic()) {
                return;
            }

            $request = $reflection->newInstance();
            if (!$request instanceof FormRequest) {
                return;
            }

            $rules = $request->rules();
            $this->processRules($operation, $rules);
            
        } catch (\Exception $e) {
            // Silently fail if we can't process the request
            return;
        }
    }

    private function processRules(Operation $operation, array $rules): void
    {
        foreach ($rules as $field => $rule) {
            if (!is_string($rule) && !is_array($rule)) {
                continue;
            }

            $processedRule = $this->processExcludeIfRule($rule);
            if ($processedRule !== null) {
                $this->addParameterToOperation($operation, $field, $processedRule);
            }
        }
    }

    private function processExcludeIfRule($rule): ?array
    {
        if (is_string($rule)) {
            return $this->parseStringRule($rule);
        }

        if (is_array($rule)) {
            return $this->parseArrayRule($rule);
        }

        return null;
    }

    private function parseStringRule(string $rule): ?array
    {
        // Check if rule contains Rule::excludeIf pattern
        if (!str_contains($rule, 'excludeIf')) {
            return null;
        }

        // Extract base validation rules before excludeIf
        $parts = explode('|', $rule);
        $baseRules = [];
        
        foreach ($parts as $part) {
            $part = trim($part);
            if (!str_contains($part, 'excludeIf') && !empty($part)) {
                $baseRules[] = $part;
            }
        }

        return empty($baseRules) ? null : $baseRules;
    }

    private function parseArrayRule(array $rules): ?array
    {
        $baseRules = [];
        $hasExcludeIf = false;

        foreach ($rules as $rule) {
            if ($rule instanceof \Illuminate\Validation\Rules\ExcludeIf) {
                $hasExcludeIf = true;
                continue;
            }

            if (is_string($rule)) {
                // Check if it's a string rule with excludeIf
                if (str_contains($rule, 'excludeIf')) {
                    $hasExcludeIf = true;
                    $extracted = $this->parseStringRule($rule);
                    if ($extracted) {
                        $baseRules = array_merge($baseRules, $extracted);
                    }
                } else {
                    $baseRules[] = $rule;
                }
            } else {
                $baseRules[] = $rule;
            }
        }

        return $hasExcludeIf && !empty($baseRules) ? $baseRules : null;
    }

    private function addParameterToOperation(Operation $operation, string $field, array $rules): void
    {
        $type = $this->inferTypeFromRules($rules);
        $isRequired = $this->isFieldRequired($rules);
        
        // Handle array fields (ending with .* or containing arrays)
        if (str_ends_with($field, '.*')) {
            $baseField = str_replace('.*', '', $field);
            $this->addArrayParameter($operation, $baseField, $type, $isRequired);
        } else {
            $this->addSimpleParameter($operation, $field, $type, $isRequired);
        }
    }

    private function addArrayParameter(Operation $operation, string $field, Type $itemType, bool $isRequired): void
    {
        $arrayType = new ArrayType();
        $arrayType->items = $itemType;
        
        $parameter = Parameter::make($field, 'query')
            ->setSchema(Schema::fromType($arrayType))
            ->setRequired($isRequired);

        $operation->addParameter($parameter);
    }

    private function addSimpleParameter(Operation $operation, string $field, Type $type, bool $isRequired): void
    {
        $parameter = Parameter::make($field, 'query')
            ->setSchema(Schema::fromType($type))
            ->setRequired($isRequired);

        $operation->addParameter($parameter);
    }

    private function inferTypeFromRules(array $rules): Type
    {
        foreach ($rules as $rule) {
            if (is_string($rule)) {
                if (in_array($rule, ['uuid', 'string'])) {
                    return new StringType();
                }
                if ($rule === 'boolean') {
                    return new BooleanType();
                }
                if (str_starts_with($rule, 'date')) {
                    return new StringType();
                }
            }
        }

        // Default to string type
        return new StringType();
    }

    private function isFieldRequired(array $rules): bool
    {
        foreach ($rules as $rule) {
            if (is_string($rule) && $rule === 'required') {
                return true;
            }
        }
        return false;
    }
}
