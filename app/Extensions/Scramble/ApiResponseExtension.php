<?php

namespace App\Extensions\Scramble;

use <PERSON>doc\Scramble\Extensions\OperationExtension;
use <PERSON>do<PERSON>\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Response;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\RouteInfo;
use Dedoc\Scramble\Support\Type\ArrayType;
use Dedoc\Scramble\Support\Type\ObjectType;
use Dedoc\Scramble\Support\Type\StringType;
use PhpParser\Node;
use PhpParser\Node\Expr\MethodCall;
use PhpParser\NodeFinder;

class ApiResponseExtension extends OperationExtension
{
    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        $methodNode = $routeInfo->methodNode();
        
        if (!$methodNode) {
            return;
        }

        $this->analyzeApiResponseCalls($operation, $methodNode);
    }

    private function analyzeApiResponseCalls(Operation $operation, Node $methodNode): void
    {
        $nodeFinder = new NodeFinder();
        
        // Ищем вызовы методов successResponse, errorResponse, createdResponse
        $methodCalls = $nodeFinder->findInstanceOf($methodNode, MethodCall::class);
        
        foreach ($methodCalls as $methodCall) {
            if (!$methodCall->name instanceof Node\Identifier) {
                continue;
            }
            
            $methodName = $methodCall->name->name;
            
            switch ($methodName) {
                case 'successResponse':
                    $this->handleSuccessResponse($operation, $methodCall);
                    break;
                case 'errorResponse':
                    $this->handleErrorResponse($operation, $methodCall);
                    break;
                case 'createdResponse':
                    $this->handleCreatedResponse($operation, $methodCall);
                    break;
                case 'noContentResponse':
                    $this->handleNoContentResponse($operation);
                    break;
            }
        }
    }

    private function handleSuccessResponse(Operation $operation, MethodCall $methodCall): void
    {
        // Если есть аргументы, первый аргумент - это данные
        if (!empty($methodCall->args)) {
            $dataArg = $methodCall->args[0]->value;
            
            // Если это new SomeResource($data)
            if ($dataArg instanceof Node\Expr\New_) {
                $this->handleResourceResponse($operation, $dataArg, 200);
            }
            // Если это массив или другие данные
            else {
                $this->addGenericSuccessResponse($operation);
            }
        } else {
            $this->addGenericSuccessResponse($operation);
        }
    }

    private function handleResourceResponse(Operation $operation, Node\Expr\New_ $newNode, int $statusCode): void
    {
        if (!$newNode->class instanceof Node\Name) {
            return;
        }

        $resourceClassName = $newNode->class->toString();

        // Создаем схему для ресурса
        $resourceType = new ObjectType($resourceClassName);

        $response = Response::make($statusCode)
            ->description($this->getResponseDescription($statusCode))
            ->setContent('application/json', Schema::fromType($resourceType));

        $operation->addResponse($response);
    }

    private function handleErrorResponse(Operation $operation, MethodCall $methodCall): void
    {
        $statusCode = 500; // По умолчанию

        // Пытаемся извлечь статус код из второго аргумента
        if (count($methodCall->args) >= 2) {
            $statusArg = $methodCall->args[1]->value;
            if ($statusArg instanceof Node\Scalar\LNumber) {
                $statusCode = $statusArg->value;
            }
        }

        $errorType = new ObjectType('ErrorResponse');
        $errorType->addProperty('error', new StringType());
        $errorType->addProperty('details', new ArrayType());

        $response = Response::make($statusCode)
            ->description('Error response')
            ->setContent('application/json', Schema::fromType($errorType));

        $operation->addResponse($response);
    }

    private function handleCreatedResponse(Operation $operation, MethodCall $methodCall): void
    {
        $createdType = new ObjectType('CreatedResponse');

        // Может быть либо id, либо ids
        if (!empty($methodCall->args)) {
            $dataArg = $methodCall->args[0]->value;

            if ($dataArg instanceof Node\Expr\Array_) {
                $createdType->addProperty('ids', new ArrayType());
            } else {
                $createdType->addProperty('id', new StringType());
            }
        } else {
            $createdType->addProperty('id', new StringType());
        }

        $response = Response::make(201)
            ->description('Resource created successfully')
            ->setContent('application/json', Schema::fromType($createdType));

        $operation->addResponse($response);
    }

    private function handleNoContentResponse(Operation $operation): void
    {
        $response = Response::make(204)
            ->description('No content');
            
        $operation->addResponse($response);
    }

    private function addGenericSuccessResponse(Operation $operation): void
    {
        $successType = new ObjectType('SuccessResponse');
        $successType->addProperty('data', new ArrayType());

        $response = Response::make(200)
            ->description('Successful response')
            ->setContent('application/json', Schema::fromType($successType));

        $operation->addResponse($response);
    }

    private function getResponseDescription(int $statusCode): string
    {
        return match ($statusCode) {
            200 => 'Successful response',
            201 => 'Resource created successfully',
            204 => 'No content',
            400 => 'Bad request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not found',
            422 => 'Validation error',
            500 => 'Internal server error',
            default => 'Response',
        };
    }
}
