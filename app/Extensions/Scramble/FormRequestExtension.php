<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Extensions\OperationExtension;
use <PERSON>do<PERSON>\Scramble\Infer;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\TypeTransformer;
use Dedoc\Scramble\Support\Generator\Types\ArrayType;
use Dedoc\Scramble\Support\Generator\Types\BooleanType;
use Dedoc\Scramble\Support\Generator\Types\IntegerType;
use Dedoc\Scramble\Support\Generator\Types\ObjectType;
use Dedoc\Scramble\Support\Generator\Types\StringType;
use Dedoc\Scramble\Support\Generator\Types\Type;

use Dedoc\Scramble\Support\RouteInfo;
use Dedoc\Scramble\GeneratorConfig;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FormRequestExtension extends OperationExtension
{
    public function __construct(
        protected Infer $infer,
        protected TypeTransformer $openApiTransformer,
        protected GeneratorConfig $config
    ) {
        parent::__construct($infer, $openApiTransformer, $config);
    }

    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        // Try to get FormRequest class from method parameters
        $requestClass = $this->getFormRequestClass($routeInfo);

        if (!$requestClass || !class_exists($requestClass)) {
            return;
        }



        try {
            // Try to create instance with dependencies
            $request = app($requestClass);

            if (!$request instanceof FormRequest) {
                return;
            }

            $rules = $request->rules();



            $this->processValidationRules($operation, $rules);

        } catch (\Exception $e) {
            // If app() fails, try reflection to create instance manually
            try {
                $reflection = new \ReflectionClass($requestClass);
                $constructor = $reflection->getConstructor();

                if ($constructor && $constructor->getNumberOfRequiredParameters() > 0) {
                    // Try to resolve constructor dependencies
                    $dependencies = [];
                    foreach ($constructor->getParameters() as $parameter) {
                        $type = $parameter->getType();
                        if ($type && $type instanceof \ReflectionNamedType) {
                            $className = $type->getName();
                            if (class_exists($className)) {
                                $dependencies[] = app($className);
                            } else {
                                // Skip if we can't resolve dependency
                                return;
                            }
                        }
                    }
                    $request = $reflection->newInstanceArgs($dependencies);
                } else {
                    $request = $reflection->newInstance();
                }

                if (!$request instanceof FormRequest) {
                    return;
                }

                $rules = $request->rules();
                $this->processValidationRules($operation, $rules);

            } catch (\Exception $innerE) {
                // Silently fail if we can't process the request
                return;
            }
        }
    }

    private function getFormRequestClass(RouteInfo $routeInfo): ?string
    {
        try {
            // Get method reflection
            $reflectionMethod = $routeInfo->reflectionMethod();

            if (!$reflectionMethod) {
                return null;
            }

            // Check method parameters for FormRequest
            foreach ($reflectionMethod->getParameters() as $parameter) {
                $type = $parameter->getType();

                if ($type && $type instanceof \ReflectionNamedType) {
                    $className = $type->getName();

                    if (class_exists($className) && is_subclass_of($className, FormRequest::class)) {
                        return $className;
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function processValidationRules(Operation $operation, array $rules): void
    {
        // Group rules by their root object (e.g., 'filters')
        $groupedRules = [];
        $processedFields = [];

        foreach ($rules as $fieldName => $fieldRules) {
            $processedRules = $this->extractBaseRules($fieldRules);

            if (!empty($processedRules)) {
                // Parse nested field structure
                $parts = explode('.', $fieldName);
                if (count($parts) > 1) {
                    // Special handling for fields.* pattern
                    if (count($parts) == 2 && $parts[1] === '*') {
                        // This is a simple array field like 'fields.*', treat as regular parameter
                        $arrayType = new ArrayType();
                        $itemType = $this->determineTypeFromRules($processedRules);

                        // Check for custom rules in the original field rules
                        $originalRules = $rules[$fieldName];
                        if (is_array($originalRules)) {
                            foreach ($originalRules as $rule) {
                                if (is_object($rule)) {
                                    $customRuleString = $this->handleCustomRule($rule);
                                    if ($customRuleString && str_starts_with($customRuleString, 'in:')) {
                                        // Extract enum values and apply to item type
                                        $enumValues = explode(',', substr($customRuleString, 3));
                                        if ($itemType instanceof StringType) {
                                            $itemType->enum = $enumValues;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        $arrayType->items = $itemType;
                        $this->createParameterFromTypeWithReplace($operation, $parts[0], $arrayType);
                        $processedFields[] = $fieldName;
                    } else {
                        // Complex nested field like 'filters.warehouses.value.*'
                        $rootKey = $parts[0]; // e.g., 'filters'
                        if (!isset($groupedRules[$rootKey])) {
                            $groupedRules[$rootKey] = [];
                        }
                        $groupedRules[$rootKey][$fieldName] = $processedRules;
                        $processedFields[] = $fieldName;
                    }
                } else {
                    // Simple field, create as regular parameter
                    $this->createParameterFromRules($operation, $fieldName, $processedRules);
                    $processedFields[] = $fieldName;
                }
            }
        }

        // Create nested object parameters
        foreach ($groupedRules as $rootKey => $nestedRules) {
            $this->createNestedObjectParameter($operation, $rootKey, $nestedRules);
        }
    }

    private function containsExcludeIf($rules): bool
    {
        if (is_string($rules)) {
            return str_contains($rules, 'excludeIf') || str_ends_with($rules, '|');
        }

        if (is_array($rules)) {
            foreach ($rules as $rule) {
                if ($rule instanceof \Illuminate\Validation\Rules\ExcludeIf) {
                    return true;
                }
                if (is_string($rule) && str_contains($rule, 'excludeIf')) {
                    return true;
                }
            }
        }

        return false;
    }

    private function createNestedObjectParameter(Operation $operation, string $rootKey, array $nestedRules): void
    {
        // Check if parameter already exists and remove it
        $existingIndex = null;
        foreach ($operation->parameters as $index => $param) {
            if ($param->name === $rootKey) {
                $existingIndex = $index;
                break;
            }
        }

        if ($existingIndex !== null) {
            unset($operation->parameters[$existingIndex]);
            $operation->parameters = array_values($operation->parameters); // Re-index array
        }

        // Build nested object structure
        $objectType = new ObjectType();
        $properties = [];

        foreach ($nestedRules as $fullFieldName => $rules) {
            $parts = explode('.', $fullFieldName);
            array_shift($parts); // Remove root key

            // Remove trailing '*' if present and mark as array
            $isArray = false;
            if (end($parts) === '*') {
                array_pop($parts); // Remove '*'
                $isArray = true;
            }

            $this->addNestedProperty($properties, $parts, $rules, $fullFieldName, $isArray);
        }

        // Convert properties array to ObjectType properties
        foreach ($properties as $propName => $propData) {
            $objectType->addProperty($propName, $this->buildPropertyType($propData));
        }

        // Create parameter
        $parameter = Parameter::make($rootKey, 'query')
            ->setSchema(Schema::fromType($objectType));

        $operation->parameters[] = $parameter;
    }

    private function addNestedProperty(array &$properties, array $parts, array $rules, string $originalFieldName = '', bool $isArrayField = false): void
    {
        if (empty($parts)) {
            return;
        }

        $currentPart = array_shift($parts);

        if (empty($parts)) {
            // Leaf node - set the actual type
            $type = $this->determineTypeFromRules($rules);
            if ($isArrayField) {
                $arrayType = new ArrayType();
                $arrayType->items = $type;
                $properties[$currentPart] = ['type' => $arrayType, 'rules' => $rules];
            } else {
                $properties[$currentPart] = ['type' => $type, 'rules' => $rules];
            }
        } else {
            // Intermediate node - create nested object
            if (!isset($properties[$currentPart])) {
                $properties[$currentPart] = ['type' => 'object', 'properties' => []];
            }

            // Ensure properties array exists
            if (!isset($properties[$currentPart]['properties']) || !is_array($properties[$currentPart]['properties'])) {
                $properties[$currentPart]['properties'] = [];
            }

            $this->addNestedProperty($properties[$currentPart]['properties'], $parts, $rules, $originalFieldName, $isArrayField);
        }
    }

    private function buildPropertyType($propData): Type
    {
        if (isset($propData['type']) && $propData['type'] instanceof Type) {
            return $propData['type'];
        }

        if ($propData['type'] === 'object') {
            $objectType = new ObjectType();
            foreach ($propData['properties'] as $propName => $nestedPropData) {
                $objectType->addProperty($propName, $this->buildPropertyType($nestedPropData));
            }
            return $objectType;
        }

        return new StringType();
    }

    private function extractBaseRules($rules): array
    {
        if (is_string($rules)) {
            return $this->extractFromStringRules($rules);
        }

        if (is_array($rules)) {
            return $this->extractFromArrayRules($rules);
        }

        return [];
    }

    private function extractFromStringRules(string $rules): array
    {
        // Handle the pattern: 'uuid|' . Rule::excludeIf(...)
        if (preg_match('/^([^|]+)\|.*excludeIf/i', $rules, $matches)) {
            return [trim($matches[1])];
        }

        // Handle the pattern where string ends with | (concatenated with Rule)
        if (str_ends_with($rules, '|')) {
            $baseRule = rtrim($rules, '|');
            if (!empty($baseRule)) {
                return [$baseRule];
            }
        }

        // Handle normal string rules
        $parts = explode('|', $rules);
        $baseRules = [];

        foreach ($parts as $part) {
            $part = trim($part);

            // Skip any part that contains excludeIf
            if (str_contains($part, 'excludeIf')) {
                continue;
            }

            if (!empty($part)) {
                $baseRules[] = $part;
            }
        }

        return $baseRules;
    }

    private function extractFromArrayRules(array $rules): array
    {
        $baseRules = [];

        foreach ($rules as $rule) {
            // Skip Rule::excludeIf instances
            if ($this->isExcludeIfRule($rule)) {
                continue;
            }

            // Handle string rules that might contain excludeIf
            if (is_string($rule)) {
                if (str_contains($rule, 'excludeIf')) {
                    $extracted = $this->extractFromStringRules($rule);
                    $baseRules = array_merge($baseRules, $extracted);
                } else {
                    $baseRules[] = $rule;
                }
                continue;
            }

            // Handle Rule::in() and other Rule instances
            if ($this->isRuleInstance($rule)) {
                $ruleString = $this->convertRuleToString($rule);
                if (!empty($ruleString)) {
                    $baseRules[] = $ruleString;
                }
                continue;
            }

            // Handle custom rule objects
            if (is_object($rule)) {
                $customRuleString = $this->handleCustomRule($rule);
                if ($customRuleString) {
                    $baseRules[] = $customRuleString;
                } else {
                    $baseRules[] = 'string'; // Default for custom rules
                }
                continue;
            }

            // Handle other rule types
            if (!empty($rule)) {
                $baseRules[] = $rule;
            }
        }

        return array_filter($baseRules);
    }

    private function isExcludeIfRule($rule): bool
    {
        return $rule instanceof \Illuminate\Validation\Rules\ExcludeIf;
    }

    private function isRuleInstance($rule): bool
    {
        return is_object($rule) && method_exists($rule, '__toString');
    }

    private function convertRuleToString($rule): string
    {
        // Handle Rule::in() specifically
        if ($rule instanceof \Illuminate\Validation\Rules\In) {
            $reflection = new \ReflectionClass($rule);
            $valuesProperty = $reflection->getProperty('values');
            $valuesProperty->setAccessible(true);
            $values = $valuesProperty->getValue($rule);

            if (is_array($values)) {
                // Convert enum values to strings
                $stringValues = array_map(function($value) {
                    if (is_object($value)) {
                        // Handle Laravel enum cases (UnitEnum)
                        if ($value instanceof \UnitEnum) {
                            if ($value instanceof \BackedEnum) {
                                return $value->value; // For backed enums
                            } else {
                                return $value->name; // For pure enums
                            }
                        }
                        // Handle other enum-like objects with value property
                        if (method_exists($value, 'value')) {
                            return $value->value;
                        }
                        // Handle objects with __toString
                        if (method_exists($value, '__toString')) {
                            return $value->__toString();
                        }
                        // Last resort - try to get a meaningful string representation
                        if (property_exists($value, 'value')) {
                            return $value->value;
                        }
                        return 'unknown';
                    }
                    return (string) $value;
                }, $values);
                return 'in:' . implode(',', $stringValues);
            }
        }

        if (method_exists($rule, '__toString')) {
            return (string) $rule;
        }

        return 'string';
    }

    private function createParameterFromRules(Operation $operation, string $fieldName, array $rules): void
    {
        $type = $this->determineTypeFromRules($rules);
        $isRequired = $this->isRequired($rules);
        $isArray = $this->isArrayField($fieldName);

        if ($isArray) {
            $this->addArrayParameter($operation, $fieldName, $type, $isRequired);
        } else {
            $this->addParameter($operation, $fieldName, $type, $isRequired);
        }
    }

    private function createParameterFromType(Operation $operation, string $fieldName, Type $type): void
    {
        $parameter = Parameter::make($fieldName, 'query')
            ->setSchema(Schema::fromType($type));

        $operation->parameters[] = $parameter;
    }

    private function createParameterFromTypeWithReplace(Operation $operation, string $fieldName, Type $type): void
    {
        // Check if parameter already exists and remove it
        $existingIndex = null;
        foreach ($operation->parameters as $index => $param) {
            if ($param->name === $fieldName) {
                $existingIndex = $index;
                break;
            }
        }

        if ($existingIndex !== null) {
            unset($operation->parameters[$existingIndex]);
            $operation->parameters = array_values($operation->parameters); // Re-index array
        }

        $parameter = Parameter::make($fieldName, 'query')
            ->setSchema(Schema::fromType($type));

        $operation->parameters[] = $parameter;
    }

    private function determineTypeFromRules(array $rules): Type
    {
        $isNullable = false;
        $isArray = false;
        $enumValues = [];
        $baseType = null;
        $format = null;
        $minimum = null;
        $maximum = null;

        foreach ($rules as $rule) {
            $ruleString = is_string($rule) ? $rule : (string) $rule;

            // Check for nullable
            if (str_contains($ruleString, 'nullable')) {
                $isNullable = true;
            }

            // Check for array
            if (str_contains($ruleString, 'array')) {
                $isArray = true;
            }

            // Check for integer
            if (str_contains($ruleString, 'integer')) {
                $baseType = new IntegerType();
            }

            // Check for boolean
            if (str_contains($ruleString, 'boolean')) {
                $baseType = new BooleanType();
            }

            // Check for UUID
            if (str_contains($ruleString, 'uuid') || str_contains($ruleString, 'UUID')) {
                $baseType = new StringType();
                $format = 'uuid';
            }

            // Check for string (if no other type found)
            if (str_contains($ruleString, 'string') && !$baseType) {
                $baseType = new StringType();
            }

            // Check for date fields
            if (str_contains($ruleString, 'date_format') || str_contains($ruleString, 'date')) {
                $baseType = new StringType();
                if (str_contains($ruleString, 'date_format:d.m.Y H:i')) {
                    $format = 'date-time';
                } else {
                    $format = 'date';
                }
            }

            // Check for enum values (in:value1,value2)
            if (preg_match('/in:([^|]+)/', $ruleString, $matches)) {
                $enumValues = array_map('trim', explode(',', $matches[1]));
            }

            // Check for min/max values
            if (preg_match('/min:(\d+)/', $ruleString, $matches)) {
                $minimum = (int) $matches[1];
            }
            if (preg_match('/max:(\d+)/', $ruleString, $matches)) {
                $maximum = (int) $matches[1];
            }
        }

        // Default to string if no type found
        if (!$baseType) {
            $baseType = new StringType();
        }

        // Apply format
        if ($format && $baseType instanceof StringType) {
            $baseType->format = $format;
        }

        // Apply min/max for integers
        if ($baseType instanceof IntegerType) {
            if ($minimum !== null) {
                $baseType->minimum = $minimum;
            }
            if ($maximum !== null) {
                $baseType->maximum = $maximum;
            }
        }

        // Apply enum values
        if (!empty($enumValues) && $baseType instanceof StringType) {
            $baseType->enum = $enumValues;
        }

        // Handle array type
        if ($isArray) {
            $arrayType = new ArrayType();
            $arrayType->items = new StringType(); // Default array items to string
            $baseType = $arrayType;
        }

        // Handle nullable by setting nullable property
        if ($isNullable && property_exists($baseType, 'nullable')) {
            $baseType->nullable = true;
        }

        return $baseType;
    }

    private function handleCustomRule($rule): ?string
    {
        // Handle AllowedFieldsRule
        if ($rule instanceof \App\Rules\AllowedFieldsRule) {
            try {
                $reflection = new \ReflectionClass($rule);
                $entityProperty = $reflection->getProperty('entity');
                $entityProperty->setAccessible(true);
                $entity = $entityProperty->getValue($rule);

                if ($entity && method_exists($entity, 'getAllowedFields')) {
                    $allowedFields = $entity->getAllowedFields();
                    if (!empty($allowedFields)) {
                        return 'in:' . implode(',', array_slice($allowedFields, 0, 20)); // Limit to first 20 fields
                    }
                }
            } catch (\Exception $e) {
                // Silently fail
            }
        }

        // Handle ValidSortFieldRule
        if ($rule instanceof \App\Rules\ValidSortFieldRule) {
            try {
                $reflection = new \ReflectionClass($rule);
                $entityProperty = $reflection->getProperty('entity');
                $entityProperty->setAccessible(true);
                $entity = $entityProperty->getValue($rule);

                if ($entity && method_exists($entity, 'getFields')) {
                    $fields = $entity::getFields();
                    if (!empty($fields)) {
                        return 'in:' . implode(',', $fields);
                    }
                }
            } catch (\Exception $e) {
                // Silently fail
            }
        }

        return null;
    }

    private function isRequired(array $rules): bool
    {
        foreach ($rules as $rule) {
            if (is_string($rule) && $rule === 'required') {
                return true;
            }
        }
        return false;
    }

    private function isArrayField(string $fieldName): bool
    {
        return str_ends_with($fieldName, '.*') || str_contains($fieldName, '.*.') || str_ends_with($fieldName, '[]');
    }

    private function addArrayParameter(Operation $operation, string $fieldName, Type $itemType, bool $isRequired): void
    {
        $cleanFieldName = str_replace(['.*', '[]'], '', $fieldName);

        $arrayType = new ArrayType();
        $arrayType->items = $itemType;

        $parameter = Parameter::make($cleanFieldName, 'query')
            ->setSchema(Schema::fromType($arrayType));

        if ($isRequired) {
            $parameter->required = true;
        }

        $operation->parameters[] = $parameter;
    }

    private function addParameter(Operation $operation, string $fieldName, Type $type, bool $isRequired): void
    {
        $parameter = Parameter::make($fieldName, 'query')
            ->setSchema(Schema::fromType($type));

        if ($isRequired) {
            $parameter->required = true;
        }

        $operation->parameters[] = $parameter;
    }
}
