<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Extensions\OperationExtension;
use <PERSON>do<PERSON>\Scramble\Infer\Scope\Scope;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\Types\ArrayType;
use Dedoc\Scramble\Support\Generator\Types\BooleanType;
use Dedoc\Scramble\Support\Generator\Types\StringType;
use Dedoc\Scramble\Support\Generator\Types\Type;
use Dedoc\Scramble\Support\RouteInfo;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RequestRulesExtension extends OperationExtension
{
    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        $requestClass = $routeInfo->getFormRequestClass();
        
        if (!$requestClass || !class_exists($requestClass)) {
            return;
        }

        try {
            $request = app($requestClass);
            
            if (!$request instanceof FormRequest) {
                return;
            }

            $rules = $request->rules();
            $this->processValidationRules($operation, $rules);
            
        } catch (\Exception $e) {
            return;
        }
    }

    private function processValidationRules(Operation $operation, array $rules): void
    {
        foreach ($rules as $fieldName => $fieldRules) {
            $processedRules = $this->extractBaseRules($fieldRules);
            
            if (!empty($processedRules)) {
                $this->createParameterFromRules($operation, $fieldName, $processedRules);
            }
        }
    }

    private function extractBaseRules($rules): array
    {
        if (is_string($rules)) {
            return $this->extractFromStringRules($rules);
        }

        if (is_array($rules)) {
            return $this->extractFromArrayRules($rules);
        }

        return [];
    }

    private function extractFromStringRules(string $rules): array
    {
        // Handle the pattern: 'uuid|' . Rule::excludeIf(...)
        if (preg_match('/^([^|]+)\|.*excludeIf/i', $rules, $matches)) {
            return [trim($matches[1])];
        }

        $parts = explode('|', $rules);
        $baseRules = [];

        foreach ($parts as $part) {
            $part = trim($part);

            // Skip any part that contains excludeIf
            if (str_contains($part, 'excludeIf')) {
                continue;
            }

            if (!empty($part)) {
                $baseRules[] = $part;
            }
        }

        return $baseRules;
    }

    private function extractFromArrayRules(array $rules): array
    {
        $baseRules = [];

        foreach ($rules as $rule) {
            // Skip Rule::excludeIf instances
            if ($this->isExcludeIfRule($rule)) {
                continue;
            }

            // Handle string rules that might contain excludeIf
            if (is_string($rule)) {
                if (str_contains($rule, 'excludeIf')) {
                    $extracted = $this->extractFromStringRules($rule);
                    $baseRules = array_merge($baseRules, $extracted);
                } else {
                    $baseRules[] = $rule;
                }
                continue;
            }

            // Handle Rule::in() and other Rule instances
            if ($this->isRuleInstance($rule)) {
                $ruleString = $this->convertRuleToString($rule);
                if (!empty($ruleString)) {
                    $baseRules[] = $ruleString;
                }
                continue;
            }

            // Handle custom rule objects
            if (is_object($rule)) {
                $baseRules[] = 'string'; // Default for custom rules
                continue;
            }

            // Handle other rule types
            if (!empty($rule)) {
                $baseRules[] = $rule;
            }
        }

        return array_filter($baseRules);
    }

    private function isExcludeIfRule($rule): bool
    {
        return $rule instanceof \Illuminate\Validation\Rules\ExcludeIf;
    }

    private function isRuleInstance($rule): bool
    {
        return is_object($rule) && method_exists($rule, '__toString');
    }

    private function convertRuleToString($rule): string
    {
        if (method_exists($rule, '__toString')) {
            return (string) $rule;
        }

        return 'string';
    }

    private function createParameterFromRules(Operation $operation, string $fieldName, array $rules): void
    {
        $type = $this->determineTypeFromRules($rules);
        $isRequired = $this->isRequired($rules);
        $isArray = $this->isArrayField($fieldName);

        if ($isArray) {
            $this->addArrayParameter($operation, $fieldName, $type, $isRequired);
        } else {
            $this->addParameter($operation, $fieldName, $type, $isRequired);
        }
    }

    private function determineTypeFromRules(array $rules): Type
    {
        foreach ($rules as $rule) {
            $ruleString = is_string($rule) ? $rule : (string) $rule;

            // UUID fields
            if (str_contains($ruleString, 'uuid')) {
                $stringType = new StringType();
                $stringType->format = 'uuid';
                return $stringType;
            }

            // Boolean fields
            if (str_contains($ruleString, 'boolean')) {
                return new BooleanType();
            }

            // Date fields
            if (str_contains($ruleString, 'date_format') || str_contains($ruleString, 'date')) {
                $stringType = new StringType();
                if (str_contains($ruleString, 'date_format:d.m.Y H:i')) {
                    $stringType->format = 'date-time';
                    $stringType->example = '01.01.2024 12:00';
                } else {
                    $stringType->format = 'date';
                }
                return $stringType;
            }

            // String fields
            if (str_contains($ruleString, 'string')) {
                return new StringType();
            }

            // Enum fields (Rule::in)
            if (str_contains($ruleString, 'in:')) {
                return new StringType();
            }
        }

        return new StringType();
    }

    private function isRequired(array $rules): bool
    {
        foreach ($rules as $rule) {
            if (is_string($rule) && $rule === 'required') {
                return true;
            }
        }
        return false;
    }

    private function isArrayField(string $fieldName): bool
    {
        return str_ends_with($fieldName, '.*') || str_contains($fieldName, '.*.') || str_ends_with($fieldName, '[]');
    }

    private function addArrayParameter(Operation $operation, string $fieldName, Type $itemType, bool $isRequired): void
    {
        $cleanFieldName = str_replace(['.*', '[]'], '', $fieldName);
        
        $arrayType = new ArrayType();
        $arrayType->items = $itemType;
        
        $parameter = Parameter::make($cleanFieldName, 'query')
            ->setSchema(Schema::fromType($arrayType))
            ->setRequired($isRequired);

        $operation->addParameter($parameter);
    }

    private function addParameter(Operation $operation, string $fieldName, Type $type, bool $isRequired): void
    {
        $parameter = Parameter::make($fieldName, 'query')
            ->setSchema(Schema::fromType($type))
            ->setRequired($isRequired);

        $operation->addParameter($parameter);
    }
}
