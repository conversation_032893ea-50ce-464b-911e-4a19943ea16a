<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Type\ArrayType;
use Dedoc\Scramble\Support\Type\ObjectType;
use Dedoc\Scramble\Support\Type\StringType;
use Dedoc\Scramble\Support\Type\IntegerType;
use Dedoc\Scramble\Support\Type\BooleanType;

class ResponseSchemas
{
    /**
     * Создает схему для стандартного успешного ответа
     */
    public static function successResponse(): Schema
    {
        $type = new ObjectType('SuccessResponse');
        $type->addProperty('data', new ArrayType());

        return Schema::fromType($type);
    }

    /**
     * Создает схему для ответа с ошибкой
     */
    public static function errorResponse(): Schema
    {
        $type = new ObjectType('ErrorResponse');
        $type->addProperty('error', new StringType());
        $type->addProperty('details', new ArrayType());

        return Schema::fromType($type);
    }

    /**
     * Создает схему для ответа с созданным ресурсом
     */
    public static function createdResponse(): Schema
    {
        $type = new ObjectType('CreatedResponse');
        $type->addProperty('id', new StringType());

        return Schema::fromType($type);
    }

    /**
     * Создает схему для ответа с массивом созданных ресурсов
     */
    public static function createdBulkResponse(): Schema
    {
        $type = new ObjectType('CreatedBulkResponse');
        $type->addProperty('ids', new ArrayType(new StringType()));

        return Schema::fromType($type);
    }
    
    /**
     * Создает схему для пагинированного ответа
     */
    public static function paginatedResponse(Schema $itemSchema): Schema
    {
        $type = new ObjectType('PaginatedResponse');

        $dataType = new ArrayType();
        $dataType->items = $itemSchema->type;

        $type->addProperty('data', $dataType);

        $linksType = new ObjectType('Links');
        $linksType->addProperty('first', new StringType());
        $linksType->addProperty('last', new StringType());
        $linksType->addProperty('prev', new StringType());
        $linksType->addProperty('next', new StringType());

        $type->addProperty('links', $linksType);

        $metaType = new ObjectType('Meta');
        $metaType->addProperty('current_page', new IntegerType());
        $metaType->addProperty('from', new IntegerType());
        $metaType->addProperty('last_page', new IntegerType());
        $metaType->addProperty('path', new StringType());
        $metaType->addProperty('per_page', new IntegerType());
        $metaType->addProperty('to', new IntegerType());
        $metaType->addProperty('total', new IntegerType());

        $type->addProperty('meta', $metaType);

        return Schema::fromType($type);
    }
    
    /**
     * Создает схему для ответа с валидационной ошибкой
     */
    public static function validationErrorResponse(): Schema
    {
        $type = new ObjectType('ValidationErrorResponse');
        $type->addProperty('message', new StringType());

        $errorsType = new ObjectType('ValidationErrors');
        $errorsType->additionalProperties = new ArrayType(new StringType());

        $type->addProperty('errors', $errorsType);

        return Schema::fromType($type);
    }

    /**
     * Создает схему для ответа с ошибкой внешнего ключа
     */
    public static function foreignKeyViolationResponse(): Schema
    {
        $type = new ObjectType('ForeignKeyViolationResponse');
        $type->addProperty('error', new StringType());
        $type->addProperty('code', new StringType());

        $detailsType = new ObjectType('ForeignKeyDetails');
        $detailsType->addProperty('table', new StringType());
        $detailsType->addProperty('constraint', new StringType());

        $type->addProperty('details', $detailsType);
        $type->addProperty('debug', new StringType());

        return Schema::fromType($type);
    }

    /**
     * Создает схему для ответа с ошибкой дублирования записи
     */
    public static function duplicateEntryResponse(): Schema
    {
        $type = new ObjectType('DuplicateEntryResponse');
        $type->addProperty('error', new StringType());
        $type->addProperty('code', new StringType());
        $type->addProperty('fields', new ArrayType(new StringType()));

        return Schema::fromType($type);
    }
}
