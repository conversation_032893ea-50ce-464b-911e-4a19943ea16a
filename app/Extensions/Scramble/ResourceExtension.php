<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Extensions\OperationExtension;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Response;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\RouteInfo;
use Dedoc\Scramble\Support\Type\ObjectType;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use PhpParser\Node;
use PhpParser\Node\Expr\MethodCall;
use PhpParser\Node\Expr\New_;
use PhpParser\NodeFinder;
use ReflectionClass;

class ResourceExtension extends OperationExtension
{
    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        $methodNode = $routeInfo->methodNode();
        
        if (!$methodNode) {
            return;
        }

        $this->analyzeResourceUsage($operation, $methodNode);
    }

    private function analyzeResourceUsage(Operation $operation, Node $methodNode): void
    {
        $nodeFinder = new NodeFinder();
        
        // Ищем создание новых экземпляров ресурсов
        $newExpressions = $nodeFinder->findInstanceOf($methodNode, New_::class);
        
        foreach ($newExpressions as $newExpr) {
            if (!$newExpr->class instanceof Node\Name) {
                continue;
            }
            
            $className = $newExpr->class->toString();
            
            // Проверяем, является ли класс ресурсом
            if ($this->isResourceClass($className)) {
                $this->addResourceResponse($operation, $className);
            }
        }
        
        // Ищем вызовы collection() на ресурсах
        $methodCalls = $nodeFinder->findInstanceOf($methodNode, MethodCall::class);
        
        foreach ($methodCalls as $methodCall) {
            if (!$methodCall->name instanceof Node\Identifier || $methodCall->name->name !== 'collection') {
                continue;
            }
            
            if ($methodCall->var instanceof Node\Name) {
                $resourceClassName = $methodCall->var->toString();
                
                if ($this->isResourceClass($resourceClassName)) {
                    $this->addResourceCollectionResponse($operation, $resourceClassName);
                }
            }
        }
    }

    private function isResourceClass(string $className): bool
    {
        if (!class_exists($className)) {
            // Попробуем добавить namespace App\Http\Resources
            $fullClassName = "\\App\\Http\\Resources\\{$className}";
            if (!class_exists($fullClassName)) {
                return false;
            }
            $className = $fullClassName;
        }
        
        try {
            $reflection = new ReflectionClass($className);
            return $reflection->isSubclassOf(JsonResource::class) || $reflection->isSubclassOf(ResourceCollection::class);
        } catch (\Exception $e) {
            return false;
        }
    }

    private function addResourceResponse(Operation $operation, string $resourceClassName): void
    {
        $resourceType = new ObjectType($resourceClassName);

        // Пытаемся получить структуру ресурса
        $this->enrichResourceType($resourceType, $resourceClassName);

        $response = Response::make(200)
            ->description('Resource response')
            ->setContent('application/json', Schema::fromType($resourceType));

        $operation->addResponse($response);
    }

    private function addResourceCollectionResponse(Operation $operation, string $resourceClassName): void
    {
        $resourceType = new ObjectType("Collection of {$resourceClassName}");

        // Добавляем стандартную структуру коллекции
        $resourceType->addProperty('data', $this->createResourceArrayType($resourceClassName));
        $resourceType->addProperty('links', $this->createLinksType());
        $resourceType->addProperty('meta', $this->createMetaType());

        $response = Response::make(200)
            ->description('Resource collection response')
            ->setContent('application/json', Schema::fromType($resourceType));

        $operation->addResponse($response);
    }

    private function createResourceArrayType(string $resourceClassName): ObjectType
    {
        $arrayType = new ObjectType("Array of {$resourceClassName}");

        // Здесь можно добавить логику для извлечения структуры ресурса

        return $arrayType;
    }

    private function createLinksType(): ObjectType
    {
        $linksType = new ObjectType('Links');
        $linksType->addProperty('first', new \Dedoc\Scramble\Support\Type\StringType());
        $linksType->addProperty('last', new \Dedoc\Scramble\Support\Type\StringType());
        $linksType->addProperty('prev', new \Dedoc\Scramble\Support\Type\StringType());
        $linksType->addProperty('next', new \Dedoc\Scramble\Support\Type\StringType());

        return $linksType;
    }

    private function createMetaType(): ObjectType
    {
        $metaType = new ObjectType('Meta');
        $metaType->addProperty('current_page', new \Dedoc\Scramble\Support\Type\IntegerType());
        $metaType->addProperty('from', new \Dedoc\Scramble\Support\Type\IntegerType());
        $metaType->addProperty('last_page', new \Dedoc\Scramble\Support\Type\IntegerType());
        $metaType->addProperty('path', new \Dedoc\Scramble\Support\Type\StringType());
        $metaType->addProperty('per_page', new \Dedoc\Scramble\Support\Type\IntegerType());
        $metaType->addProperty('to', new \Dedoc\Scramble\Support\Type\IntegerType());
        $metaType->addProperty('total', new \Dedoc\Scramble\Support\Type\IntegerType());

        return $metaType;
    }

    private function enrichResourceType(ObjectType $resourceType, string $resourceClassName): void
    {
        if (!class_exists($resourceClassName)) {
            return;
        }
        
        try {
            $reflection = new ReflectionClass($resourceClassName);
            
            // Пытаемся найти метод toArray
            if ($reflection->hasMethod('toArray')) {
                $toArrayMethod = $reflection->getMethod('toArray');
                
                // Анализируем PHPDoc комментарий метода
                $docComment = $toArrayMethod->getDocComment();
                if ($docComment) {
                    // Здесь можно добавить логику для извлечения информации из PHPDoc
                }
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки
        }
    }
}
