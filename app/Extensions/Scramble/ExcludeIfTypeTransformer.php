<?php

namespace App\Extensions\Scramble;

use <PERSON>doc\Scramble\Infer\Extensions\TypeTransformer;
use <PERSON>doc\Scramble\Infer\Scope\Scope;
use Dedoc\Scramble\Support\Generator\Types\StringType;
use Dedoc\Scramble\Support\Generator\Types\Type;
use Illuminate\Validation\Rules\ExcludeIf;
use PhpParser\Node;

class ExcludeIfTypeTransformer implements TypeTransformer
{
    public function transform(Node $node, Scope $scope): ?Type
    {
        // Handle ExcludeIf rules by returning the base type
        if ($this->isExcludeIfRule($node)) {
            return $this->extractBaseType($node);
        }

        return null;
    }

    private function isExcludeIfRule(Node $node): bool
    {
        // Check if this is a Rule::excludeIf call
        if ($node instanceof Node\Expr\StaticCall) {
            if ($node->class instanceof Node\Name && 
                $node->class->toString() === 'Illuminate\\Validation\\Rule' &&
                $node->name instanceof Node\Identifier &&
                $node->name->name === 'excludeIf') {
                return true;
            }
        }

        return false;
    }

    private function extractBaseType(Node $node): Type
    {
        // For ExcludeIf rules, we'll return a string type as default
        // In a real implementation, you might want to analyze the context
        // to determine the actual base type
        return new StringType();
    }
}
