<?php

namespace App\Listeners;

use App\Events\CabinetCreated;
use App\Jobs\CreateCabinetSettingsJob;
use Illuminate\Support\Facades\Cache;

class CreateCabinetSettings
{
    public function handle(CabinetCreated $event): void
    {
        CreateCabinetSettingsJob::dispatchSync($event->cabinetId, $event->user);

        Cache::delete("permissions_{$event->user->id}");
        Cache::delete("employees_{$event->user->id}");
    }
}
