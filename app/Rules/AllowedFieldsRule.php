<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Entities\BaseEntity;

class AllowedFieldsRule implements ValidationRule
{
    public function __construct(
        private readonly BaseEntity $entity
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Если значение пустое, пропускаем валидацию
        if (empty($value)) {
            return;
        }

        $allowedFields = $this->entity->getAllowedFields();

        foreach (array_map('trim', explode(',', $value)) as $fieldValue) {
            // Проверяем точное совпадение
            if (in_array($fieldValue, $allowedFields, true)) {
                continue;
            }

            // Для вложенных полей проверяем точное совпадение
            if (str_contains($fieldValue, '.')) {
                if (!in_array($fieldValue, $allowedFields, true)) {
                    $fail("The field '{$fieldValue}' is not allowed.");
                }
                continue;
            }

            $fail("The field '{$fieldValue}' is not allowed.");
        }
    }
}
