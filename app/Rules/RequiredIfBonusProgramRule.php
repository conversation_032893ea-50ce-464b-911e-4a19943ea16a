<?php

namespace App\Rules;

use Closure;
use App\Enums\Api\Internal\DiscountTypeEnum;
use Illuminate\Contracts\Validation\ValidationRule;

class RequiredIfBonusProgramRule implements ValidationRule
{
    protected $fields;

    public function __construct(array $fields)
    {
        $this->fields = $fields;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $type = request()->get('type');

        if ($type === DiscountTypeEnum::BONUS_PROGRAM->value) {
            foreach ($this->fields as $field) {
                if (!request()->has($field)) {
                    $fail("The field :field is required when type is BONUS_PROGRAM.")->translate();
                }
            }
        }
    }
}
