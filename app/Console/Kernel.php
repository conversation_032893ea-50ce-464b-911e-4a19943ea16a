<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Синхронизация системных складов Ozon по расписанию из конфигурации
        if (config('ozon.system_warehouses.sync_enabled', true)) {
            $schedule->command('ozon:sync-system-warehouses')
                ->cron(config('ozon.system_warehouses.sync_schedule', '0 */6 * * *'))
                ->withoutOverlapping()
                ->runInBackground()
                ->appendOutputTo(storage_path('logs/ozon-system-warehouses-sync.log'));
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/../Modules/Marketplaces/Console/Commands');

        require base_path('routes/console.php');
    }
}
