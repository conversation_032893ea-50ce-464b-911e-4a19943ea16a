<?php

namespace App\Console\Commands\Ozon;

use App\Actions\Ozon\OzonePerformanceClientStatisticsOrdersGenerateAction as Action;
use Illuminate\Console\Command;

class OzonPerformanceClientStatisticsOrdersGenerateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ozon:performance_statistics_orders_generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Запуск команды для Ozon';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Запуск команды для Ozon Performance statistics orders_generate...');
        $action = new Action();
        $action->handle();
        $this->info('Заполнение таблицы "ozon_performance_client_statistics_orders_generate" завершено.');
    }
}
