<?php

namespace App\Console\Commands;

use App\Extensions\Scramble\RequestRulesExtension;
use App\Http\Requests\Api\Internal\Acceptances\AcceptanceIndexRequest;
use Illuminate\Console\Command;

class TestScrambleExtension extends Command
{
    protected $signature = 'test:scramble-extension';
    protected $description = 'Test the Scramble extension for Rule::excludeIf handling';

    public function handle()
    {
        $this->info('Testing Scramble Extension for Rule::excludeIf...');

        try {
            // Test rule extraction directly without creating extension
            $request = new AcceptanceIndexRequest(app(\App\Entities\AcceptanceEntity::class));
            $rules = $request->rules();

            $this->info("Found " . count($rules) . " validation rules:");

            // Test specific problematic rule
            $testRule = $rules['filters.contractor_owners.value.*'] ?? null;
            if ($testRule) {
                $this->info("Testing rule: filters.contractor_owners.value.*");
                $this->line("Original rule: " . (is_string($testRule) ? $testRule : 'array rule'));

                // Test the rule parsing logic directly
                if (is_string($testRule)) {
                    // Handle the pattern: 'uuid|' . Rule::excludeIf(...)
                    if (preg_match('/^([^|]+)\|.*excludeIf/i', $testRule, $matches)) {
                        $baseRule = trim($matches[1]);
                        $this->line("Extracted base rule: " . $baseRule);
                    }
                }
            }

            $this->info('Extension test completed successfully!');

        } catch (\Exception $e) {
            $this->error('Extension test failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
        }
    }
}
