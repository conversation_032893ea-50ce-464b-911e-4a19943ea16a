<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class Install extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:install';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Installing application';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        Artisan::call('migrate:fresh');
        Artisan::call('cache:clear');
        Artisan::call('app:fill-countries');
        Artisan::call('app:get-currencies');
        Artisan::call('app:fill-measurement-units');
        Artisan::call('app:fill-permissions');
        Artisan::call('app:fill-statuses');
        Artisan::call('db:seed', [
            '--class' => 'DatabaseSeeder',
        ]);
    }
}
