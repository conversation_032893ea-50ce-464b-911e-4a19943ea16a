<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class InspectOperation extends Command
{
    protected $signature = 'inspect:operation';
    protected $description = 'Inspect Operation class';

    public function handle()
    {
        try {
            $reflection = new \ReflectionClass('Dedoc\\Scramble\\Support\\Generator\\Operation');
            
            $this->info('Operation methods:');
            foreach ($reflection->getMethods() as $method) {
                $this->line('- ' . $method->getName());
            }
            
            $this->info('');
            $this->info('Operation properties:');
            foreach ($reflection->getProperties() as $property) {
                $this->line('- ' . $property->getName());
            }
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
