<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\Internal\Procurement\Acceptances\AcceptanceController;
use Illuminate\Console\Command;

class DebugFormRequest extends Command
{
    protected $signature = 'debug:form-request';
    protected $description = 'Debug FormRequest detection';

    public function handle()
    {
        try {
            $reflection = new \ReflectionClass(AcceptanceController::class);
            $indexMethod = $reflection->getMethod('index');
            
            $this->info('Method: ' . $indexMethod->getName());
            
            foreach ($indexMethod->getParameters() as $parameter) {
                $type = $parameter->getType();
                $this->info('Parameter: ' . $parameter->getName());
                
                if ($type && $type instanceof \ReflectionNamedType) {
                    $className = $type->getName();
                    $this->info('Type: ' . $className);
                    
                    if (class_exists($className)) {
                        $this->info('Class exists: YES');
                        
                        if (is_subclass_of($className, \Illuminate\Foundation\Http\FormRequest::class)) {
                            $this->info('Is FormRequest: YES');
                        } else {
                            $this->info('Is FormRequest: NO');
                        }
                    } else {
                        $this->info('Class exists: NO');
                    }
                }
            }
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
