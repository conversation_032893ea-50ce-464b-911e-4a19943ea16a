<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class InspectRouteInfo extends Command
{
    protected $signature = 'inspect:route-info';
    protected $description = 'Inspect RouteInfo class';

    public function handle()
    {
        try {
            $reflection = new \ReflectionClass('Dedoc\\Scramble\\Support\\RouteInfo');
            
            $this->info('RouteInfo methods:');
            foreach ($reflection->getMethods() as $method) {
                $this->line('- ' . $method->getName());
            }
            
            $this->info('');
            $this->info('RouteInfo properties:');
            foreach ($reflection->getProperties() as $property) {
                $this->line('- ' . $property->getName());
            }
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
