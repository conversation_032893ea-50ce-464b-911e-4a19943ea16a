<?php

namespace App\Contracts\Policies\Sales;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO\CustomerOrderItemCalculateDTO;

interface CustomerOrderItemPolicyContract extends BaseResourcePolicyContract
{
    public function index(string $orderId): void;

    public function calculateMetrics(CustomerOrderItemCalculateDTO $dto): void;

}
