<?php

namespace App\Contracts\Policies\Purchases;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemCalculateDTO;

interface AcceptanceItemPolicyContract extends BaseResourcePolicyContract
{
    public function index(string $acceptanceId): void;

    public function calculateItemMetrics(AcceptanceItemCalculateDTO $dto): void;

}
