<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Entities\DiscountSavingEntity;

interface DiscountSavingsRepositoryContract extends CRUDRepositoryContract
{
    public function getEntity(): DiscountSavingEntity;
    public function upsert(array $data): int;
    public function oldDiscountSavingsIdsForDiscount(string $resourceId): ?Collection;
    public function deleteOldDiscountSavingsWhereSavingsIds(array $recordsToDelete): int;
    
}
