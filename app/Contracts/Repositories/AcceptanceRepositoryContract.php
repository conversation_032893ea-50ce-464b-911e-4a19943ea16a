<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface AcceptanceRepositoryContract extends CRUDRepositoryContract
{
    public function bulkDelete(array $ids): int;
    public function getFirstShipmentItemForAllProducts(string $resourceId): ?Collection;
    public function getFirstShipmentItemForAllProductsBulk(array $acceptanceIds): Collection;

    public function getFirst(string $resourceId): ?object;

    public function whereIn(string $field, array $resourceIds): Collection;

    public function getWhereInIds(array $ids): Collection;
}
