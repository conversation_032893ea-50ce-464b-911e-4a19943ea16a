<?php

namespace App\Contracts\Repositories;

use App\Contracts\CRUDRepositoryContract;
use App\Contracts\HasArchiveRepositoryContract;

interface CabinetCurrenciesRepositoryContract extends CRUDRepositoryContract, HasArchiveRepositoryContract
{
    public function deleteWhereIn(array $ids): int;
    public function deleteAccountingRecord(string $cabinetId): int;

    public function firstWhere(array $where): ?object;
}
