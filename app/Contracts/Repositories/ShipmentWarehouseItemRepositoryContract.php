<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface ShipmentWarehouseItemRepositoryContract
{
    public function deleteWhereInIds(array $ids): void;

    public function insert(array $data): void;

    public function getShipmentItemsByWarehouseItem(string $warehouseItemId): Collection;

    public function calculateShipmentCostsByItems(array $shipmentItemIds): Collection;

}
