<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface GoodsTransferWarehouseItemRepositoryContract
{
    public function deleteWhereInIds(array $ids): void;
    
    public function insert(array $data): void;
    
    public function getTransferItemsByWarehouseItem(string $warehouseItemId): Collection;
    
    public function calculateTransferCostsByItems(array $transferItemIds): Collection;
}
