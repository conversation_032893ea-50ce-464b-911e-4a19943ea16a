<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Contracts\HasArchiveRepositoryContract;

interface ContractRepositoryContract extends CRUDRepositoryContract, HasArchiveRepositoryContract
{
    public function getWhereInIds(array $ids): Collection;

    public function deleteWhereInIds(array $ids): bool;

    public function getMaxNumber(?string $cabinetId): ?string;
}
