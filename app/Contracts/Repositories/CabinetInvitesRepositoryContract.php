<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface CabinetInvitesRepositoryContract
{
    public function getSendedCabinetInvites(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection;
    public function getAllUserInvites(string $email): Collection;
    public function checkExistInvite(string $email, string $cabinetId): bool;

    public function insert(array $data): void;
    public function show(string $resourceId, int $userId, string $email): ?object;
    public function delete(string $resourceId): void;
    public function find(string $id): ?object;

    public function update(string $resourceId, array $data): int;
}
