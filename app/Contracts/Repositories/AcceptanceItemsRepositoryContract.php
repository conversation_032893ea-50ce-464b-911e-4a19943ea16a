<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface AcceptanceItemsRepositoryContract extends CRUDRepositoryContract
{
    public function getWithoutMeta(string $acceptanceId): Collection;
    public function checkItemExists(string $productId, string $acceptanceId): bool;
    public function whereIn(string $field, array $resourceIds): Collection;
    public function bulkDelete(array $ids): int;
    public function getByAcceptanceId(string $acceptanceId): Collection;
    public function calculateMetricsForNewItem(string $productId, string $warehouseId, string $dateFrom, string $cabinetId): object;
}
