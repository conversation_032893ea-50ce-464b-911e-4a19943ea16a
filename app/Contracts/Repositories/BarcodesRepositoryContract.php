<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface BarcodesRepositoryContract extends CRUDRepositoryContract
{
    public function getWhereBarcodableId(string $id): Collection;

    public function upsert(array $barcodes): void;

    public function getExistsInDb(int $code, string $cabinetId, string $barcode): bool;

    public function getMaxBarcode(int $type, string $cabinetId, int $length): ?int;
    
    public function deleteOldBarcodes(string $id, array $incomingBarcodeIds): ?int;

    public function getAllBarcodesWhereInValue(array $codes, string $cabinetId): ?Collection;

    public function getWhereInBarcodableIds(array $ids): Collection;

    public function deleteByIds(array $id): int;
    public function deleteOldBarcodesWhereBarcoddableId(string $id): int;

    public function getMaxInnerCodeByCabinet(string $cabinetId): ?int;

    public function getMaxCodeWithMaxInnerCodeForProductAndPackingsFirst(int $maxInnerCode): ?object;

    public function getMaxBarcodeEAN13ByCabinet(string $cabinetId): ?int;

    public function getMaxBarcodeEAN8ByCabinet(string $cabinetId): ?int;

    public function getMaxBarcodeEANByCabinetFirst(string $cabinetId, int $maxBarcode): ?object;
}
