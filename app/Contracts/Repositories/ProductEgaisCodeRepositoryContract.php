<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface ProductEgaisCodeRepositoryContract extends CRUDRepositoryContract
{
    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int;

    public function oldProductEgaisCodeIdsForProducts(string $resourceId): ?Collection;

    public function upsert(Collection $data): int;

    public function getWhereProductId(string $resourceId): ?Collection;

    public function getFirst(string $resourceId): ?object;
    
    public function deleteArray(array|Collection $id): int;

}
