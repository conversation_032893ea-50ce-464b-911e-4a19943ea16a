<?php

namespace App\Contracts\Services\Internal\Purchases;

use App\Contracts\CRUDServiceContract;

interface VendorOrderServiceContract extends CRUDServiceContract
{
    public function bulkDelete(array $ids): void;
    public function bulkHeld(array $ids): void;
    public function bulkUnheld(array $ids): void;
    public function bulkWaiting(array $ids): void;
    public function bulkUnwaiting(array $ids): void;

    public function bulkCopy(array $ids): void;
}
