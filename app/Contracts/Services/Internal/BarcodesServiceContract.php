<?php

namespace App\Contracts\Services\Internal;

use App\Contracts\CRUDServiceContract;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;

interface BarcodesServiceContract extends CRUDServiceContract
{
    public function bulkCreate(BarcodeDTO $dto): string;

    public function bulkUpdate(BarcodeDTO $dto): void;

    public function generateCodeAndBarcodeByCabinetId(string $id): array;

    public function generateCodeByCabinetId(string $cabinetId): string;

    public function getBarcodeGenerateEAN13ForCabinet(string $cabinetId): array;

    public function getBarcodeGenerateEAN8ForCabinet(string $cabinetId): array;

}
