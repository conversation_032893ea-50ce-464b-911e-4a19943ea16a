<?php

namespace App\Contracts\Services\Internal\Sales;

use App\Contracts\CRUDServiceContract;

interface CustomerOrdersServiceContract extends CRUDServiceContract
{
    public function bulkDelete(array $ids): void;

    public function bulkHeld(array $ids): void;

    public function bulkUnheld(array $ids): void;

    public function bulkReserve(array $ids): void;

    public function bulkUnreserve(array $ids): void;

    public function bulkCopy(array $ids): void;
}
