<?php

declare(strict_types=1);

namespace App\Clients\Ozon\API\Endpoint;

use App\Clients\Ozon\API\AbstractEndpoint;
use InvalidArgumentException;
use stdClass;

class Products extends AbstractEndpoint
{
    /**
     * Метод для получения списка всех товаров.
     *
     * Если вы используете фильтр по идентификатору offer_id или product_id, остальные параметры заполнять не обязательно.
     * За один раз вы можете использовать только одну группу идентификаторов, не больше 1000 товаров.
     *
     * Если вы не используете для отображения идентификаторы, укажите limit и last_id в следующих запросах.
     *
     * @param object|null $filter ,
     * @param string|null $lastId
     * @param int $limit
     * @return mixed|void
     */
    public function list(?object $filter = null, ?string $lastId = '', int $limit = 100)
    {
        if ($limit > 1000 || $limit < 1) {
            throw new InvalidArgumentException('Превышение максимального количества запрашиваемых товаров');
        }

        $body = [
            'filter'  => $filter ?: new stdClass(),
            'last_id' => $lastId ?? '',
            'limit'   => $limit,
        ];

        return $this->postRequest("/v3/product/list", $body);
    }

    public function getInfoList(array $offerIds = [], array $productIds = [], array $skus = [])
    {
        if (empty($offerIds) && empty($productIds) && empty($skus)) {
            return [];
        }


        $body = [
            'offer_id'  => $offerIds,
            'product_id' => $productIds,
            'sku'   => $skus,
        ];

        return $this->postRequest("/v3/product/info/list", $body);
    }

    public function getDescription(string $id)
    {
        $body = [
            'product_id' => $id,
        ];

        return $this->postRequest('/v1/product/info/description', $body);
    }

    public function getProductAttributesInfo(stdClass $filter = new stdClass(), ?string $lastId = null, int $limit = 1000, string $sortBy = 'id', string $sortDir = 'desc')
    {
        if (!isset($filter->offer_id) && !isset($filter->product_id) && !isset($filter->sku)) {
            return [];
        }


        $body = [
            'filter' => $filter,
            'last_id' => $lastId,
            'limit' => $limit,
            'sort_by' => $sortBy,
            'sort_dir' => $sortDir,
        ];

        return $this->postRequest('/v4/product/info/attributes', $body);
    }

    public function updateStocks(array $stocks)
    {
        if (empty($stocks)) {
            throw new InvalidArgumentException('Stocks array cannot be empty');
        }

        if (count($stocks) > 100) {
            throw new InvalidArgumentException('Maximum 100 products per request');
        }

        $body = [
            'stocks' => $stocks
        ];

        return $this->postRequest('/v2/products/stocks', $body);
    }
}
