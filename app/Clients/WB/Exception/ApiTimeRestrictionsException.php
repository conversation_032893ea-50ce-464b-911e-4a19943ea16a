<?php

declare(strict_types=1);

namespace App\Clients\WB\Exception;

/**
 * Exception thrown when API time restrictions are encountered
 *
 * This exception is thrown when the API enforces time-based restrictions,
 * such as rate limiting, maintenance windows, or other time-based constraints.
 */
class ApiTimeRestrictionsException extends WBSellerException
{
    /**
     * @param string $message The error message
     * @param int $code The error code (usually HTTP status code)
     * @param \Throwable|null $previous The previous throwable used for exception chaining
     */
    public function __construct(string $message = '', int $code = 429, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
