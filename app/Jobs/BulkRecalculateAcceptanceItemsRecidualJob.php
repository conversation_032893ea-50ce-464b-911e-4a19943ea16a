<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use Illuminate\Contracts\Container\BindingResolutionException;

class BulkRecalculateAcceptanceItemsRecidualJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    protected Collection $acceptances;
    protected array $productIds;
    protected AcceptanceRepositoryContract $acceptanceRepository;

    /**
     * Create a new job instance.
     * @throws BindingResolutionException
     */
    public function __construct(Collection $acceptances, array $productIds)
    {
        $this->productIds = $productIds;
        $this->acceptances = $acceptances;
        $this->acceptanceRepository = app()->make(AcceptanceRepositoryContract::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Получаем все необходимые записи для пересчета одним запросом
        $allAcceptances = $this->acceptanceRepository->getBulkNewestAcceptancesForReciduals(
            $this->acceptances,
            $this->productIds
        );

        $acceptancesArray = $allAcceptances->map(function ($item) {
            return [
                'id' => $item->id,
                'recidual' => $item->recidual,
                'updated_at' => $item->updated_at,
                'acceptance_id' => $item->acceptance_id,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->price,
            ];
        })->toArray();

        // Массовое обновление всех записей одной операцией
        DB::table('acceptance_items')
            ->upsert(
                $acceptancesArray,
                ['id'],
                ['recidual', 'updated_at']
            );
    }
}
