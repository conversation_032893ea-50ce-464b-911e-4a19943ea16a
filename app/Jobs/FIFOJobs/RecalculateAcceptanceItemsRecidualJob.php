<?php

namespace App\Jobs\FIFOJobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use Illuminate\Contracts\Container\BindingResolutionException;

class RecalculateAcceptanceItemsRecidualJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    protected object $acceptance;
    protected AcceptanceRepositoryContract $acceptanceRepository;
    protected string $productId;

    /**
     * Create a new job instance.
     * @throws BindingResolutionException
     */
    public function __construct(object $acceptance, string $productId)
    {
        $this->productId = $productId;
        $this->acceptance = $acceptance;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->acceptanceRepository = app()->make(AcceptanceRepositoryContract::class);

        $acceptances = $this->acceptanceRepository->getNewestAcceptancesForReciduals(
            $this->acceptance,
            $this->productId
        );

        $acceptancesArray = $acceptances->map(function ($item) {
            return [
                'id' => $item->id,
                'recidual' => $item->recidual,
                'updated_at' => $item->updated_at,
                'acceptance_id' => $item->acceptance_id,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->price,
            ];
        })->toArray();

        //TODO переместить в репозиторий переделавав upsert
        DB::table('acceptance_items')
            ->upsert(
                $acceptancesArray,
                ['id'],
                ['recidual', 'updated_at']
            );
    }
}
