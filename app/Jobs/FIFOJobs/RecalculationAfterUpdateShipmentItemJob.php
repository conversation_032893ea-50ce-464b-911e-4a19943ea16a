<?php

namespace App\Jobs\FIFOJobs;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecalculationAfterUpdateShipmentItemJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected object $item;
    protected ShipmentItemDTO $dto;
    protected FifoServiceContract $fifoService;
    protected ShipmentsRepositoryContract $shipmentsRepository;
    protected ShipmentItemsRepositoryContract $shipmentItemsRepository;

    /**
     * @throws BindingResolutionException
     */
    public function __construct(object $item, ShipmentItemDTO $dto)
    {
        $this->dto = $dto;
        $this->item = $item;
        $this->fifoService = app()->make(FifoServiceContract::class);
        $this->shipmentItemsRepository = app()->make(ShipmentItemsRepositoryContract::class);
        $this->shipmentsRepository = app()->make(ShipmentsRepositoryContract::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Если изменилась только цена
        if ($this->item->price !== $this->dto->price && $this->item->quantity == $this->dto->quantity) {

            $this->shipmentItemsRepository->update(
                $this->dto->resourceId,
                [
                    'price' => $this->dto->price,
                    'discount' => $this->dto->discount,
                    'total_price' => $this->dto->price * $this->dto->quantity,
                    'profit' => $this->dto->price * $this->dto->quantity - $this->dto->totalCost
                ],
            );

            // Получаем суммы всех элементов поставки для пересчета
            $shipmentTotals = $this->shipmentItemsRepository
                ->getShipmentTotalsById($this->item->shipment_id);

            $this->shipmentsRepository->update(
                $this->item->shipment_id,
                [
                    'total_cost' => $shipmentTotals->total_price_sum,
                    'profit' => $shipmentTotals->profit_sum
                ],
            );
            return;
        }

        // Если изменилось количество позиций
        if ($this->item->quantity !== $this->dto->quantity) {
            $this->fifoService->handle($this->dto->resourceId);
        }
    }
}
