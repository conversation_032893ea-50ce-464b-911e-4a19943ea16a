<?php

namespace App\Jobs\FIFOJobs;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\ShipmentWarehouseItemRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class RecalculationAfterUpdateAcceptanceItemJob implements ShouldQueue
{
    use Batchable;
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;


    protected object $resource;
    protected FifoServiceContract $fifoService;
    protected AcceptanceItemDto $newResource;
    protected ShipmentsRepositoryContract $shipmentsRepository;
    protected ShipmentItemsRepositoryContract $shipmentItemsRepository;
    protected WarehouseItemsRepositoryContract $warehouseItemsRepository;
    protected ShipmentWarehouseItemRepositoryContract $shipmentWarehouseItemsRepository;

    public function __construct(object $resource, AcceptanceItemDto $newResource)
    {
        $this->resource = $resource;
        $this->newResource = $newResource;
    }

    public function handle(): void
    {
        $this->fifoService = app()->make(FifoServiceContract::class);
        $this->shipmentsRepository = app()->make(ShipmentsRepositoryContract::class);
        $this->shipmentItemsRepository = app()->make(ShipmentItemsRepositoryContract::class);
        $this->warehouseItemsRepository = app()->make(WarehouseItemsRepositoryContract::class);
        $this->shipmentWarehouseItemsRepository = app()->make(ShipmentWarehouseItemRepositoryContract::class);

        // Если изменилась только цена
        if (
            $this->resource->price !== $this->newResource->price
            &&
            $this->resource->quantity == $this->newResource->quantity
        ) {
            $this->recalculateSums();
        }

        // Если изменилось количество позиций
        if ($this->resource->quantity !== $this->newResource->quantity) {
            $this->recalculatePositions();
        }
    }

    public function recalculateSums(): void
    {
        $this->warehouseItemsRepository->update(
            [
                'unit_price' => $this->newResource->price,
                'updated_at' => Carbon::now(),
                'total_price' => $this->newResource->price * $this->newResource->quantity
            ],
            $this->resource->id,
            $this->resource->product_id
        );


        $shipmentItems = $this->shipmentWarehouseItemsRepository
            ->getShipmentItemsByWarehouseItem($this->resource->id);
        $shipmentItemIds = $shipmentItems->pluck('shipment_item_id')->toArray();

        $shipmentCosts = $this->shipmentWarehouseItemsRepository
            ->calculateShipmentCostsByItems($shipmentItemIds);

        $updates = [];

        foreach ($shipmentItems as $shipmentItem) {
            $newCost = $shipmentCosts[$shipmentItem->shipment_item_id]->new_cost;
            $updates[] = [
                'id' => $shipmentItem->shipment_item_id,
                'total_cost' => $newCost,
                'profit' => $newCost - $shipmentItem->total_price,
                'cost' => $newCost / $shipmentItem->quantity
            ];
        }

        //TODO переделать контракт и реализовать в репозитории
        DB::table('shipment_items')->upsert($updates, ['id'], ['total_cost', 'profit', 'cost']);

        $shipmentIds = $shipmentItems->pluck('shipment_id')->unique()->toArray();
        $shipmentUpdates = $this->shipmentItemsRepository->getShipmentTotalsByIds($shipmentIds);

        $this->shipmentsRepository->upsert($shipmentUpdates, ['total_cost', 'profit']);
    }

    public function recalculatePositions(): void
    {
        $this->warehouseItemsRepository->update(
            [
                'unit_price' => $this->newResource->price,
                'updated_at' => Carbon::now(),
                'total_price' => $this->newResource->price * $this->newResource->quantity,
                'quantity' => $this->newResource->quantity,
            ],
            $this->resource->acceptance_id,
            $this->resource->product_id
        );

        $shipmentItem = $this->warehouseItemsRepository
            ->getShipmentItemIdByAcceptance(
                $this->resource->acceptance_id,
                $this->resource->product_id
            );

        if (!$shipmentItem) {
            return;
        }

        $this->fifoService->handle($shipmentItem->id);
    }

}
