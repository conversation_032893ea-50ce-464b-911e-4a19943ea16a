<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Contracts\Repositories\FilesRepositoryContract;
use Illuminate\Contracts\Container\BindingResolutionException;

class DeleteFilesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    private string $relatedId;
    private FilesRepositoryContract $filesRepository;

    /**
     * Create a new job instance.
     * @throws BindingResolutionException
     */
    public function __construct(string $relatedId)
    {
        $this->relatedId = $relatedId;
        $this->filesRepository = app()->make(FilesRepositoryContract::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->filesRepository->getByRelatedId($this->relatedId)->each(
            function ($file) {
                Storage::disk('s3-docs')->delete($file->path);
            }
        );

        $this->filesRepository->deleteByRelatedId($this->relatedId);
    }
}
