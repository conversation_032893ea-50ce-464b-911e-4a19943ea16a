<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonV1ReturnsFboFbsListRepositoryContract;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonV1ReturnsFboFbsListRepository implements OzonV1ReturnsFboFbsListRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_fbo_fbs_returns';

    public function __construct(
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE . ' as ord')
            ->join('ozon_order_items as ordi', 'ord.id', '=', 'ordi.ozon_order_id')
            ->where('ord.cabinet_id', $id)
            ->select('ordi.*', 'ord.*', 'ord.id as id')
            ->get();

    }

    public function getAllOrderIds(string $cabinetId): Collection
    {
        return DB::table(self::TABLE)
        ->where('cabinet_id', $cabinetId)
        ->pluck('id', 'operation_id');
    }

    public function getReturnsByReturnIds(array $returnIds): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('return_id', $returnIds)
            ->pluck('id', 'return_id'); // Вернёт массив ['return_id' => 'id']
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)->upsert(
            $data,
            ['id'], // , 'return_id'
            [

                'updated_at',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {

        return DB::table(self::TABLE . ' as ord')
        ->join('ozon_order_items as ordi', 'ord.id', '=', 'ordi.ozon_order_id')
        ->where('ord.id', $id)
        ->select('ordi.*', 'ord.*', 'ord.id as id')
        ->first();

    }

}
