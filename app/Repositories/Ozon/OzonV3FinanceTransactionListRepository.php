<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonV3FinanceTransactionListRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\OzonV3FinanceTransactionListEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonV3FinanceTransactionListRepository implements OzonV3FinanceTransactionListRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_transactions';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly OzonV3FinanceTransactionListEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->getEntity();
        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::GOODS_AND_SERVICES->value, $this->entity::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            match ($value['condition'] ?? true) {
                FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                default => $this->handleInCondition($query, $filter, $value['value']),
            };
        }

        $query->when(isset($filters['search']), function ($query) use ($filters) {
            $query->where('posting_number', 'like', '%' . $filters['search']['value'] . '%')
                ->orWhere('operation_id', 'like', '%' . $filters['search']['value'] . '%')
                ->orWhere('operation_type_name', 'like', '%' . $filters['search']['value'] . '%');
        });
        $query->when(isset($filters['shared_access']), fn ($query) => $query->where('shared_access', '=', $filters['shared_access']['value']));
        $query->when(isset($filters['show_only']), function ($query) use ($filters) {
            if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                return $query->where('archived_at', '!=', null);
            }

            if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                return $query->whereNull('archived_at');
            }

            return $query;
        });

        return $query
        ->when($sortField, function ($query) use ($sortField, $sortDirection) {
            return $query->sort($sortField, $sortDirection);
        })
        ->paginate($perPage, $page)
        ->get();

    }

    public function getEntity(): OzonV3FinanceTransactionListEntity
    {
        return $this->entity;
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'posting_number' => $query->whereNull('posting_number'),
            'operation_date' => $query->whereNull('operation_date'),
            'delivery_schema' => $query->whereNull('delivery_schema'),
            'order_date' => $query->whereNull('order_date'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            default => null,
        };

    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'posting_number' => $query->whereNotNull('posting_number'),
            'operation_date' => $query->whereNotNull('operation_date'),
            'delivery_schema' => $query->whereNotNull('delivery_schema'),
            'order_date' => $query->whereNotNull('order_date'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            default => null,
        };

    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'posting_number' => $query->where('posting_number', 'not like', '%' . $value . '%')
                        ->orWhere('posting_number', null),
            'delivery_schema' => $query->where('delivery_schema', 'not like', '%' . $value . '%')
                        ->orWhere('delivery_schema', null),
            'operation_type_name' => $query->where('operation_type_name', 'not like', '%' . $value . '%')
                        ->orWhere('operation_type_name', null),
            'operation_type' => $query->where('operation_type', 'not like', '%' . $value . '%')
                        ->orWhere('operation_type', null),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            default => null,
        };

    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'operation_id' => $query->where('operation_id', 'like', '%' . $value . '%'),
            'posting_number' => $query->where('posting_number', 'like', '%' . $value . '%'),
            'type' => $query->where('type', 'like', '%' . $value . '%'),
            'delivery_schema' => $query->where('delivery_schema', 'like', '%' . $value . '%'),
            'operation_type_name' => $query->where('operation_type_name', 'like', '%' . $value . '%'),
            'operation_type' => $query->where('operation_type', 'like', '%' . $value . '%'),
            'ozon_company_id' => $query->where('ozon_company_id', 'like', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            default => null,
        };

    }

    public function getAllOrderIds(string $cabinetId): Collection
    {
        return DB::table(self::TABLE)
        ->where('cabinet_id', $cabinetId)
        ->pluck('id', 'operation_id');
    }

    public function getOrdersByOperationIds(array $operationIds): array
    {
        return DB::table(self::TABLE)
            ->whereIn('operation_id', $operationIds)
            ->pluck('id', 'operation_id') // Вернёт массив ['operation_id' => 'id']
            ->toArray();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)->upsert(
            $data,
            ['operation_id'], // , 'operation_id'
            [
                'operation_type',
                'operation_date',
                'operation_type_name',
                'delivery_charge',
                'return_delivery_charge',
                'accruals_for_sale',
                'sale_commission',
                'amount',
                'type',
                'delivery_schema',
                'order_date',
                'posting_number',
                'warehouse_id',
                'updated_at',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {

        return DB::table(self::TABLE . ' as ord')
        ->join('ozon_order_items as ordi', 'ord.id', '=', 'ordi.ozon_order_id')
        ->where('ord.id', $id)
        ->select('ordi.*', 'ord.*', 'ord.id as id')
        ->first();

    }

}
