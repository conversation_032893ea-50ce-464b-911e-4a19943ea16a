<?php

namespace App\Repositories\Sales\ComissionReports;

use App\Contracts\Repositories\ReceivedComissionReportsRealizedItemsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\ReceivedComissionReportRealizedItemEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ReceivedComissionReportsRealizedItemsRepository implements ReceivedComissionReportsRealizedItemsRepositoryContract
{
    use HasTimestamps;

    protected const TABLE = 'received_comission_report_realized_items';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('report_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->whereHas('product', function ($builder) use ($filters) {
                $builder->where('title', 'ILIKE', '%' . $filters['search']['value'] . '%');
            });
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
                ->where('id', $id)
                ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function findFirst(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    private function getEntity(): ReceivedComissionReportRealizedItemEntity
    {
        return new ReceivedComissionReportRealizedItemEntity();
    }
}
