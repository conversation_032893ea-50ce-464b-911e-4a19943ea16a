<?php

namespace App\Repositories\Sales\ComissionReports;

use App\Contracts\Repositories\ComissionReportsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\ComissionReportsTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

readonly class ComissionReportsRepository implements ComissionReportsRepositoryContract
{
    public function __construct(
        private AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        // Функция для построения запроса с учетом связей
        $buildQuery = function ($type, $permissionName, $fields) use ($id, $filters) {
            $selectFields = $fields === ['*'] ?
                [
                    "id",
                    "created_at",
                    "updated_at",
                    "cabinet_id",
                    "status_id",
                    "number",
                    "date_from",
                    "is_held",
                    "is_printed",
                    "legal_entity_id",
                    "contractor_id",
                    "sales_channel_id",
                    "currency_id",
                    "sum",
                    "comment",
                    "employee_id",
                    "department_id",
                    "comission_type",
                    "comission_value",
                    DB::raw("'$type' as type")
                ] : array_merge($fields, [DB::raw("'$type' as type")]);

            $selectFields = array_map(static function ($item) use ($type) {
                if ($item instanceof Expression) {
                    return $item;
                }
                return "{$type}.{$item}";
            }, $selectFields);

            $query = DB::table($type)
                ->select($selectFields)
                ->where("{$type}.cabinet_id", $id);

            $query = $this->authService->queryFilter($id, $query, $permissionName, $type);

            foreach ($filters as $filter => $value) {
                if (isset($value['condition'])) {
                    switch ($value['condition']) {
                        case FilterConditionEnum::EMPTY->value:
                            match ($filter) {
                                'contracts' => $query->whereNull("{$type}.contract_id"),
                                'sales_channels' => $query->whereNull("{$type}.sales_channel_id"),
                                'contractors' => $query->whereNull("{$type}.contractor_id"),
                                'legals' => $query->whereNull("{$type}.legal_entity_id"),
                                'statuses' => $query->whereNull("{$type}.status_id"),
                                'employee_owners' => $query->whereNull("{$type}.employee_id"),
                                'department_owners' => $query->whereNull("{$type}.department_id"),
                                'contractor_groups' => $query->whereNotExists(function ($subQuery) use ($type) {
                                    $subQuery->select(DB::raw(1))
                                        ->from('contractor_group')
                                        ->whereColumn('contractor_group.contractor_id', "{$type}.contractor_id");
                                }),
                                'contractor_owners' => $query->join(
                                    'contractors',
                                    'contractors.id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereNull('contractors.employee_id'),
                                'products' => $query->where(function ($outerQuery) use ($type) {
                                    $this->whereProductEmpty($type, $outerQuery);
                                }),
                                default => null,
                            };
                            break;
                        case FilterConditionEnum::NOT_EMPTY->value:
                            match ($filter) {
                                'contracts' => $query->whereNotNull("{$type}.contract_id"),
                                'sales_channels' => $query->whereNotNull("{$type}.sales_channel_id"),
                                'contractors' => $query->whereNotNull("{$type}.contractor_id"),
                                'legals' => $query->whereNotNull("{$type}.legal_entity_id"),
                                'statuses' => $query->whereNotNull("{$type}.status_id"),
                                'employee_owners' => $query->whereNotNull("{$type}.employee_id"),
                                'department_owners' => $query->whereNotNull("{$type}.department_id"),
                                'contractor_groups' => $query->whereExists(function ($subQuery) use ($type) {
                                    $subQuery->select(DB::raw(1))
                                        ->from('contractor_group')
                                        ->whereColumn('contractor_group.contractor_id', "{$type}.contractor_id");
                                }),
                                'contractor_owners' => $query->join(
                                    'contractors',
                                    'contractors.id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereNotNull('contractors.employee_id'),
                                'products' => $query->where(function ($outerQuery) use ($type) {
                                    $this->whereProductNotEmpty($type, $outerQuery);
                                }),
                                default => null,
                            };
                            break;
                        case FilterConditionEnum::NOT_IN->value:
                            match ($filter) {
                                'contracts' => $query->where(function ($query) use ($type, $value) {
                                    $query->whereNotIn("{$type}.contract_id", $value['value'])
                                        ->orWhereNull("{$type}.contract_id");
                                }),
                                'sales_channels' => $query->where(function ($query) use ($type, $value) {
                                    $query->whereNotIn("{$type}.sales_channel_id", $value['value'])
                                        ->orWhereNull("{$type}.sales_channel_id");
                                }),
                                'contractors' => $query->whereNotIn("{$type}.contractor_id", $value['value']),
                                'legals' => $query->whereNotIn("{$type}.legal_entity_id", $value['value']),
                                'statuses' => $query->whereNotIn("{$type}.status_id", $value['value']),
                                'employee_owners' => $query->whereNotIn("{$type}.employee_id", $value['value']),
                                'department_owners' => $query->whereNotIn("{$type}.department_id", $value['value']),
                                'contractor_groups' => $query->join(
                                    'contractor_group',
                                    'contractor_group.contractor_id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereNotIn('contractor_group.group_id', $value['value']),
                                'contractor_owners' => $query->join(
                                    'contractors',
                                    'contractors.id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereNotIn('contractors.employee_id', $value['value']),
                                'products' => $query->where(function ($outerQuery) use ($type, $value) {
                                    $this->whereProductNotIn($type, $outerQuery, $value);
                                }),
                                default => null,
                            };
                            break;
                        default:
                            match ($filter) {
                                'sales_channels' => $query->whereIn("{$type}.sales_channel_id", $value['value']),
                                'contractors' => $query->whereIn("{$type}.contractor_id", $value['value']),
                                'legals' => $query->whereIn("{$type}.legal_entity_id", $value['value']),
                                'statuses' => $query->whereIn("{$type}.status_id", $value['value']),
                                'employee_owners' => $query->whereIn("{$type}.employee_id", $value['value']),
                                'department_owners' => $query->whereIn("{$type}.department_id", $value['value']),
                                'contractor_groups' => $query->join(
                                    'contractor_group',
                                    'contractor_group.contractor_id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereIn('contractor_group.group_id', $value['value']),
                                'contractor_owners' => $query->join(
                                    'contractors',
                                    'contractors.id',
                                    '=',
                                    "{$type}.contractor_id"
                                )
                                    ->whereIn('contractors.employee_id', $value['value']),
                                'products' => $query->where(function ($outerQuery) use ($type, $value) {
                                    $this->whereProductIn($type, $outerQuery, $value);
                                }),
                                default => null,
                            };
                            break;
                    }
                }
            }

            $query->when(
                isset($filters['is_held']['value']),
                fn($q) => $q->where("{$type}.is_held", $filters['is_held']['value'])
            );

            $query->when(
                isset($filters['is_common']['value']),
                fn($q) => $q->where("{$type}.is_common", $filters['is_common']['value'])
            );
            $query->when(
                isset($filters['is_printed']['value']),
                fn($q) => $q->where("{$type}.is_printed", $filters['is_common']['value'])
            );

            $query->when(isset($filters['period']['from']), function ($query) use ($filters, $type) {
                $from = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['from'])?->format('Y-m-d H:i:s');
                $to = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['to'])?->format('Y-m-d H:i:s');

                $query->whereBetween("{$type}.created_at", [$from, $to]);
            });

            $query->when(isset($filters['search']['value']), function ($query) use ($filters, $type) {
                $query->where(function ($query) use ($type, $filters) {
                    $query->where("{$type}.number", 'ilike', '%' . $filters['search']['value'] . '%')
                        ->orWhere("{$type}.comment", 'ilike', '%' . $filters['search']['value'] . '%');
                });
            });

            $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters, $type) {
                $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
                $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

                $query->whereBetween("{$type}.updated_at", [$from, $to]);
            });

            return $query;
        };

        // Формируем запрос в зависимости от типа
        $query = match ($filters['type']['value'] ?? null) {
            ComissionReportsTypeEnum::RECEIVED_REPORTS->value => $buildQuery(
                'received_comission_reports',
                PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
                $fields
            ),

            ComissionReportsTypeEnum::ISSUED_REPORTS->value => $buildQuery(
                'issued_comission_reports',
                PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
                $fields
            ),

            default => $buildQuery(
                'received_comission_reports',
                PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
                $fields
            )->unionAll(
                $buildQuery(
                    'issued_comission_reports',
                    PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
                    $fields
                )
            )
        };

        if ($sortField) {
            $query->orderBy($sortField, $sortDirection);
        }
        return $this->paginateResult($query, $page, $perPage, []);
    }

    public function paginateResult(Builder $baseQuery, int $page, int $perPage, array $relationFields): Collection
    {
        $total = $baseQuery->count();

        $results = $baseQuery
            ->forPage($page, $perPage)
            ->get();

        $results->transform(function ($item) use ($relationFields) {
            foreach ($relationFields as $field) {
                $sqlField = str_replace('.', '_', $field);

                if (isset($item->{$sqlField}) && $item->{$sqlField} !== '[]') {
                    $item->{$field} = json_decode($item->{$sqlField}, true);
                } else {
                    $item->{$field} = [];
                }

                if ($sqlField !== $field) {
                    unset($item->{$sqlField});
                }
            }
            return $item;
        });

        return collect([
            'data' => $results,
            'meta' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * @param $type
     * @param BaseEntity|EntityBuilder|Builder $outerQuery
     * @param mixed $value
     * @return void
     */
    public function whereProductIn(
        $type,
        BaseEntity|EntityBuilder|Builder $outerQuery,
        array $value
    ): void {
        switch ($type) {
            case ComissionReportsTypeEnum::ISSUED_REPORTS->value:
                $outerQuery->whereExists(function ($subQuery) use ($type, $value) {
                    $subQuery->select(DB::raw(1))
                        ->from('issued_comission_report_items')
                        ->join('products', 'products.id', '=', 'issued_comission_report_items.product_id')
                        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                        ->whereColumn('issued_comission_report_items.report_id', "{$type}.id")
                        ->where(function ($q) use ($value) {
                            $q->whereIn('issued_comission_report_items.product_id', $value['value'])
                                ->orWhereIn('product_groups.id', $value['value']);
                        });
                });
                break;

            case ComissionReportsTypeEnum::RECEIVED_REPORTS->value:
                $outerQuery->where(function ($innerQuery) use ($type, $value) {
                    $innerQuery->whereExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->join(
                                'products',
                                'products.id',
                                '=',
                                'received_comission_report_realized_items.product_id'
                            )
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_realized_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->orWhereExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->join('products', 'products.id', '=', 'received_comission_report_return_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_return_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    });
                });
                break;

            default:
                $outerQuery->where(function ($innerQuery) use ($type, $value) {
                    $innerQuery->whereExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('issued_comission_report_items')
                            ->join('products', 'products.id', '=', 'issued_comission_report_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('issued_comission_report_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('issued_comission_report_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->orWhereExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->join(
                                'products',
                                'products.id',
                                '=',
                                'received_comission_report_realized_items.product_id'
                            )
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_realized_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->orWhereExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->join('products', 'products.id', '=', 'received_comission_report_return_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_return_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    });
                });
                break;
        }
    }

    private function whereProductNotIn($type, BaseEntity|EntityBuilder|Builder $outerQuery, array $value): void
    {
        switch ($type) {
            case ComissionReportsTypeEnum::ISSUED_REPORTS->value:
                $outerQuery->whereNotExists(function ($subQuery) use ($type, $value) {
                    $subQuery->select(DB::raw(1))
                        ->from('issued_comission_report_items')
                        ->join('products', 'products.id', '=', 'issued_comission_report_items.product_id')
                        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                        ->whereColumn('issued_comission_report_items.report_id', "{$type}.id")
                        ->where(function ($q) use ($value) {
                            $q->whereIn('issued_comission_report_items.product_id', $value['value'])
                                ->orWhereIn('product_groups.id', $value['value']);
                        });
                });
                break;

            case ComissionReportsTypeEnum::RECEIVED_REPORTS->value:
                $outerQuery->where(function ($innerQuery) use ($type, $value) {
                    $innerQuery->whereNotExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->join(
                                'products',
                                'products.id',
                                '=',
                                'received_comission_report_realized_items.product_id'
                            )
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_realized_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->whereNotExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->join('products', 'products.id', '=', 'received_comission_report_return_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_return_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    });
                });
                break;

            default:
                $outerQuery->where(function ($innerQuery) use ($type, $value) {
                    $innerQuery->whereNotExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('issued_comission_report_items')
                            ->join('products', 'products.id', '=', 'issued_comission_report_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('issued_comission_report_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('issued_comission_report_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->whereNotExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->join(
                                'products',
                                'products.id',
                                '=',
                                'received_comission_report_realized_items.product_id'
                            )
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_realized_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    })->whereNotExists(function ($subQuery) use ($type, $value) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->join('products', 'products.id', '=', 'received_comission_report_return_items.product_id')
                            ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id")
                            ->where(function ($q) use ($value) {
                                $q->whereIn('received_comission_report_return_items.product_id', $value['value'])
                                    ->orWhereIn('product_groups.id', $value['value']);
                            });
                    });
                });
                break;
        }
    }

    private function whereProductNotEmpty($type, BaseEntity|EntityBuilder|Builder $outerQuery): void
    {
        switch ($type) {
            case ComissionReportsTypeEnum::ISSUED_REPORTS->value:
                $outerQuery->whereExists(function ($subQuery) use ($type) {
                    $subQuery->select(DB::raw(1))
                        ->from('issued_comission_report_items')
                        ->whereColumn('issued_comission_report_items.report_id', "{$type}.id");
                });
                break;

            case ComissionReportsTypeEnum::RECEIVED_REPORTS->value:
                $outerQuery->where(function ($innerQuery) use ($type) {
                    $innerQuery->whereExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id");
                    })->orWhereExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id");
                    });
                });
                break;

            default:
                $outerQuery->where(function ($innerQuery) use ($type) {
                    $innerQuery->whereExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('issued_comission_report_items')
                            ->whereColumn('issued_comission_report_items.report_id', "{$type}.id");
                    })->orWhereExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id");
                    })->orWhereExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id");
                    });
                });
                break;
        }
    }

    private function whereProductEmpty($type, BaseEntity|EntityBuilder|Builder $outerQuery): void
    {
        switch ($type) {
            case ComissionReportsTypeEnum::ISSUED_REPORTS->value:
                $outerQuery->whereNotExists(function ($subQuery) use ($type) {
                    $subQuery->select(DB::raw(1))
                        ->from('issued_comission_report_items')
                        ->whereColumn('issued_comission_report_items.report_id', "{$type}.id");
                });
                break;

            case ComissionReportsTypeEnum::RECEIVED_REPORTS->value:
                $outerQuery->where(function ($innerQuery) use ($type) {
                    $innerQuery->whereNotExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id");
                    })->whereNotExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id");
                    });
                });
                break;

            default:
                $outerQuery->where(function ($innerQuery) use ($type) {
                    $innerQuery->whereNotExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('issued_comission_report_items')
                            ->whereColumn('issued_comission_report_items.report_id', "{$type}.id");
                    })->whereNotExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_realized_items')
                            ->whereColumn('received_comission_report_realized_items.report_id', "{$type}.id");
                    })->whereNotExists(function ($subQuery) use ($type) {
                        $subQuery->select(DB::raw(1))
                            ->from('received_comission_report_return_items')
                            ->whereColumn('received_comission_report_return_items.report_id', "{$type}.id");
                    });
                });
                break;
        }
    }
}
