<?php

namespace App\Repositories\Warehouses\Cells;

use App\Contracts\Repositories\WarehouseCellsRepositoryContract;
use App\Entities\WarehouseCellEntity;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseCellsRepository implements WarehouseCellsRepositoryContract
{
    private const TABLE = 'warehouse_cells';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);


        $query->select($baseFields)
            ->where('warehouse_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('address', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('description', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert(
                array_merge($data, ['created_at' => Carbon::now()])
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->join('warehouses as w', 'warehouse_cells.warehouse_id', '=', 'w.id')
            ->leftJoin('warehouse_cell_sizes as wcs', 'warehouse_cells.size_id', '=', 'wcs.id')
            ->leftJoin('warehouse_cell_groups as wg', 'warehouse_cells.group_id', '=', 'wg.id')
            ->leftJoin('warehouse_storage_area_cells as wsac', 'warehouse_cells.id', '=', 'wsac.cell_id')
            ->leftJoin('warehouse_storage_areas as wsa', 'wsac.storage_area_id', '=', 'wsa.id')
            ->where('warehouse_cells.id', $id)
            ->select(
                [
                    'warehouse_cells.*',
                    'wcs.name as size_name',
                    'wg.address as group_name',
                    'w.name as warehouse_name',
                    'wsa.name as storage_area_name'
                ]
            )
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    private function getEntity(): WarehouseCellEntity
    {
        return new WarehouseCellEntity();
    }
}
