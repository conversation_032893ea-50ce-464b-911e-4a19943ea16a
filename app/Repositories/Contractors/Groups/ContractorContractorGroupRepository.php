<?php

namespace App\Repositories\Contractors\Groups;

use App\Contracts\Repositories\ContractorContractorGroupRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\ContractorContractorGroupEntity;
use App\Entities\EntityBuilder;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorContractorGroupRepository implements ContractorContractorGroupRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const string TABLE = 'contractor_group';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly ContractorContractorGroupEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->entity;
        $query->select($baseFields)
            ->where('is_system', true)
            ->orWhere(function (EntityBuilder $query) use ($id) {
                $query->where('is_system', false)
                    ->where('cabinet_id', $id);
            });

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function oldContractorGroupIdsForContractor(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE) // текущие
            ->where('contractor_id', $resourceId)
            ->pluck('group_id');
    }

    public function deleteOldContractorGroupWhereGroupIds(string $id, array $recordsToDelete): int
    {
        return DB::table(self::TABLE)
            ->where('contractor_id', $id)
            ->whereIn('group_id', $recordsToDelete)
            ->delete();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['contractor_id', 'group_id'],
                ['contractor_id', 'group_id']
            );
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getEntity(): ContractorContractorGroupEntity
    {
        return $this->entity;
    }


}
