<?php

namespace App\Repositories\Contractors\Groups;

use App\Contracts\Repositories\ContractorGroupsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\ContractorGroupsEntity;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorGroupsRepository implements ContractorGroupsRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const string TABLE = 'contractor_groups';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly ContractorGroupsEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->getEntity();
        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::CONTRACTORS->value, $this->entity::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'like', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getEntity(): ContractorGroupsEntity
    {
        return $this->entity;
    }


}
