<?php

namespace App\Repositories\Documents;

use App\Contracts\Repositories\RelatedDocumentsRepositoryContract;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class RelatedDocumentsRepository implements RelatedDocumentsRepositoryContract
{
    protected string $table = 'documents';

    public function addChild(string $parentId, string $childId): array
    {
        $nodes = DB::table('documents')
            ->whereIn('documentable_id', [$parentId, $childId])
            ->get();

        $parent = $nodes->firstWhere('documentable_id', $parentId);
        $child = $nodes->firstWhere('documentable_id', $childId);

        if (!$parent) {
            throw new RuntimeException('Parent node not found');
        }

        if (!$child) {
            throw new RuntimeException('Child node not found');
        }

        $subtreeSize = DB::table('documents')
            ->where('tree_id', $child->tree_id)
            ->where('lft', '>=', $child->lft)
            ->where('rgt', '<=', $child->rgt)
            ->count() * 2;

        $lastChild = DB::table('documents')
            ->where('tree_id', $parent->tree_id)
            ->where('lft', '>', $parent->lft)
            ->where('rgt', '<', $parent->rgt)
            ->orderBy('rgt', 'desc')
            ->first();

        $insertPosition = $lastChild ? $lastChild->rgt : $parent->lft;

        DB::statement(
            "
            UPDATE documents
            SET
                lft = CASE
                    WHEN tree_id = ? AND lft > ? THEN lft + ?
                    ELSE lft
                END,
                rgt = CASE
                    WHEN tree_id = ? AND rgt > ? THEN rgt + ?
                    ELSE rgt
                END
            WHERE tree_id = ?
            AND (lft > ? OR rgt > ?)",
            [
                $parent->tree_id, $insertPosition, $subtreeSize,
                $parent->tree_id, $insertPosition, $subtreeSize,
                $parent->tree_id,
                $insertPosition, $insertPosition
            ]
        );

        $offset = $insertPosition - $child->lft + 1;

        DB::statement(
            "
            UPDATE documents
            SET
                lft = lft + ?,
                rgt = rgt + ?,
                tree_id = ?
            WHERE tree_id = ?
            AND lft >= ?
            AND rgt <= ?",
            [
                $offset, $offset,
                $parent->tree_id,
                $child->tree_id,
                $child->lft,
                $child->rgt
            ]
        );

        DB::table('documents')
            ->where('documentable_id', $childId)
            ->update([
                'parent_id' => $parentId
            ]);

        return ['documentable_id' => $childId];
    }
}
