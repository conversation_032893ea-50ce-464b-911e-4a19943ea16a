<?php

namespace App\Repositories\Goods\Products;

use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductAttributeRepository implements ProductAttributeRepositoryContract
{
    private const TABLE = 'product_attributes';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->where('cabinet_id', $cabinetId)
            ->count();
    }

    public function getWhereProductId(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE . ' as pa')
            ->leftJoin('attributes as a', 'a.id', '=', 'pa.attribute_id')
            ->leftJoin('attribute_values as av', 'av.id', '=', 'pa.attribute_values_id')
            ->where('pa.product_id', $resourceId)
            ->select('pa.*', 'a.name as attribute_name', 'av.value as attribute_values')
            ->orderBy('pa.sort_order', 'asc')
            ->get();
    }

    public function getFirst(string $resourceId): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->first();
    }

    public function oldProductAttributesIdsForProducts(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE) // текущие
        ->where('product_id', $resourceId)
        ->pluck('id');
    }

    public function upsert(Collection $data): int
    {
        return DB::table(self::TABLE)->upsert(
            $data->toArray(),
            ['id'],
            [
                'product_id',
                'attribute_id',
                'attribute_values_id',
                'copy',
                'sort_order',
            ]
        );
    }

    public function insert(array|Collection $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }

    public function deleteArray(array|Collection $id): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->get();
    }

}
