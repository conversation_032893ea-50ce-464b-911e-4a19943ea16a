<?php

namespace App\Repositories\Workspace\Permissions;

use App\Contracts\Repositories\DepartmentPermissionsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DepartmentPermissionsRepository implements DepartmentPermissionsRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'department_permissions';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function deleteWhereNotInIds(string $departamentId, array $ids): int
    {
        return DB::table(self::TABLE)
            ->where('department_id', $departamentId)
            ->whereNotIn('id', $ids)
            ->delete();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['id'],
                ['scope']
            );
    }

    public function get(string $employeeId): Collection
    {
        return DB::table(self::TABLE . ' as d')
            ->leftJoin('permissions as p', 'd.permission_id', '=', 'p.id')
            ->where('d.department_id', $employeeId)
            ->select([
                'd.*',
                DB::raw('coalesce(jsonb_agg(p) filter (where p.id is not null), \'[]\') AS permissions'),
            ])
            ->groupBy('d.id')
            ->get();
    }
}
