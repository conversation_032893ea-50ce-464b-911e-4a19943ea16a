<?php

namespace App\Repositories\Workspace\Cabinet;

use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Entities\CabinetInviteEntity;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CabinetInvitesRepository implements CabinetInvitesRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'cabinet_invites';

    public function getAllUserInvites(string $email): Collection
    {
        return DB::table(self::TABLE . ' as ci')
            ->join('cabinets as c', 'ci.cabinet_id', '=', 'c.id')
            ->join('employees as e', 'ci.employee_id', '=', 'e.id')
            ->where('ci.email', $email)
            ->where('ci.status', CabinetInviteStatusEnum::WAITING)
            ->select([
               'ci.id as id',
               'ci.email as email',
               'ci.status as status',
               'ci.token as token',
               'ci.created_at as created_at',
               'ci.updated_at as updated_at',
               'c.name as cabinet_name',
                DB::raw('CONCAT(e.lastname, \' \', e.firstname) AS employee_name')
            ])
            ->get();
    }

    public function checkExistInvite(string $email, string $cabinetId): bool
    {
        return DB::table(self::TABLE)
            ->where('status', CabinetInviteStatusEnum::WAITING)
            ->where('email', $email)
            ->where('cabinet_id', $cabinetId)
            ->exists();
    }


    public function insert(array $data): void
    {
        $data = $this->setTimestamps($data);
        DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $resourceId, int $userId, string $email): ?object
    {
        return DB::table(self::TABLE)
            ->join('cabinets', 'cabinet_invites.cabinet_id', '=', 'cabinets.id')
            ->where('cabinet_invites.token', $resourceId)
            ->where(function (Builder $query) use ($userId, $email) {
                $query->where('cabinet_invites.email', $email)
                    ->orWhere('cabinets.user_id', $userId);
            })
            ->select(
                [
                    'cabinet_invites.*',
                    'cabinets.name as cabinet_name'
                ]
            )
            ->first();
    }

    public function delete(string $resourceId): void
    {
        DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->delete();
    }

    public function find(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->find($id);
    }

    public function getSendedCabinetInvites(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('email', 'ILIKE', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function update(string $resourceId, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    private function getEntity(): CabinetInviteEntity
    {
        return new CabinetInviteEntity();
    }
}
