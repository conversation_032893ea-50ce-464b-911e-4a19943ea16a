<?php

namespace App\Repositories\Workspace\Cabinet;

use App\Contracts\Repositories\CabinetsRepositoryContract;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CabinetsRepository implements CabinetsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'cabinets';

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
            ->where('user_id', $id)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function tempGet(string $userId, string $filters): Collection
    {
        return DB::table('employees as e')
             ->join('cabinet_employee as ce', 'ce.employee_id', '=', 'e.id')
             ->join('cabinets as c', 'c.id', '=', 'ce.cabinet_id')
             ->where('e.user_id', $userId)
             ->when($filters === 'own', fn (Builder $query) => $query->where('c.user_id', $userId))
             ->when($filters === 'invited', fn (Builder $query) => $query->where('c.user_id', '!=', $userId))
             ->select(['c.*', 'e.id as employee_id'])
             ->get();
    }

    public function getEmployees(string $cabinetId): Collection
    {
        return DB::table('employees as e')
            ->join('cabinet_employee as ce', 'ce.employee_id', '=', 'e.id')
            ->where('ce.cabinet_id', $cabinetId)
            ->get();
    }
}
