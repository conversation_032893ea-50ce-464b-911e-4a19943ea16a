<?php

namespace App\Repositories\Workspace;

use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Facades\DB;

class CabinetEmployeeRepository implements CabinetEmployeeRepositoryContract
{
    use HasTimestamps;

    public function insert(array $data): void
    {
        $data = $this->setTimestamps($data);
        DB::table('cabinet_employee')
            ->insert($data);
    }
}
