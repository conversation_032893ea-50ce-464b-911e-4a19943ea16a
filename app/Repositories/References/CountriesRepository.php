<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\CountriesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\CountryEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CountriesRepository implements CountriesRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;

    private const string TABLE = 'countries';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);


        $query->select($baseFields);
        switch ($filters['type']['value'] ?? null) {
            case EntitiesTypeEnum::SYSTEM->value:
                $query->where('cabinet_id', null);
                break;
            case EntitiesTypeEnum::CUSTOM->value:
                $query->where('cabinet_id', $id);
                break;
            default:
                $query->where(function () use ($query, $id) {
                    $query->where('cabinet_id', $id)
                        ->orWhere('cabinet_id', null);
                });
                break;
        }

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::COUNTRIES->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('full_name', 'ilike', '%' . $filters['search']['value'] . '%');
        });
        $query->when(
            isset($filters['name']['value']),
            fn ($query) => $query->where('name', 'ilike', '%' . $filters['name']['value'] . '%')
        );

        $query->when(
            isset($filters['is_common']['value']),
            fn ($query) => $query->where('is_common', $filters['is_common']['value'])
        );

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }


    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'code' => $query->whereNull('code'),
            'iso2' => $query->whereNull('iso2'),
            'iso3' => $query->whereNull('iso3'),
            'full_name' => $query->whereNull('full_name'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'code' => $query->whereNotNull('code'),
            'iso2' => $query->whereNotNull('iso2'),
            'iso3' => $query->whereNotNull('iso3'),
            'full_name' => $query->whereNotNull('full_name'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'code' => $query->where('code', 'not like', '%' . $value . '%'),
            'iso2' => $query->where('iso2', 'not like', '%' . $value . '%'),
            'iso3' => $query->where('iso3', 'not like', '%' . $value . '%'),
            'full_name' => $query->where('full_name', 'not like', '%' . $value . '%'),
            'employee_owners' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('employee_id', $value)
                    ->orWhere('employee_id', null);
            }),
            'department_owners' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('department_id', $value)
                    ->orWhere('department_id', null);
            }),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'code' => $query->where('code', 'ilike', '%' . $value . '%'),
            'iso2' => $query->where('iso2', 'ilike', '%' . $value . '%'),
            'iso3' => $query->where('iso3', 'ilike', '%' . $value . '%'),
            'full_name' => $query->where('full_name', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            default => null,
        };
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    private function getEntity(): CountryEntity
    {
        return new CountryEntity();
    }
}
