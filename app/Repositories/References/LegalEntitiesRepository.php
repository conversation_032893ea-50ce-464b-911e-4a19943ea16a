<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\LegalEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LegalEntitiesRepository implements LegalEntitiesRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const TABLE = 'legal_entities';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::LEGAL_ENTITIES->value, $query::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(isset($filters['search']), function ($query) use ($filters) {
            $query->where(function ($query) use ($filters) {
                $query->where('code', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('short_name', 'ilike', '%' . $filters['search']['value'] . '%');
            });
        });

        $query->when(
            isset($filters['shared_access']),
            fn ($query) => $query->where('shared_access', $filters['shared_access']['value'])
        );

        $query->when(isset($filters['show_only']), function ($query) use ($filters) {
            if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                return $query->where('archived_at', '!=', null);
            }

            if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                return $query->whereNull('archived_at');
            }

            return $query;
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function show(string $id): ?object
    {
        $addressesSubquery = DB::table('legal_detail_addresses AS lda')
            ->select('lda.legal_detail_id')
            ->selectRaw('
                COALESCE(
                    (jsonb_object_agg(
                        \'data\',
                        jsonb_build_object(
                            \'postcode\', lda.postcode,
                            \'country\', lda.country,
                            \'region\', lda.region,
                            \'city\', lda.city,
                            \'street\', lda.street,
                            \'house\', lda.house,
                            \'office\', lda.office,
                            \'other\', lda.other,
                            \'comment\', lda.comment
                        )
                    ) FILTER (where lda.id is not null)) -> \'data\',
                    \'{}\'::jsonb
                ) as address
            ')
            ->groupBy('lda.legal_detail_id');

        return DB::table(self::TABLE . ' as le')
            ->leftJoin('legal_addresses as la', 'le.id', '=', 'la.legal_entity_id')
            ->leftJoin('legal_details as ld', 'le.id', '=', 'ld.legal_entity_id')
            ->leftJoinSub($addressesSubquery, 'lda', 'ld.id', '=', 'lda.legal_detail_id')
            ->leftJoin('legal_accounts as a', 'le.id', '=', 'a.legal_entity_id')
            ->leftJoin('legal_heads as lh', 'le.id', '=', 'lh.legal_entity_id')
            ->leftJoin('files as logo', 'logo.id', '=', 'le.logo_image_id')
            ->leftJoin('files as stamp', 'stamp.id', '=', 'lh.stamp_image_id')
            ->leftJoin('files as accountant_signature', 'accountant_signature.id', '=', 'lh.accountant_signature_image_id')
            ->leftJoin('files as head_signature', 'head_signature.id', '=', 'lh.head_signature_image_id')
            ->where('le.deleted_at', null)
            ->where('le.id', $id)
            ->select([
                'le.id', 'le.deleted_at', 'le.created_at',
                'le.updated_at', 'le.archived_at',
                'le.cabinet_id', 'le.short_name',
                'le.code', 'le.phone',
                'le.fax', 'le.email',
                'le.discount_card', 'le.employee_id',
                'le.department_id',
                'le.is_default',
                DB::raw('COALESCE(
                    CASE
                        WHEN logo.id IS NOT NULL THEN jsonb_build_object(
                                \'id\', logo.id,
                                \'created_at\', logo.created_at,
                                \'updated_at\', logo.updated_at,
                                \'name\', logo.name,
                                \'path\', logo.path,
                                \'size\', logo.size,
                                \'mime_type\', logo.mime_type,
                                \'is_private\', logo.is_private,
                                \'employee_id\', logo.employee_id,
                                \'type\', logo.type
                            )
                        ELSE \'[]\'::jsonb
                    END,
                    \'[]\'::jsonb
                ) AS logo_image'),
                DB::raw('COALESCE((jsonb_object_agg(\'data\', to_jsonb(la.*)) filter (where la.id is not null)) -> \'data\', \'[]\'::jsonb) AS address'),
                DB::raw('COALESCE(
                    (jsonb_object_agg(
                        \'data\',
                        jsonb_build_object(
                            \'taxation_type\', ld.taxation_type,
                            \'tax_rate\', ld.tax_rate,
                            \'vat_rate\', ld.vat_rate,
                            \'type\', ld.type,
                            \'prefix\', ld.prefix,
                            \'inn\', ld.inn,
                            \'kpp\', ld.kpp,
                            \'ogrn\', ld.ogrn,
                            \'okpo\', ld.okpo,
                            \'full_name\', ld.full_name,
                            \'firstname\', ld.firstname,
                            \'patronymic\', ld.patronymic,
                            \'lastname\', ld.lastname,
                            \'ogrnip\', ld.ogrnip,
                            \'certificate_number\', ld.certificate_number,
                            \'certificate_date\', ld.certificate_date,
                            \'address\', COALESCE(lda.address, \'{}\'::jsonb)
                        )
                    ) FILTER (where ld.id is not null)) -> \'data\',
                    \'[]\'::jsonb
                ) AS detail'),
                DB::raw('coalesce(jsonb_agg(a) filter (where a.id is not null), \'[]\') AS accounts'),
                DB::raw('COALESCE(
                    CASE
                        WHEN lh.id IS NOT NULL THEN jsonb_build_object(
                            \'id\', lh.id,
                            \'created_at\', lh.created_at,
                            \'updated_at\', lh.updated_at,
                            \'head_name\', lh.head_name,
                            \'head_position\', lh.head_position,
                            \'accountant_name\', lh.accountant_name,
                            \'stamp\', jsonb_build_object(
                                \'id\', stamp.id,
                                \'created_at\', stamp.created_at,
                                \'updated_at\', stamp.updated_at,
                                \'name\', stamp.name,
                                \'path\', stamp.path,
                                \'size\', stamp.size,
                                \'mime_type\', stamp.mime_type,
                                \'is_private\', stamp.is_private,
                                \'employee_id\', stamp.employee_id,
                                \'type\', stamp.type
                            ),
                            \'accountant_signature\', jsonb_build_object(
                                \'id\', accountant_signature.id,
                                \'created_at\', accountant_signature.created_at,
                                \'updated_at\', accountant_signature.updated_at,
                                \'name\', accountant_signature.name,
                                \'path\', accountant_signature.path,
                                \'size\', accountant_signature.size,
                                \'mime_type\', accountant_signature.mime_type,
                                \'is_private\', accountant_signature.is_private,
                                \'employee_id\', accountant_signature.employee_id,
                                \'type\', accountant_signature.type
                            ),
                            \'head_signature\', jsonb_build_object(
                                \'id\', head_signature.id,
                                \'created_at\', head_signature.created_at,
                                \'updated_at\', head_signature.updated_at,
                                \'name\', head_signature.name,
                                \'path\', head_signature.path,
                                \'size\', head_signature.size,
                                \'mime_type\', head_signature.mime_type,
                                \'is_private\', head_signature.is_private,
                                \'employee_id\', head_signature.employee_id,
                                \'type\', head_signature.type
                            )
                        )
                        ELSE \'[]\'::jsonb
                    END,
                    \'[]\'::jsonb
                ) AS head'),
            ])
            ->groupBy(['le.id', 'logo.id', 'lh.id', 'stamp.id', 'accountant_signature.id', 'head_signature.id'])
            ->first();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function getEntity(): LegalEntity
    {
        return new LegalEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'code' => $query->whereNull('code'),
            'phone' => $query->whereNull('phone'),
            'email' => $query->whereNull('email'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            'inn' => function ($query) {
                $query->whereHas('details', function ($query) {
                    $query->whereNull('inn');
                });
            },
            'address' => function ($query) {
                $query->whereHas('details', function ($query) {
                    $query->whereRaw(
                        "postcode IS NULL AND city IS NULL AND region IS NULL AND house IS NULL
                        AND office IS NULL AND other IS NULL AND street IS NULL"
                    );
                })->with('details');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'code' => $query->whereNotNull('code'),
            'phone' => $query->whereNotNull('phone'),
            'email' => $query->whereNotNull('email'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'inn' => function ($query) {
                $query->whereHas('details', function ($query) {
                    $query->where('inn', '!=', null);
                });
            },
            'address' => function ($query) {
                $query->whereHas('details', function ($query) {
                    $query->whereRaw(
                        "postcode IS NOT NULL OR city IS NOT NULL OR region IS NOT NULL OR house IS NOT NULL
                        OR office IS NOT NULL OR other IS NOT NULL OR street IS NOT NULL"
                    );
                })->with('details');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        $result = match ($filter) {
            'code' => $query->where(function ($query) use ($value) {
                $query->where('code', 'not ilike', '%' . $value . '%')
                    ->orWhere('code', null);
            }),
            'phone' => $query->where(function ($query) use ($value) {
                $query->where('phone', 'not ilike', '%' . $value . '%')
                    ->orWhere('phone', null);
            }),
            'email' => $query->where(function ($query) use ($value) {
                $query->where('email', 'not ilike', '%' . $value . '%')
                    ->orWhere('email', null);
            }),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'inn' => $query->whereHas('details', function ($query) use ($value) {
                $query->where('inn', 'not ilike', '%' . $value . '%')
                    ->orWhere('inn', null);
            }),
            'address' => $query->whereHas('details', function ($query) use ($value) {
                $query->whereRaw(
                    "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') NOT ILIKE ?",
                    ['%' . $value . '%']
                );
            })->with('details'),
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'code' => $query->where('code', 'ilike', '%' . $value . '%'),
            'phone' => $query->where('phone', 'like', '%' . $value . '%'),
            'email' => $query->where('email', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'inn' =>    $query->whereHas('details', function ($query) use ($value) {
                $query->where('inn', 'ilike', '%' . $value . '%');
            }),
            'address' => $query->whereHas('details', function ($query) use ($value) {
                $query->whereRaw(
                    "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') ILIKE ?",
                    ['%' . $value . '%']
                );
            })->with('details'),
            default => null,
        };
    }
}
