<?php

namespace App\Repositories\References\Discounts;

use App\Contracts\Repositories\DiscountContractorGroupRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\DiscountContractorGroupEntity;
use App\Entities\EntityBuilder;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DiscountContractorGroupRepository implements DiscountContractorGroupRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const string TABLE = 'discount_contractor_group';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly DiscountContractorGroupEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->entity;
        $query->select($baseFields)
            ->where('is_system', true)
            ->orWhere(function (EntityBuilder $query) use ($id) {
                $query->where('is_system', false)
                    ->where('cabinet_id', $id);
            });

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function oldContractorGroupIdsForDiscount(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE) // текущие
            ->where('discount_id', $resourceId)
            ->pluck('group_id');
    }

    public function deleteOldContractorGroupWhereGroupIds(string $id, array $recordsToDelete): int
    {
        return DB::table(self::TABLE)
            ->where('discount_id', $id)
            ->whereIn('group_id', $recordsToDelete)
            ->delete();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['discount_id', 'group_id'],
                ['discount_id', 'group_id']
            );
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getEntity(): DiscountContractorGroupEntity
    {
        return $this->entity;
    }


}
