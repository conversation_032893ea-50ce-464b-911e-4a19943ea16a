<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

trait HasJobMutex
{
    protected function acquireLock(string $lockKey, int $ttl = 3600): bool
    {
        $lockKey = $this->formatLockKey($lockKey);
        
        if (Cache::has($lockKey)) {
            Log::info("Job mutex: Lock already exists for {$lockKey}");
            return false;
        }

        $acquired = Cache::put($lockKey, [
            'job_class' => static::class,
            'acquired_at' => now()->toISOString(),
            'process_id' => getmypid(),
            'hostname' => gethostname(),
        ], $ttl);

        if ($acquired) {
            Log::info("Job mutex: Lock acquired for {$lockKey}");
        } else {
            Log::warning("Job mutex: Failed to acquire lock for {$lockKey}");
        }

        return $acquired;
    }

    protected function releaseLock(string $lockKey): bool
    {
        $lockKey = $this->formatLockKey($lockKey);
        $released = Cache::forget($lockKey);
        
        if ($released) {
            Log::info("Job mutex: Lock released for {$lockKey}");
        } else {
            Log::warning("Job mutex: Failed to release lock for {$lockKey}");
        }

        return $released;
    }

    protected function isLocked(string $lockKey): bool
    {
        return Cache::has($this->formatLockKey($lockKey));
    }

    protected function getLockInfo(string $lockKey): ?array
    {
        return Cache::get($this->formatLockKey($lockKey));
    }

    protected function formatLockKey(string $lockKey): string
    {
        return 'job_mutex:' . $lockKey;
    }

    protected function getDefaultLockKey(): string
    {
        return str_replace('\\', '_', static::class);
    }

    protected function executeWithLock(string $lockKey, callable $callback, int $ttl = 3600): mixed
    {
        if (!$this->acquireLock($lockKey, $ttl)) {
            Log::info("Job skipped due to existing lock: {$lockKey}");
            return null;
        }

        try {
            return $callback();
        } finally {
            $this->releaseLock($lockKey);
        }
    }
}
