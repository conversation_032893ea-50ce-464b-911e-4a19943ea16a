<?php

namespace App\Traits;

use Exception;
use InvalidArgumentException;
use App\Enums\Api\Internal\BarcodeEnum;

trait HasBarcodes
{

    public function codeGenerateEanOrGtin(array|string $data, int $length): mixed
    {

        if (is_array($data)) {
            foreach ($data['value'] as $k => $barcode) {
                $dynamicKey = 'value.' . $k;
                $lastEanOrGtin = $barcode;
            }
        } else {
            $dynamicKey = 'value';
            $lastEanOrGtin = $data;
        }

        // Убедитесь, что код имеет длину n символов
        if (strlen($lastEanOrGtin) !== $length) {
            return [
                'message' => "Штрихкод должен состоять из {$length} цифр",
                'errors' => [
                    $dynamicKey => ["Штрихкод должен состоять из {$length} цифр"]
                ],
            ];
        }

        $data = '';
        // Количество штрихкодов для генерации
        $count = 1;

        // Генерируем и выводим только те штрихкоды, у которых контрольная сумма верна
        $validCount = 0;
        while ($validCount < $count) {
            $lastEanOrGtin = $this->generateNextEanOrGtin($lastEanOrGtin, $length);
            if ($this->checkEanOrGtinChecksum($lastEanOrGtin, $length)) {
                $data = $lastEanOrGtin;
                $validCount++;
            }
        }

        return $data;
    }

    // Функция для генерации следующего штрихкода
    public function generateNextEanOrGtin(int $last, int $length): mixed
    {
        // Увеличиваем последний штрихкод на 1
        $last = $last + 1;

        // Проверяем, что штрихкод остается n-значным
        if (strlen((string)$last) > $length) {
            throw new Exception("Штрихкод превышает допустимую длину ({$length} символов).");
        }

        // Возвращаем следующий штрихкод
        return $last;
    }


    // Функция для проверки контрольной суммы
    public function checkEanOrGtinChecksum(string $barcode, int $length): mixed
    {
        $barcodeStr = (string)$barcode;

        $weights = [];
        switch ($length) {
            case 8:
            case 12:
            case 14:
                for ($i = 0; $i < $length - 1; $i++) {
                    $weights[] = ($i % 2 == 0) ? 3 : 1;
                }
                break;
            case 13:
                for ($i = 0; $i < $length - 1; $i++) {
                    $weights[] = ($i % 2 == 0) ? 1 : 3;
                }
                break;
            default:
                throw new InvalidArgumentException("Штрихкод должен быть {$length}-значным.");
                break;
        }

        $sum = 0;
        for ($i = 0; $i < $length - 1; $i++) {
            $digit = (int)$barcodeStr[$i];
            $sum += $digit * $weights[$i];
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$barcodeStr[$length - 1];
    }


    function validateGtinOrUpc(string $barcode, int $typeBarcode, int $dynamicKey = 1)
    {
        $errors = [];
        // Проверяем длину и что все символы - цифры
        if (!preg_match('/^\d{8}$|^\d{12}$|^\d{13}$|^\d{14}$/', $barcode)) {
            if ($typeBarcode == BarcodeEnum::GTIN->value) {
                $errors["barcodes.$dynamicKey.value"] = "Штрихкод GTIN должен содержать только цифры и состоять из 8, 12, 13 или 14 цифр.";
            }
            if ($typeBarcode == BarcodeEnum::UPC->value) {
                $errors["barcodes.$dynamicKey.value"] = "Штрихкод UPC должен содержать только цифры и состоять из 12 цифр.";
            }
            if ($typeBarcode == BarcodeEnum::EAN8->value) {
                $errors["barcodes.$dynamicKey.value"] = "Штрихкод EAN8 должен содержать только цифры и состоять из 8 цифр.";
            }
            if ($typeBarcode == BarcodeEnum::EAN13->value) {
                $errors["barcodes.$dynamicKey.value"] = "Штрихкод EAN13 должен содержать только цифры и состоять из 13 цифр.";
            }
        }

        if ($errors) {
            throw new Exception(json_encode($errors, JSON_UNESCAPED_UNICODE));
        }

        // Извлекаем контрольное число
        $controlDigit = (int) substr($barcode, -1);
        $number = substr($barcode, 0, -1);

        // Рассчитываем контрольное число по алгоритму Луна (Luhn algorithm)
        $checksum = 0;
        $reverseDigits = str_split(strrev($number));

        foreach ($reverseDigits as $i => $digit) {
            $digit = (int) $digit;
            if ($i % 2 === 0) {
                $digit *= 3;
            }
            $checksum += $digit;
        }

        $calculatedControlDigit = (10 - ($checksum % 10)) % 10;

        // Проверяем контрольную сумму
        if ($controlDigit == $calculatedControlDigit) {
            return [
                'message' => BarcodeEnum::fromInt($typeBarcode)->name .  " контрольная сумма верна",
                'success' => [
                    $dynamicKey => [BarcodeEnum::fromInt($typeBarcode)->name .  " контрольная сумма верна"]
                ],
            ];
        } else {
            return [
                'message' => BarcodeEnum::fromInt($typeBarcode)->name .  " Неверная контрольная сумма",
                'errors' => [
                    $dynamicKey => [BarcodeEnum::fromInt($typeBarcode)->name .  " Неверная контрольная сумма"]
                ],
            ];
        }
    }




    public function validateCODE128(?string $barcode, int $typeBarcode, int $dynamicKey = 1): bool
    {

        $errors = [];

        $barcodeTypeName = BarcodeEnum::fromInt($typeBarcode)->name;

        if ($barcode === null) {
            $errors["barcodes.$dynamicKey.value"] = "$barcodeTypeName штрихкод не должен быть пустым";
        } else {
            $response = preg_match('/^[a-zA-Z0-9!@#$%^&*()\-=+[\]{}|;:\'",.<>\/?]+$/', $barcode);

            if (!$response) {
                $errors["barcodes.$dynamicKey.value"] = "$barcodeTypeName штрихкод не должен содержать кириллицу";
            }

            if (strlen($barcode) >= 30) {
                $errors["barcodes.$dynamicKey.value"] = "$barcodeTypeName штрихкод не должен превышать 30 символов";
            }
        }

        if ($errors) {
            throw new Exception(json_encode($errors, JSON_UNESCAPED_UNICODE));
        }

        return true;
    }


    public function codeGenerateEAN13(array|string $data): mixed
    {

        if (is_array($data)) {
            foreach ($data['value'] as $k => $barcode) {
                $dynamicKey = 'value.' . $k;
                $lastEAN13 = $barcode;
            }
        } else {
            $dynamicKey = 'value';
            $lastEAN13 = $data;
        }

        // Убедитесь, что код имеет длину 13 символов
        if (strlen($lastEAN13) !== 13) {
            return [
                'message' => 'EAN-13 должен состоять из 13 цифр',
                'errors' => [
                    $dynamicKey => ['EAN-13 должен состоять из 13 цифр']
                ],
            ];
        }

        $data = '';
        // Количество штрихкодов для генерации
        $count = 1;

        // Генерируем и выводим только те штрихкоды, у которых контрольная сумма верна
        $validEAN13Count = 0;
        while ($validEAN13Count < $count) {
            $lastEAN13 = $this->generateNextEAN13($lastEAN13);
            if ($this->checkEAN13Checksum($lastEAN13)) {
                $data = $lastEAN13;
                $validEAN13Count++;
            }
        }

        return $data;
    }



    // Функция для генерации следующего штрихкода EAN-13
    public function generateNextEAN13(int $lastEAN13): mixed
    {
        // Увеличиваем последний штрихкод на 1
        $nextEAN13 = $lastEAN13 + 1;

        // Проверяем, что штрихкод остается 13-значным
        if (strlen((string)$nextEAN13) > 13) {
            throw new Exception("Штрихкод превышает допустимую длину (13 символов).");
        }

        // Возвращаем следующий штрихкод
        return $nextEAN13;
    }

    // Функция для проверки контрольной суммы EAN-13
    public function checkEAN13Checksum(string $ean13): mixed
    {
        $ean13Str = (string)$ean13;
        if (strlen($ean13Str) !== 13) {
            throw new Exception("Штрихкод должен быть 13-значным.");
        }

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $digit = (int)$ean13Str[$i];
            if ($i % 2 == 0) {
                $sum += $digit;
            } else {
                $sum += $digit * 3;
            }
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$ean13Str[12];
    }


    // Валидация EAN13
    public function validateEAN13(array|string $data): mixed
    {
        if (is_array($data)) {
            foreach ($data['barcodes'] as $k => $barcode) {
                $dynamicKey = 'value.' . $k;
                $code = $barcode['value'];
            }
        } else {
            $dynamicKey = 'value';
            $code = $data;
        }

        // Убедитесь, что код имеет длину 13 символов
        if (strlen($code) !== 13) {
            return [
                'message' => 'EAN-13 должен состоять из 13 цифр',
                'errors' => [
                    $dynamicKey => ['EAN-13 должен состоять из 13 цифр']
                ],
            ];
        }

        // Получаем первые 12 цифр кода
        $codeWithoutCheckDigit = substr($code, 0, 12);

        // Вычисляем контрольную сумму
        $calculatedCheckDigit = $this->calculateEAN13CheckDigit($codeWithoutCheckDigit);

        // Получаем последнюю цифру кода (контрольную сумму)
        $providedCheckDigit = substr($code, 12, 1);

        // Проверяем контрольную сумму
        if ($calculatedCheckDigit == $providedCheckDigit) {
            return [
                'message' => 'EAN-13 контрольная цифра верна',
                'success' => [
                    $dynamicKey => ['EAN-13 контрольная цифра верна']
                ],
            ];
        } else {
            return [
                'message' => 'EAN-13 Неверная контрольная цифра',
                'errors' => [
                    $dynamicKey => ['EAN-13 Неверная контрольная цифра']
                ],
            ];
        }
    }

    public function calculateEAN13CheckDigit(string $code): mixed
    {
        // Преобразуем код в массив цифр
        $digits = str_split($code);

        // Инициализируем суммы для четных и нечетных позиций
        $oddSum = 0;
        $evenSum = 0;

        // Проходим по цифрам и суммируем их с учетом позиции
        for ($i = 0; $i < 12; $i++) {
            if ($i % 2 == 0) {
                $oddSum += $digits[$i];
            } else {
                $evenSum += $digits[$i];
            }
        }

        // Вычисляем общую сумму
        $totalSum = $oddSum + $evenSum * 3;

        // Вычисляем контрольную сумму
        $checkDigit = (10 - ($totalSum % 10)) % 10;

        return $checkDigit;
    }



    /**
     * Всё для EAN8
     */

    // Получаем существующий штрихкод и отдаём следующий EAN8
    public function codeGenerateEAN8(array|string $data): mixed
    {
        if (is_array($data)) {
            foreach ($data['value'] as $k => $barcode) {
                $dynamicKey = 'value.' . $k;
                $lastEAN8 = $barcode;
            }
        } else {
            $dynamicKey = 'value';
            $lastEAN8 = $data;
        }


        // Убедитесь, что код имеет длину 8 символов
        if (strlen($lastEAN8) !== 8) {
            return [
                'message' => 'EAN-8 должен состоять из 8 цифр',
                'errors' => [
                    $dynamicKey => ['EAN-8 должен состоять из 8 цифр']
                ],
            ];
        }


        $data = '';
        // Количество штрихкодов для генерации
        $count = 1;

        // Генерируем и выводим только те штрихкоды, у которых контрольная сумма верна
        $validEAN8Count = 0;
        while ($validEAN8Count < $count) {
            $lastEAN8 = $this->generateNextEAN8($lastEAN8);
            if ($this->checkEAN8Checksum($lastEAN8)) {
                $data = $lastEAN8;
                $validEAN8Count++;
            }
        }

        return $data;
    }



    // Функция для генерации следующего штрихкода EAN-8
    public function generateNextEAN8(int $lastEAN8): mixed
    {
        // Увеличиваем последний штрихкод на 1
        $nextEAN8 = $lastEAN8 + 1;

        // Проверяем, что штрихкод остается 8-значным
        if (strlen((string)$nextEAN8) > 8) {
            throw new Exception("Штрихкод превышает допустимую длину (8 символов).");
        }

        // Возвращаем следующий штрихкод
        return $nextEAN8;
    }

    // Функция для проверки контрольной суммы EAN-8
    public function checkEAN8Checksum(string $ean8): mixed
    {
        $ean8Str = (string)$ean8;
        if (strlen($ean8Str) !== 8) {
            throw new Exception("Штрихкод должен быть 8-значным.");
        }

        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $digit = (int)$ean8Str[$i];
            if ($i % 2 == 0) {
                $sum += $digit * 3;
            } else {
                $sum += $digit;
            }
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$ean8Str[7];
    }


    // Валидация EAN8
    public function validateEAN8(array|string $data): mixed
    {
        if (is_array($data)) {
            foreach ($data['barcodes'] as $k => $barcode) {
                $dynamicKey = 'value.' . $k;
                $code = $barcode['value'];
            }
        } else {
            $dynamicKey = 'value';
            $code = $data;
        }

        // Убедитесь, что код имеет длину 8 символов
        if (strlen($code) !== 8) {
            return [
                'message' => 'EAN-8 должен состоять из 8 цифр',
                'errors' => [
                    $dynamicKey => ['EAN-8 должен состоять из 8 цифр']
                ],
            ];
        }

        // Получаем первые 7 цифр кода
        $codeWithoutCheckDigit = substr($code, 0, 7);

        // Вычисляем контрольную сумму
        $calculatedCheckDigit = $this->calculateEAN8CheckDigit($codeWithoutCheckDigit);

        // Получаем последнюю цифру кода (контрольную сумму)
        $providedCheckDigit = substr($code, 7, 1);

        // Проверяем контрольную сумму
        if ($calculatedCheckDigit == $providedCheckDigit) {
            return [
                'message' => 'EAN-8 контрольная цифра верна',
                'success' => [
                    $dynamicKey => ['EAN-8 контрольная цифра верна']
                ],
            ];
        } else {
            return [
                'message' => 'EAN-8 Неверная контрольная цифра',
                'errors' => [
                    $dynamicKey => ['EAN-8 Неверная контрольная цифра']
                ],
            ];
        }
    }

    public function calculateEAN8CheckDigit(string $code): mixed
    {

        // Преобразуем код в массив цифр
        $digits = str_split($code);

        // Инициализируем суммы для четных и нечетных позиций
        $oddSum = 0;
        $evenSum = 0;

        // Проходим по цифрам и суммируем их с учетом позиции
        for ($i = 0; $i < 7; $i++) {
            if ($i % 2 == 0) {
                $oddSum += $digits[$i];
            } else {
                $evenSum += $digits[$i];
            }
        }

        // Вычисляем общую сумму
        $totalSum = $oddSum * 3 + $evenSum;

        // Вычисляем контрольную сумму
        $checkDigit = (10 - ($totalSum % 10)) % 10;

        return $checkDigit;
    }

    /**
     * Универсальный генератор GTIN (8, 12, 13 или 14 цифр)
     *
     * @param int $length Длина GTIN (8, 12, 13 или 14)
     * @return string Сгенерированный GTIN
     * @throws InvalidArgumentException Если длина неверная
     */
    function generateGTIN(int $code)
    {

        $length = strlen($code);

        switch ($length) {
            case 8:
                $res = $this->codeGenerateEanOrGtin($code, 8);
                break;
            case 12:
                $res = $this->codeGenerateEAN13($code, 12);
                break;
            case 13:
                $res = $this->codeGenerateEanOrGtin($code, 13);
                break;
            case 14:
                $res = $this->codeGenerateEAN13($code, 14);
                break;
            default:
                throw new InvalidArgumentException('GTIN должен быть длиной 8, 12, 13 или 14 цифр.');
                break;
        }

        return $res;
    }
}
