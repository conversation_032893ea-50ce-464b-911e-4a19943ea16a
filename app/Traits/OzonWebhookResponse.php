<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

trait OzonWebhookResponse
{
    protected function ozonSuccessResponse(array $data = []): JsonResponse
    {
        return response()->json($data, Response::HTTP_OK);
    }

    protected function ozonErrorResponse(
        string $message, 
        int $status = Response::HTTP_INTERNAL_SERVER_ERROR,
        string $code = 'ERROR_UNKNOWN',
        mixed $details = null
    ): JsonResponse {
        return response()->json([
            'error' => [
                'code' => $code,
                'message' => $message,
                'details' => $details
            ]
        ], $status);
    }

    protected function ozonValidationErrorResponse(string $message, mixed $details = null): JsonResponse
    {
        return $this->ozonErrorResponse($message, Response::HTTP_BAD_REQUEST, 'VALIDATION_ERROR', $details);
    }

    protected function ozonUnauthorizedResponse(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->ozonErrorResponse($message, Response::HTTP_UNAUTHORIZED, 'UNAUTHORIZED');
    }

    protected function ozonForbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return $this->ozonErrorResponse($message, Response::HTTP_FORBIDDEN, 'FORBIDDEN');
    }

    protected function ozonNotFoundResponse(string $message = 'Not Found'): JsonResponse
    {
        return $this->ozonErrorResponse($message, Response::HTTP_NOT_FOUND, 'NOT_FOUND');
    }

    protected function executeOzonAction(callable $action): JsonResponse
    {
        try {
            return $action();
        } catch (\Throwable $e) {
            return $this->ozonErrorResponse(
                'Internal server error',
                Response::HTTP_INTERNAL_SERVER_ERROR,
                'ERROR_UNKNOWN'
            );
        }
    }
} 