<?php

namespace App\Modules\AI\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\AI\Repository\v1\EmployeeRepository;
use App\Modules\AI\Repository\v1\WarehouseRepository;
use App\Modules\AI\Repository\v1\DepartmentRepository;
use App\Modules\AI\Repository\v1\LegalEntityRepository;
use App\Modules\AI\Repository\v1\SalesChannelsRepository;
use App\Modules\AI\Repository\v1\CabinetCurrencyRepository;
use App\Modules\AI\Contracts\v1\Repository\EmployeeRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\WarehouseRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\DepartmentRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\LegalEntityRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\SalesChannelsRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\CabinetCurrencyRepositoryContract;

class AppRepositoryProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(WarehouseRepositoryContract::class, WarehouseRepository::class);
        $this->app->singleton(LegalEntityRepositoryContract::class, LegalEntityRepository::class);
        $this->app->singleton(EmployeeRepositoryContract::class, EmployeeRepository::class);
        $this->app->singleton(DepartmentRepositoryContract::class, DepartmentRepository::class);
        $this->app->singleton(SalesChannelsRepositoryContract::class, SalesChannelsRepository::class);
        $this->app->singleton(CabinetCurrencyRepositoryContract::class, CabinetCurrencyRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
