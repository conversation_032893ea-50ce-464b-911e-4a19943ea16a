<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBS;

use App\Actions\Ozon\SyncStocksAction;
use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class StocksController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly SyncStocksAction $syncStocksAction
    ) {
    }

    public function syncStocks(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $this->syncStocksAction->handle($integrationId);

            return $this->noContentResponse();
        });
    }
} 