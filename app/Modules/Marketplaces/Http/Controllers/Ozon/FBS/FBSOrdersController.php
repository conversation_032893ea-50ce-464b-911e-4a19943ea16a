<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBS;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\CancelFBSOrderItemsRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\CancelFBSOrderRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetFBSOrdersCancelReasonRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetFBSOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetPackageLabelsRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetCutoffDateRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetDeliveredStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetDeliveringStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetLastMileStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetTimeslotRequest;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Services\Ozon\Services\FBS\FBSOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS заказами Wildberries
 */
class FBSOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly FBSOrdersService $ordersService
    ) {
    }
    /**
     * Загрузка FBS заказов
     */
    public function loadOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }

    /**
     * Отмена FBS заказа
     */
    public function cancelOrder(CancelFBSOrderRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelOrder(
                cancelReasonId: $validated['cancel_reason_id'],
                orderId: $orderId,
                cancelReasonMessage: $validated['cancel_reason_message'] ?? null,
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Подтверждение FBS заказа
     */
    public function confirmOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->confirmFBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    public function indexFbs(GetFBSOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();
            $orders = $this->ordersService->getOrders($integrationId, $filters);

            return $this->successResponse([
                'orders' => $orders,
                'page' => $filters['page'] ?? 1,
                'per_page' => $filters['per_page'] ?? 15,
            ]);
        });
    }

    public function getCancelReason(GetFBSOrdersCancelReasonRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->checkPermissionsToIntegration($data['integration_id']);

            $result = $this->ordersService->getCancelReasons($data['integration_id'], $data['related_posting_numbers']);

            return $this->successResponse($result);
        });
    }

    /**
     * Отмена отдельных товаров в FBS заказе
     *
     * Если отменяется часть товаров, количество в БД уменьшается на указанное количество.
     * Если отменяется всё количество товара, запись удаляется из БД.
     */
    public function cancelOrderItems(CancelFBSOrderItemsRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelOrderItems(
                cancelReasonId: $validated['cancel_reason_id'],
                orderId: $orderId,
                cancelReasonMessage: $validated['cancel_reason_message'],
                items: $validated['items'],
            );

            return $this->noContentResponse();
        });
    }

    public function setProductCountry(SetOrderItemCountryRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->setProductCountry(
                orderId: $orderId,
                productId: $validated['sku'],
                countryIsoCode: $validated['country_iso2'],
            );

            return $this->noContentResponse();
        });
    }

    public function collectOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->collectOrder(
                orderId: $orderId,
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Получить этикетки для FBS отправлений
     */
    public function getPackageLabels(GetPackageLabelsRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $validated = $request->validated();

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $result = $this->ordersService->getPackageLabels($validated['order_ids']);

            return $this->successResponse($result);
        });
    }

    public function setCutoffDate(SetCutoffDateRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $validated = $request->validated();

            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->setCutoffDate(
                orderId: $orderId,
                newCutoffDate: $validated['new_cutoff_date']
            );

            $this->noContentResponse();
        });
    }

    public function setTimeslot(SetTimeslotRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $validated = $request->validated();

            $this->ordersService->setTimeslot(
                orderId: $orderId,
                newTimeslot: $validated['new_timeslot']
            );

            return $this->noContentResponse();
        });
    }

    public function getTimeslotChangeRestrictions(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $result = $this->ordersService->getTimeslotChangeRestrictions($orderId);
            return $this->successResponse($result);
        });
    }

    public function setDeliveringStatus(SetDeliveringStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToIntegration($integrationId);

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setDeliveringStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

    public function setLastMileStatus(SetLastMileStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setLastMileStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

    public function setDeliveredStatus(SetDeliveredStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToIntegration($integrationId);

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setDeliveredStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

}
