<?php

namespace App\Modules\Marketplaces\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Contracts\ModuleAuthInterface;
use App\Modules\Marketplaces\Factories\MarketplacePolicyFactory;
use App\Modules\Marketplaces\Factories\MarketplaceRequestFactory;
use App\Modules\Marketplaces\Http\Requests\CancelMatchRequest;
use App\Modules\Marketplaces\Http\Requests\ConfirmMatchRequest;
use App\Modules\Marketplaces\Http\Requests\CreateProductFromMatchSimpleRequest;
use App\Modules\Marketplaces\Http\Requests\FindSuggestedMatchesRequest;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\LoadReportsRequest;
use App\Modules\Marketplaces\Http\Requests\ManualMatchRequest;
use App\Modules\Marketplaces\Http\Requests\MarketplaceIndexRequest;
use App\Modules\Marketplaces\Http\Requests\UpdateMatchedProductRequest;
use App\Modules\Marketplaces\Services\MarketplaceService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MarketplaceController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly MarketplaceService $service,
        private readonly MarketplacePolicyFactory $policyFactory,
        private readonly ModuleAuthInterface $policy
    ) {
    }

    public function index(MarketplaceIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $cabinetId = $request->validated('cabinet_id');

            $this->policy->hasAccessToCabinet($cabinetId);

            $data = $this->service->index($cabinetId);

            return $this->successResponse($data);
        });
    }

    public function store(Request $request, string $type): ?JsonResponse
    {
        $marketplaceRequest = MarketplaceRequestFactory::createOrUpdateRequest($type, $request);

        return $this->executeAction(function () use ($marketplaceRequest, $type) {

            $dto = $marketplaceRequest->toDto();

            $this->policyFactory
                ->create($type)
                ->create($dto);

            $id = $this->service
                ->getDriver($type)
                ->create($dto);

            return $this->createdResponse($id);
        });
    }

    public function show(string $type, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $data = $this->service
                ->getDriver($type)
                ->show($id);

            return $this->successResponse($data);
        });
    }

    public function update(Request $request, string $type, string $id): ?JsonResponse
    {
        $marketplaceRequest = MarketplaceRequestFactory::createOrUpdateRequest($type, $request);

        return $this->executeAction(function () use ($marketplaceRequest, $type, $id) {
            $dto = $marketplaceRequest->toDto();

            $this->policyFactory
                ->create($type)
                ->update($dto, $id);

            $this->service
                ->getDriver($type)
                ->update($dto, $id);

            return $this->noContentResponse();
        });
    }

    public function destroy(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->delete($id);

            return $this->noContentResponse();
        });
    }

    public function connect(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {

            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->connect($id);

            return $this->noContentResponse();
        });
    }

    public function disconnect(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);
            $this->service
                ->getDriver($type)
                ->disconnect($id);

            return $this->noContentResponse();
        });
    }

    public function loadProducts(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);
            $this->service
                ->getDriver($type)
                ->loadProducts($id);

            return $this->noContentResponse();
        });
    }

    public function loadPrices(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadPrices($id);

            return $this->noContentResponse();
        });
    }

    public function loadResidues(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadResidues($id);

            return $this->noContentResponse();
        });
    }

    public function loadOrders(LoadOrdersRequest $request, string $type, string $id): ?JsonResponse
    {
        $dateFrom = $request->validated('date_from');
        $dateTo = $request->validated('date_to');

        return $this->executeAction(function () use ($type, $id, $dateFrom, $dateTo) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadOrders($id, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }



    public function loadReports(LoadReportsRequest $request, string $type, string $id): ?JsonResponse
    {
        $period = $request->validated();

        return $this->executeAction(function () use ($type, $id, $period) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadReports($id, $period['date_from'], $period['date_to']);

            return $this->noContentResponse();
        });
    }

    /**
     * Получает список сопоставленных товаров для указанного кабинета и интеграции
     *
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     */
    public function getMatchedProducts(string $type, string $cabinetId, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($type, $cabinetId, $integrationId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $result = $this->service
                ->getDriver($type)
                ->getMatchedProducts($cabinetId, $integrationId);

            return $this->successResponse($result);
        });
    }

    /**
     * Получает список товаров к сопоставлению для указанного кабинета и интеграции
     *
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     */
    public function getProductsToMatch(string $type, string $cabinetId, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($type, $cabinetId, $integrationId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $result = $this->service
                ->getDriver($type)
                ->getProductsToMatch($cabinetId, $integrationId);

            return $this->successResponse($result);
        });
    }

    /**
     * Находит предполагаемые соответствия для всех товаров интеграции
     *
     * @param  FindSuggestedMatchesRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     */
    public function findSuggestedMatches(FindSuggestedMatchesRequest $request, string $type, string $cabinetId, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId, $integrationId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $matchType = $request->getMatchType();

            $result = $this->service
                ->getDriver($type)
                ->findSuggestedMatches($cabinetId, $integrationId, $matchType);

            return $this->successResponse($result);
        });
    }

    /**
     * Подтверждает сопоставление товаров из маркетплейса с товарами в системе
     *
     * @param  ConfirmMatchRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     */
    public function confirmMatch(ConfirmMatchRequest $request, string $type, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $matches = $request->getMatches();

            $this->service
                ->getDriver($type)
                ->confirmMatch($cabinetId, $matches);

            return $this->noContentResponse();
        });
    }

    /**
     * Создает новый товар в системе из товара к сопоставлению и сопоставляет их
     *
     * @param  CreateProductFromMatchSimpleRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     */
    public function createProductFromMatch(CreateProductFromMatchSimpleRequest $request, string $type, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $productToMatchId = $request->getProductToMatchId();

            $result = $this->service
                ->getDriver($type)
                ->createProductFromMatch($cabinetId, $productToMatchId);

            return $this->successResponse(['matched_product_id' => $result]);
        });
    }

    /**
     * Ручное сопоставление товара из маркетплейса с товаром в системе
     *
     * @param  ManualMatchRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     */
    public function manualMatch(ManualMatchRequest $request, string $type, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $productToMatchId = $request->getProductToMatchId();
            $productId = $request->getProductId();

            $result = $this->service
                ->getDriver($type)
                ->manualMatch($cabinetId, $productToMatchId, $productId);

            return $this->successResponse(['matched_product_id' => $result]);
        });
    }

    /**
     * Отменяет сопоставление товаров из маркетплейса и возвращает их в список товаров к сопоставлению
     *
     * @param  CancelMatchRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     */
    public function cancelMatch(CancelMatchRequest $request, string $type, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $matchedProductIds = $request->getMatchedProductIds();

            $this->service
                ->getDriver($type)
                ->cancelMatch($cabinetId, $matchedProductIds);

            return $this->noContentResponse();
        });
    }

    /**
     * Изменяет товар в системе, к которому сопоставлен товар из маркетплейса
     *
     * @param  UpdateMatchedProductRequest  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $cabinetId  ID кабинета
     */
    public function updateMatchedProduct(UpdateMatchedProductRequest $request, string $type, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $type, $cabinetId) {
            $this->policyFactory
                ->create($type)
                ->index($cabinetId);

            $matchedProductId = $request->getMatchedProductId();
            $productId = $request->getProductId();

            $this->service
                ->getDriver($type)
                ->updateMatchedProduct($cabinetId, $matchedProductId, $productId);

            return $this->noContentResponse();
        });
    }

    public function updateCostAccountingSettings(Request $request, string $type, string $id): JsonResponse
    {
        $request = MarketplaceRequestFactory::updateCostAccountingSettings($type, $request);

        return $this->executeAction(function () use ($request, $type, $id) {
            $dto = $request->toDto($id);

            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->updateCostAccountingSettings($dto, $id);

            return $this->noContentResponse();
        });
    }
}
