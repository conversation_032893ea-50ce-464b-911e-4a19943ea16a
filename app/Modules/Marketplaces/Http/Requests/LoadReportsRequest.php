<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoadReportsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'date_from' => 'required|date_format:Y-m-d|before_or_equal:date_to',
            'date_to' => 'required|date_format:Y-m-d|after_or_equal:date_from',
        ];
    }
}
