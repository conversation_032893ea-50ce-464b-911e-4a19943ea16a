<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes;

use Illuminate\Foundation\Http\FormRequest;

class DeleteBoxInSupplyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'box_ids' => ['required', 'array', 'min:1', 'max:50'],
            'box_ids.*' => ['required', 'string', 'uuid'],
        ];
    }

    public function messages(): array
    {
        return [
            'box_ids.required' => 'Список ID коробок обязателен',
            'box_ids.array' => 'Список ID коробок должен быть массивом',
            'box_ids.min' => 'Необходимо указать минимум 1 коробку',
            'box_ids.max' => 'Максимальное количество коробок: 50',
            'box_ids.*.uuid' => 'Каждый ID коробки должен быть валидным UUID',
        ];
    }
}
