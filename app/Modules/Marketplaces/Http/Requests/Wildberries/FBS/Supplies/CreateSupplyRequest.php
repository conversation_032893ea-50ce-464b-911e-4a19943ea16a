<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Supplies;

use Illuminate\Foundation\Http\FormRequest;

class CreateSupplyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'integration_id' => ['required', 'string', 'uuid'],
            'name' => ['required', 'string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'integration_id.required' => 'ID интеграции обязателен',
            'integration_id.uuid' => 'ID интеграции должен быть валидным UUID',
            'name.required' => 'Название поставки обязательно для заполнения',
            'name.string' => 'Название поставки должно быть строкой',
            'name.max' => 'Название поставки не должно превышать 255 символов',
        ];
    }
}
