<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Warehouses;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFBSWarehouseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'warehouse_id' => ['required', 'string', 'uuid'],
            'name' => ['string', 'max:255'],
            'is_active' => ['boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'warehouse_id.required' => 'ID склада обязателен',
            'warehouse_id.uuid' => 'ID склада должен быть валидным UUID',
            'name.max' => 'Название склада не должно превышать 255 символов',
            'is_active.boolean' => 'Статус активности должен быть булевым значением',
        ];
    }
}
