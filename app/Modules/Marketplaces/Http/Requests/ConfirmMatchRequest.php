<?php

namespace App\Modules\Marketplaces\Http\Requests;

use App\Modules\Marketplaces\Enums\ProductMatchingTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConfirmMatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'matches' => 'required|array|min:1',
            'matches.*.product_to_match_id' => 'required|string|uuid',
            'matches.*.product_id' => 'required|string|uuid',
            'matches.*.match_type' => ['required', 'string', Rule::in(ProductMatchingTypeEnum::cases())],
        ];
    }

    /**
     * Получить массив сопоставлений из запроса
     *
     * @return array
     */
    public function getMatches(): array
    {
        return $this->validated('matches');
    }
}
