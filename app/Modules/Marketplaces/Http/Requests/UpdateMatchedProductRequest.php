<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMatchedProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'matched_product_id' => 'required|string|uuid',
            'product_id' => 'required|string|uuid',
        ];
    }

    /**
     * Получить ID сопоставленного товара из запроса
     *
     * @return string
     */
    public function getMatchedProductId(): string
    {
        return $this->validated('matched_product_id');
    }

    /**
     * Получить новый ID товара в системе из запроса
     *
     * @return string
     */
    public function getProductId(): string
    {
        return $this->validated('product_id');
    }
}
