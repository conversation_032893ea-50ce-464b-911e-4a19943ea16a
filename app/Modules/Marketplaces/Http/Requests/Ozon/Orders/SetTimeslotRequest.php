<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Orders;

use Illuminate\Foundation\Http\FormRequest;

class SetTimeslotRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'new_timeslot' => ['required', 'array'],
            'new_timeslot.from' => ['required', 'date_format:Y-m-d\TH:i:s.v\Z', 'after_or_equal:today'],
            'new_timeslot.to' => ['required', 'date_format:Y-m-d\TH:i:s.v\Z', 'after:new_timeslot.from'],
        ];
    }
}
