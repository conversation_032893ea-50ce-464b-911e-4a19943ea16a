<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Carriages;

use Illuminate\Foundation\Http\FormRequest;

class GetDigitalDocumentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'doc_type' => ['nullable', 'string', 'in:act_of_acceptance,act_of_mismatch,act_of_excess'],
        ];
    }
}
