<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Carriages;

use Illuminate\Foundation\Http\FormRequest;

class CreateArrivalPassRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'arrival_passes' => ['required', 'array', 'min:1'],
            'arrival_passes.*.driver_name' => ['required', 'string', 'max:255'],
            'arrival_passes.*.driver_phone' => ['required', 'string', 'max:20'],
            'arrival_passes.*.vehicle_license_plate' => ['required', 'string', 'max:20'],
            'arrival_passes.*.vehicle_model' => ['required', 'string', 'max:255'],
            'arrival_passes.*.with_returns' => ['nullable', 'boolean'],
        ];
    }
}
