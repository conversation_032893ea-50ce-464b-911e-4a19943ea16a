<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFBSWarehouseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'warehouse_id' => ['required', 'string', 'uuid'],
            'name' => ['string', 'max:255'],
            'is_active' => ['boolean'],
        ];
    }
}
