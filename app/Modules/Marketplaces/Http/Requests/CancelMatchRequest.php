<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CancelMatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'matched_product_ids' => 'required|array|min:1',
            'matched_product_ids.*' => 'required|string|uuid',
        ];
    }

    /**
     * Получить массив ID сопоставленных товаров из запроса
     *
     * @return array
     */
    public function getMatchedProductIds(): array
    {
        return $this->validated('matched_product_ids');
    }
}
