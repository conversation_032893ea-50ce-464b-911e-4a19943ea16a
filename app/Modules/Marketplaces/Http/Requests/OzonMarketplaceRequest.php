<?php

namespace App\Modules\Marketplaces\Http\Requests;

use App\Modules\Marketplaces\Services\Ozon\Enums\OrderNumberingTypeEnum;
use Illuminate\Validation\Rule;

class OzonMarketplaceRequest extends BaseMarketplaceRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'cabinet_id' => 'required|string|uuid',
            'name' => 'required|string|max:255',
            'client_id' => 'required|string|max:100',
            'api_key' => 'required|string',
            'legal_entity_id' => 'required|string|uuid',
            'contractor_id' => 'required|string|uuid',
            'comission_contract_id' => 'required|uuid',
            'department_id' => 'required|string|uuid',
        ];

        if ($this->isMethod('PUT')) {
            // Настройки синхронизации цен
            $rules['price_sync.your_price_id'] = 'required|string|uuid';
            $rules['price_sync.prediscount_price_id'] = 'sometimes|nullable|string|uuid';
            $rules['price_sync.min_price_id'] = 'sometimes|nullable|string|uuid';
            $rules['price_sync.auto_sync'] = 'sometimes|boolean';

            // Настройки синхронизации заказов
            $rules['order_sync.num_type'] = ['sometimes', 'string', Rule::in(OrderNumberingTypeEnum::cases())];
            $rules['order_sync.add_prefix'] = 'sometimes|boolean';
            $rules['order_sync.prefix'] = 'sometimes|string|max:50';
            $rules['order_sync.use_common_block_contract'] = 'sometimes|boolean';
            $rules['order_sync.reserve'] = 'sometimes|boolean';
            $rules['order_sync.sync_order_statuses'] = 'sometimes|boolean';
            $rules['order_sync.auto_accept_orders'] = 'sometimes|boolean';
            $rules['order_sync.fair_mark'] = 'sometimes|boolean';
            $rules['order_sync.auto_sync'] = 'sometimes|boolean';

            // Настройки синхронизации отчетов
            $rules['report_sync.auto_sync'] = 'sometimes|boolean';
        }

        return $rules;
    }

    protected function getMarketplaceType(): string
    {
        return 'ozon';
    }
}
