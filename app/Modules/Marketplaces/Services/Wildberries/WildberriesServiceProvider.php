<?php

namespace App\Modules\Marketplaces\Services\Wildberries;

use App\Modules\Marketplaces\Contracts\Wildberries\WildberriesOrderMappingsRepositoryContract;
use App\Modules\Marketplaces\Contracts\Wildberries\WildberriesOrdersRepositoryContract;
use App\Modules\Marketplaces\Contracts\Wildberries\WildberriesProductMappingsRepositoryContract;
use App\Modules\Marketplaces\Services\Wildberries\Repositories\WildberriesOrderMappingsRepository;
use App\Modules\Marketplaces\Services\Wildberries\Repositories\WildberriesOrdersRepository;
use App\Modules\Marketplaces\Services\Wildberries\Repositories\WildberriesProductMappingsRepository;
use Illuminate\Support\ServiceProvider;

class WildberriesServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(WildberriesOrderMappingsRepositoryContract::class, WildberriesOrderMappingsRepository::class);
        $this->app->singleton(WildberriesOrdersRepositoryContract::class, WildberriesOrdersRepository::class);
        $this->app->singleton(WildberriesProductMappingsRepositoryContract::class, WildberriesProductMappingsRepository::class);
    }

    public function boot(): void
    {
        $migrationsPath = __DIR__.'/migrations';
        $this->loadMigrationsFrom($migrationsPath);
    }
}
