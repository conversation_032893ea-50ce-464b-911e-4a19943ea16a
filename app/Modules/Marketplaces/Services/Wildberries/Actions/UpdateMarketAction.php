<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use App\Modules\Marketplaces\Services\Wildberries\Data\MarketData;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class UpdateMarketAction
{
    private Carbon $time;

    public function __construct()
    {
        $this->time = Carbon::now();
    }

    public function run(MarketData $data, string $id): void
    {
        try {
            DB::beginTransaction();

            $updated = $this->updateIntegration($id, $data);

            if ($updated) {
                $this->updateOrderSettings($id, $data);
                $this->updatePriceSettings($id, $data);
                $this->updateReportSettings($id, $data);
            }


            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new RuntimeException($e->getMessage());
        }
    }

    /**
     * @param string $id
     * @param MarketData $data
     * @return bool
     */
    private function updateIntegration(string $id, MarketData $data): bool
    {
        return DB::table('wildberries_integrations')
            ->where('id', $id)
            ->update([
                'updated_at' => $this->time,

                'shop_name' => $data->shopName,

                'token' => encrypt($data->token),

                'legal_entity_id' => $data->legalEntityId,
                'department_id' => $data->departmentId,
                'contractor_id' => $data->contractorId,
                'commission_contract_id' => $data->comissionContractId
            ]) > 0;
    }

    /**
     * @param string $id
     * @param MarketData $data
     * @return void
     */
    private function updateOrderSettings(string $id, MarketData $data): void
    {
        DB::table('wildberries_order_settings')
            ->where('integration_id', $id)
            ->update(
                [
                    'numbering_type' => $data->orderSyncSettingsData->numType,
                    'add_prefix_to_orders' => $data->orderSyncSettingsData->addPrefix,
                    'order_prefix' => $data->orderSyncSettingsData->prefix,
                    'use_common_agreement' => $data->orderSyncSettingsData->useCommonBlockContract,
                    'sync_statuses' => $data->orderSyncSettingsData->syncOrderStatuses,
                    'reserve_from_inventory' => $data->orderSyncSettingsData->reserve,
                    'send_mark_codes' => $data->orderSyncSettingsData->fairMark,
                    'print_label_size' => $data->orderSyncSettingsData->printLabelSize,
                    'auto_sync' => $data->orderSyncSettingsData->autoSync,

                    'updated_at' => $this->time,
                ]
            );
    }

    /**
     * @param string $id
     * @param MarketData $data
     * @return void
     */
    private function updatePriceSettings(string $id, MarketData $data): void
    {
        DB::table('wildberries_price_settings')
            ->where('integration_id', $id)
            ->update([
                'updated_at' => $this->time,

                'price_id' => $data->priceSyncSettingsData->priceId,
                'discount_price_id' => $data->priceSyncSettingsData->discountPriceId,
                'auto_sync' => $data->priceSyncSettingsData->autoSync,
            ]);
    }

    /**
     * @param string $id
     * @param MarketData $data
     * @return void
     */
    private function updateReportSettings(string $id, MarketData $data): void
    {
        DB::table('wildberries_report_settings')
            ->where('integration_id', $id)
            ->update([
                'updated_at' => $this->time,

                'auto_sync' => $data->reportSyncSettingsData->autoSync,
            ]);
    }

}
