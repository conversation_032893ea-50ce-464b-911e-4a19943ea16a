<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Prices\SyncPricesJob;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class LoadPricesAction
{
    /**
     * @throws BindingResolutionException
     * @throws WBSellerException
     */
    public function run(string $id): void
    {
        $integration = DB::table('wildberries_integrations')
            ->where('wildberries_integrations.id', $id)
            ->select(['wildberries_integrations.*'])
            ->first();

        $priceSettings = DB::table('wildberries_price_settings')
            ->where('integration_id', $id)
            ->first();

        if (!$priceSettings || !$priceSettings->price_id || !$priceSettings->min_price) {
            throw new RuntimeException('Нет настроек цен');
        }

        $token = decrypt($integration->token);

        $job = new SyncPricesJob(
            integrationId: $integration->id,
            token: $token,
            retailPriceId: $priceSettings->price_id,
            salePriceId: $priceSettings->min_price,
        );

        $job->handle();
    }
}
