<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Products;

use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class UpdateMatchedProductAction
{
    /**
     * Изменяет товар в системе, к которому сопоставлен товар из Wildberries
     *
     * @param string $cabinetId ID кабинета
     * @param string $matchedProductId ID сопоставленного товара
     * @param string $productId Новый ID товара в системе
     */
    public function run(string $cabinetId, string $matchedProductId, string $productId): void
    {
        // Получаем сопоставленный товар
        $matchedProduct = DB::table('wildberries_matched_products')
            ->where('id', $matchedProductId)
            ->where('cabinet_id', $cabinetId)
            ->first();

        if (!$matchedProduct) {
            throw new RuntimeException("Сопоставленный товар {$matchedProductId} не найден");
        }

        // Проверяем существование нового товара в системе
        $product = DB::table('products')
            ->where('id', $productId)
            ->where('cabinet_id', $cabinetId)
            ->first();

        if (!$product) {
            throw new RuntimeException("Товар {$productId} в системе не найден");
        }

        // Проверяем, что такого сопоставления еще нет
        $existingMatch = DB::table('wildberries_matched_products')
            ->where('cabinet_id', $cabinetId)
            ->where('wildberries_integration_id', $matchedProduct->wildberries_integration_id)
            ->where('wb_id', $matchedProduct->wb_id)
            ->where('size_id', $matchedProduct->size_id)
            ->where('product_id', $productId)
            ->first();

        if ($existingMatch && $existingMatch->id !== $matchedProductId) {
            throw new RuntimeException('Такое сопоставление уже существует');
        }

        // Обновляем запись о сопоставлении
        DB::table('wildberries_matched_products')
            ->where('id', $matchedProductId)
            ->update([
                'product_id' => $productId,
                'updated_at' => now(),
            ]);
    }
}
