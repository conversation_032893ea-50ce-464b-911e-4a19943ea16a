<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Products;

use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\Helpers\MatchProducts;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class ConfirmMatchAction
{
    use HasOrderedUuid;

    /**
     * Подтверждает сопоставление товаров из Wildberries с товарами в системе
     *
     * @param string $cabinetId ID кабинета
     * @param array $matches Массив сопоставлений в формате [['product_to_match_id' => '...', 'product_id' => '...', 'match_type' => '...'], ...]
     */
    public function run(string $cabinetId, array $matches): void
    {
        if (empty($matches)) {
            return;
        }

        $productToMatchIds = array_column($matches, 'product_to_match_id');
        $productIds = array_column($matches, 'product_id');

        $productsToMatch = DB::table('wildberries_products_to_match')
            ->whereIn('id', $productToMatchIds)
            ->where('cabinet_id', $cabinetId)
            ->where('is_matched', false)
            ->get()
            ->keyBy('id');

        if (count($productsToMatch) !== count($productToMatchIds)) {
            throw new RuntimeException("Некоторые товары к сопоставлению не найдены или уже сопоставлены");
        }

        $products = DB::table('products')
            ->whereIn('id', $productIds)
            ->where('cabinet_id', $cabinetId)
            ->get()
            ->keyBy('id');

        if (count($products) !== count($productIds)) {
            throw new RuntimeException("Некоторые товары в системе не найдены");
        }

        $matchedProductsData = [];
        $productsToMatchUpdates = [];
        $now = now();

        foreach ($matches as $match) {
            $productToMatchId = $match['product_to_match_id'];
            $productId = $match['product_id'];
            $matchType = $match['match_type'];
            $productToMatch = $productsToMatch[$productToMatchId];

            $matchedProductId = $this->generateUuid();

            $matchedProductsData[] = [
                'id' => $matchedProductId,
                'cabinet_id' => $cabinetId,
                'wildberries_integration_id' => $productToMatch->wildberries_integration_id,
                'product_id' => $productId,
                'wb_id' => $productToMatch->wb_id,
                'vendor_code' => $productToMatch->vendor_code,
                'title' => $productToMatch->title,
                'size_id' => $productToMatch->size_id,
                'size_name' => $productToMatch->size_name,
                'skus' => $productToMatch->skus,
                'match_type' => $matchType,
                'is_created' => false,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $productsToMatchUpdates[] = [
                'id' => $productToMatchId,
                'matched_product_id' => $matchedProductId,
            ];

            MatchProducts::matchProductInOrders($productId, $productToMatch->wb_id, $productToMatch->cabinet_id, $productToMatch->size_id);
        }

        DB::table('wildberries_matched_products')->insert($matchedProductsData);

        foreach ($productsToMatchUpdates as $update) {
            DB::table('wildberries_products_to_match')
                ->where('id', $update['id'])
                ->update([
                    'is_matched' => true,
                    'matched_product_id' => $update['matched_product_id'],
                    'updated_at' => $now,
                ]);
        }
    }
}
