<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Products;

use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\Helpers\MatchProducts;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use JsonException;
use RuntimeException;
use stdClass;
use Throwable;

readonly class CreateProductFromMatchAction
{
    use HasOrderedUuid;

    /**
     * Создает новый товар в системе из товара к сопоставлению и сопоставляет их
     *
     * @param string $cabinetId ID кабинета
     * @param string $productToMatchId ID товара к сопоставлению
     * @return string ID созданного сопоставления
     * @throws JsonException
     * @throws Throwable
     */
    public function run(string $cabinetId, string $productToMatchId): string
    {
        return DB::transaction(function () use ($cabinetId, $productToMatchId) {
            $productToMatch = $this->getProductToMatch($cabinetId, $productToMatchId);
            $this->validateNoExistingMatch($cabinetId, $productToMatch);
            $defaultDepartment = $this->getDefaultDepartment($cabinetId);

            $productId = $this->generateUuid();
            $matchedProductId = $this->generateUuid();
            $now = now();

            $this->createProduct($productId, $cabinetId, $productToMatch, $defaultDepartment, $now);
            $this->createBarcodes($productId, $cabinetId, $productToMatch);
            $this->createMatchedProduct($matchedProductId, $cabinetId, $productToMatch, $productId, $now);
            $this->updateProductToMatchStatus($productToMatchId, $matchedProductId, $now);
            $this->createProductAccountingFeatures($productId, $now);
            $this->createProductPrices($productId, $productToMatch, $now);

            MatchProducts::matchProductInOrders($productId, $productToMatch->wb_id, $productToMatch->cabinet_id, $productToMatch->size_id);
            return $matchedProductId;
        });
    }

    /**
     * Получает товар к сопоставлению
     */
    private function getProductToMatch(string $cabinetId, string $productToMatchId): stdClass
    {
        $productToMatch = DB::table('wildberries_products_to_match')
            ->where('id', $productToMatchId)
            ->where('cabinet_id', $cabinetId)
            ->where('is_matched', false)
            ->first();

        if (!$productToMatch) {
            throw new RuntimeException('Товар к сопоставлению не найден или уже сопоставлен');
        }

        return $productToMatch;
    }

    /**
     * Проверяет, что такого сопоставления еще нет
     */
    private function validateNoExistingMatch(string $cabinetId, stdClass $productToMatch): void
    {
        $existingMatch = DB::table('wildberries_matched_products')
            ->where('cabinet_id', $cabinetId)
            ->where('wildberries_integration_id', $productToMatch->wildberries_integration_id)
            ->where('wb_id', $productToMatch->wb_id)
            ->where('size_id', $productToMatch->size_id)
            ->first();

        if ($existingMatch) {
            throw new RuntimeException('Такое сопоставление уже существует');
        }
    }

    /**
     * Получает департамент по умолчанию для кабинета
     */
    private function getDefaultDepartment(string $cabinetId): stdClass
    {
        $defaultDepartment = DB::table('departments')
            ->where('cabinet_id', $cabinetId)
            ->orderBy('created_at')
            ->first();

        if (!$defaultDepartment) {
            throw new RuntimeException('Не найден департамент по умолчанию для кабинета');
        }

        return $defaultDepartment;
    }

    /**
     * Создает товар в системе
     */
    private function createProduct(string $productId, string $cabinetId, stdClass $productToMatch, stdClass $defaultDepartment, $now): void
    {
        $article = $productToMatch->vendor_code . (!empty($productToMatch->size_name) ? "-{$productToMatch->size_name}" : '');
        $code = $productToMatch->wb_id . (!empty($productToMatch->size_id) ? "-{$productToMatch->size_id}" : '');

        DB::table('products')->insert([
            'id' => $productId,
            'cabinet_id' => $cabinetId,
            'title' => $productToMatch->title,
            'short_title' => substr($productToMatch->title, 0, 100),
            'article' => $article,
            'code' => $code,
            'department_id' => $defaultDepartment->id,
            'description' => $productToMatch->description ?? '',
            'type' => 1, // Товар
            'indication_subject_calculation' => 'product',
            'tax_system' => 'osno',
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }

    /**
     * Создает штрихкоды для товара
     *
     * @throws JsonException
     */
    private function createBarcodes(string $productId, string $cabinetId, stdClass $productToMatch): void
    {
        $skus = json_decode($productToMatch->skus, false, 512, JSON_THROW_ON_ERROR) ?? [];
        if (empty($skus)) {
            return;
        }

        $barcodes = [];
        foreach ($skus as $sku) {
            $barcodes[] = [
                'id' => $this->generateUuid(),
                'barcodable_id' => $productId,
                'barcodable_type' => 'products',
                'value' => $sku,
                'type' => 1, // 1 - числовое значение для CODE128
                'cabinet_id' => $cabinetId,
            ];
        }

        if (!empty($barcodes)) {
            DB::table('barcodes')->insert($barcodes);
        }
    }

    /**
     * Создает сопоставление товара
     */
    private function createMatchedProduct(string $matchedProductId, string $cabinetId, stdClass $productToMatch, string $productId, $now): void
    {
        DB::table('wildberries_matched_products')->insert([
            'id' => $matchedProductId,
            'cabinet_id' => $cabinetId,
            'wildberries_integration_id' => $productToMatch->wildberries_integration_id,
            'product_id' => $productId,
            'wb_id' => $productToMatch->wb_id,
            'vendor_code' => $productToMatch->vendor_code,
            'title' => $productToMatch->title,
            'size_id' => $productToMatch->size_id,
            'size_name' => $productToMatch->size_name,
            'skus' => $productToMatch->skus,
            'match_type' => 'CREATED',
            'is_created' => true,
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }

    /**
     * Обновляет статус товара к сопоставлению
     */
    private function updateProductToMatchStatus(string $productToMatchId, string $matchedProductId, $now): void
    {
        DB::table('wildberries_products_to_match')
            ->where('id', $productToMatchId)
            ->update([
                'is_matched' => true,
                'matched_product_id' => $matchedProductId,
                'updated_at' => $now,
            ]);
    }

    /**
     * Создает учетные характеристики товара
     */
    private function createProductAccountingFeatures(string $productId, $now): void
    {
        DB::table('product_accounting_features')->insert([
            'id' => $this->generateUuid(),
            'product_id' => $productId,
            'type_accounting' => 'without_special_accounting',
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }

    /**
     * Создает цены товара
     */
    private function createProductPrices(string $productId, stdClass $productToMatch, Carbon $now): void
    {
        $priceSettings = DB::table('wildberries_price_settings')
            ->where('integration_id', $productToMatch->wildberries_integration_id)
            ->first();

        if (!$priceSettings->price_id || !$priceSettings->discount_price_id) {
            throw new RuntimeException('Настройте сопоставление цен');
        }
        $defaultCurrency = DB::table('cabinet_currencies')
            ->where('is_accouting', true)
            ->first();

        // Создаем основную цену
        DB::table('product_prices')->insert([
            'id' => $this->generateUuid(),
            'product_id' => $productId,
            'cabinet_price_id' => $priceSettings->price_id,
            'amount' => $productToMatch->price,
            'currency_id' => $defaultCurrency->id,
            'sort' => 1,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // Создаем цену со скидкой
        DB::table('product_prices')->insert([
            'id' => $this->generateUuid(),
            'product_id' => $productId,
            'cabinet_price_id' => $priceSettings->discount_price_id,
            'amount' => $productToMatch->discount_price,
            'currency_id' => $defaultCurrency->id,
            'sort' => 2,
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }


}
