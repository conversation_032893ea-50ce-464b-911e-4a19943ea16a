<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use App\Modules\Marketplaces\Services\Wildberries\Jobs\Products\LoadProductsJob;
use Illuminate\Support\Facades\DB;
use Throwable;

readonly class LoadProductsAction
{
    /**
     * @throws Throwable
     */
    public function run(string $id): void
    {
        $integration = DB::table('wildberries_integrations')
            ->where('wildberries_integrations.id', $id)
            ->select(['wildberries_integrations.*'])
            ->first();

        $token = decrypt($integration->token);

        $job = new LoadProductsJob(
            cabinetId: $integration->cabinet_id,
            token: $token,
            integrationId: $integration->id,
        );

        $job->handle();
    }
}
