<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseCollectOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use Illuminate\Support\Facades\DB;

/**
 * Action для сборки заказов самовывоза
 */
class CollectSelfDeliveryOrderAction extends BaseCollectOrderAction
{
    /**
     * Получить ожидаемый тип заказа
     *
     * @return string Тип заказа
     */
    protected function getExpectedOrderType(): string
    {
        return OrderDeliveryTypeEnum::SELF_DELIVERY->value;
    }

    protected function getOrderTableName(): string
    {
        return 'wildberries_self_delivery_orders';
    }

    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_self_delivery_order_items';
    }

    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_self_delivery_order_delivery_infos';
    }

    /**
     * Подтверждение заказа самовывоза в Wildberries через API
     *
     * @param object $order Данные заказа
     * @return void
     * @throws WBSellerException
     */
    protected function confirmOrderInWildberries(object $order): void
    {
        $api = new API(['masterkey' => $order->token]);
        $api->Marketplace()->selfDelivery()->confirm($order->wb_number);
    }

    protected function performTypeSpecificConfirmation(object $order): void
    {
        $api = new API(['masterkey' => $order->token]);
        $clientInfo = $api->Marketplace()->selfDelivery()->getClientInfo($order->wb_number);

        DB::table('wildberries_dbs_orders')
            ->where('id', $order->id)
            ->update([
                'delivery_phone' => $clientInfo[0]['phone'],
                'first_name' => $clientInfo[0]['firstName'],
                'updated_at' => now()
            ]);
    }
}
