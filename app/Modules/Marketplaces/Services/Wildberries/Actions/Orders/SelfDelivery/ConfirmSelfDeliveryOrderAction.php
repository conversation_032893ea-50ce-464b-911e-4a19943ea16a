<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery;

use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseConfirmOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;

/**
 * Action для подтверждения заказов самовывоза
 */
class ConfirmSelfDeliveryOrderAction extends BaseConfirmOrderAction
{
    /**
     * Получить ожидаемый тип заказа
     *
     * @return string Тип заказа
     */
    protected function getExpectedOrderType(): string
    {
        return OrderDeliveryTypeEnum::SELF_DELIVERY->value;
    }

    protected function getOrderTableName(): string
    {
        return 'wildberries_self_delivery_orders';
    }
}
