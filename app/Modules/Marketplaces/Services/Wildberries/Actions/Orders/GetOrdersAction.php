<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders;

use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

readonly class GetOrdersAction
{
    /**
     * Получение списка заказов с фильтрацией
     *
     * @param  string  $integrationId  ID кабинета
     * @param  array  $filters  Фильтры
     * @return Collection Коллекция заказов
     */
    public function run(string $integrationId, string $type, array $filters = []): Collection
    {

        $orders = match ($type) {
            OrderDeliveryTypeEnum::FBS->value => $this->getOrdersFromTable('wildberries_fbs_orders', $integrationId, $filters),
            OrderDeliveryTypeEnum::DBS->value => $this->getOrdersFromTable('wildberries_dbs_orders', $integrationId, $filters),
            OrderDeliveryTypeEnum::SELF_DELIVERY->value => $this->getOrdersFromTable('wildberries_self_delivery_orders', $integrationId, $filters),
            default => null,
        };

        if (!$orders) {
            return collect();
        }

        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 15;

        $orders = $orders->sortBy($sortField, SORT_REGULAR, $sortDirection === 'desc');

        return $orders->skip(($page - 1) * $perPage)->take($perPage)->values();
    }

    /**
     * Получение заказов из конкретной таблицы
     */
    private function getOrdersFromTable(string $tableName, string $integrationId, array $filters): Collection
    {
        $query = DB::table($tableName)
            ->where('integration_id', $integrationId)
            ->when(isset($filters['module_status']), fn ($query) => $query->where('module_status', $filters['module_status']))
            ->when(isset($filters['wb_status']), fn ($query) => $query->where('wb_status', $filters['wb_status']))
            ->when(isset($filters['date_from']), fn ($query) => $query->where('created_at', '>=', $filters['date_from']))
            ->when(isset($filters['date_to']), fn ($query) => $query->where('created_at', '<=', $filters['date_to']))
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->where(function ($query) use ($filters) {
                    $query->where('number', 'ilike', '%'.$filters['search'].'%')
                        ->orWhere('wb_number', 'ilike', '%'.$filters['search'].'%');
                });
            });

        return $query->get();
    }
}
