<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseCancelOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;

/**
 * Action для отмены DBS заказов
 */
class CancelDBSOrderAction extends BaseCancelOrderAction
{
    /**
     * Получить ожидаемый тип заказа
     *
     * @return string Тип заказа
     */
    protected function getExpectedOrderType(): string
    {
        return OrderDeliveryTypeEnum::DBS->value;
    }

    protected function getOrderTableName(): string
    {
        return 'wildberries_dbs_orders';
    }

    protected function getOrderIdFieldName(): string
    {
        return 'dbs_order_id';
    }

    /**
     * Отмена DBS заказа в Wildberries через API
     *
     * @param object $order Данные заказа
     * @return void
     * @throws WBSellerException
     */
    protected function cancelOrderInWildberries(object $order): void
    {
        $token = decrypt($order->token);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->DBS()->cancelOrder($order->wb_number);
    }
}
