<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Action переводит сборочное задание в статус complete — в доставке.
 */
class AssembleDBSOrderAction
{
    /**
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function run(string $orderId): void
    {
        $order = DB::table('wildberries_dbs_orders')
            ->where('id', $orderId)
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if ($order->module_status !== 'confirmed' || $order->wb_status !== 'confirm') {
            throw new RuntimeException('Order is not in confirmed state');
        }

        $api = new API(['masterkey' => $order->token]);
        $api->Marketplace()->DBS()->deliver($order->wb_number);

        DB::table('wildberries_dbs_orders')
            ->where('id', $orderId)
            ->update([
                'module_status' => 'delivery',
                'wb_status' => 'deliver',
                'updated_at' => now(),
            ]);
    }
}
