<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseLoadOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\SyncDBSOrdersJob;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Queue;

/**
 * Action для загрузки DBS заказов из Wildberries
 */
class LoadDBSOrdersAction extends BaseLoadOrdersAction
{
    /**
     * Выполнение загрузки DBS заказов
     *
     * @param string $integrationId ID интеграции
     * @param string|null $dateFrom Дата начала
     * @param string|null $dateTo Дата окончания
     * @return void
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception
     * @throws \Throwable
     */
    public function run(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $integration = $this->getIntegrationData($integrationId);

        $parameters = $this->prepareJobParameters($integration, $dateFrom, $dateTo);

        Queue::push(
            new SyncDBSOrdersJob(
                cabinetId: $parameters['cabinetId'],
                token: $parameters['token'],
                integrationId: $parameters['integrationId'],
                sellerId: $parameters['sellerId'],
                legalEntityId: $parameters['legalEntityId'],
                employeeId: $parameters['employeeId'],
                departmentId: $parameters['departmentId'],
                contractorId: $parameters['contractorId'],
                orderNumberingType: $parameters['orderNumberingType'],
                addPrefix: $parameters['addPrefix'],
                prefix: $parameters['prefix'],
                reserve: $parameters['reserve'],
                dateFrom: $parameters['dateFrom'],
                dateTo: $parameters['dateTo']
            )
        );
    }
}
