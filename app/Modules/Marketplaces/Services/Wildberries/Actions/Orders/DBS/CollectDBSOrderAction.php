<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseCollectOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use Illuminate\Support\Facades\DB;

/**
 * Action для сборки DBS заказов
 */
class CollectDBSOrderAction extends BaseCollectOrderAction
{
    /**
     * Получить ожидаемый тип заказа
     *
     * @return string Тип заказа
     */
    protected function getExpectedOrderType(): string
    {
        return OrderDeliveryTypeEnum::DBS->value;
    }

    protected function getOrderTableName(): string
    {
        return 'wildberries_dbs_orders';
    }

    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_dbs_order_items';
    }

    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_dbs_order_delivery_infos';
    }

    /**
     * Подтверждение DBS заказа в Wildberries через API
     *
     * @param object $order Данные заказа
     * @return void
     * @throws WBSellerException
     */
    protected function confirmOrderInWildberries(object $order): void
    {
        $api = new API(['masterkey' => $order->token]);
        $api->Marketplace()->DBS()->confirm($order->wb_number);
    }

    /**
     * @throws WBSellerException
     */
    protected function performTypeSpecificConfirmation(object $order): void
    {
        $api = new API(['masterkey' => $order->token]);
        $clientInfo = $api->Marketplace()->DBS()->getOrdersClient($order->wb_number);

        DB::table('wildberries_dbs_orders')
            ->where('id', $order->id)
            ->update([
                'delivery_phone' => $clientInfo[0]['replacementPhone'],
                'delivery_contact' => $clientInfo[0]['fullName'],
                'updated_at' => now()
            ]);
    }
}
