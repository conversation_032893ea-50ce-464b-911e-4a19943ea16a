<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class GetBoxStickersAction
{
    use HasOrderedUuid;

    /**
     * Добавить заказы в короб
     * Метод добавляет заказы в короб для выбранной поставки.
     *
     * Можно добавить только пока поставка на сборке.
     *
     * @param string $supplyId
     * @param string $boxId
     * @param string $orderId
     * @return array
     *
     * @throws NotFoundException
     * @throws WBSellerException
     */
    public function run(string $supplyId, array $boxIds): array
    {
        $supply = DB::table('wildberries_supplies')
            ->join('wildberries_integrations', 'wildberries_supplies.integration_id', '=', 'wildberries_integrations.id')
            ->where('id', $supplyId)
            ->select([
                'wildberries_supplies.*',
                'wildberries_integrations.token as token',
            ])
            ->first();

        if (! $supply) {
            throw new NotFoundException('Supply not found');
        }

        if ($supply->done || \Illuminate\Support\Carbon::parse($supply->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Supply is done or closed');
        }

        $boxes = DB::table('wildberries_boxes')
            ->where('supply_id', $supplyId)
            ->where('id', $boxIds)
            ->first();

        if ($boxes->isEmpty()) {
            throw new NotFoundException('Box not found');
        }

        $ordersFlag = DB::table('wildberries_box_orders')
            ->where('box_id', $boxIds)
            ->exists();

        if (!$ordersFlag) {
            throw new RuntimeException('Box not contains orders');
        }

        $token = decrypt($supply->token);
        $api = new API(['masterkey' => $token]);
        $stickers = $api->Marketplace()->getSupplyBoxStickers($supply->supply_id, $boxes->pluck('trbxIds')->toArray(), 'svg');

        return [
            'stickers' => $stickers,
            'filetype' => 'svg',
        ];
    }
}
