<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\DBW;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base\BaseCollectOrderAction;
use RuntimeException;

/**
 * Action для сборки FBS заказов
 */
class CollectFBSDBWOrderAction extends BaseCollectOrderAction
{
    protected function getOrderTableName(): string
    {
        return 'wildberries_fbs_orders';
    }

    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_fbs_order_items';
    }

    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_fbs_order_delivery_infos';
    }

    /**
     * Подтверждение FBS заказа в Wildberries через API
     *
     * @param object $order Данные заказа
     * @return void
     * @throws WBSellerException
     * @throws RuntimeException
     */
    protected function confirmOrderInWildberries(object $order): void
    {
        $token = decrypt($order->token);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->WBGO()->confirm($order->wb_number);
    }

    protected function performTypeSpecificValidation(object $order): void
    {
        if ($order->delivery_type !== 'wbgo') {
            throw new RuntimeException('Order is not in wbgo delivery type');
        }
    }
}
