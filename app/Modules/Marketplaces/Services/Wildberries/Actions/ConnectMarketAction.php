<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use Exception;
use RuntimeException;
use App\Clients\WB\API;
use App\Clients\WB\APIToken;
use Illuminate\Support\Facades\DB;

readonly class ConnectMarketAction
{
    private array $needleApi;

    public function __construct()
    {
        $this->needleApi = [
            'marketplace' => 'Маркетплейс',
            'content' => 'Контент',
            'prices' => 'Цены и скидки',
            'statistics' => 'Статистика',
        ];
    }

    public function run(string $id): string
    {
        $integration = DB::table('wildberries_integrations')
            ->where('id', $id)
            ->first();

        if ($integration->connection_status) {
            return 'success';
        }

        $token = decrypt($integration->token);
        try {
            $apiToken = new APIToken($token);
        } catch (Exception $exc) {
            throw new RuntimeException($exc->getMessage()); // Неверный формат токена
        }

        //dd($token->sellerId(), $token->accessTo('marketplace'));
        if ($apiToken->isExpired()) {
            throw new RuntimeException('Токен просрочен');
        }

        if (!app()->environment('local')) {
            if ($apiToken->isReadOnly()) {
                throw new RuntimeException('Токен доступа только для чтения');
            }
            if ($apiToken->isTest()) {
                throw new RuntimeException('Токен для тестовой среды');
            }
            if (!$apiToken->sellerId()) {
                throw new RuntimeException('Нет доступа к API Маркетплейс');
            }
            foreach ($this->needleApi as $apiName => $name) {
                if (!$apiToken->accessTo($apiName)) {
                    throw new RuntimeException('Нет доступа к API ' . $name);
                }
            }
        }

        $api = new API([
            'masterkey' => $token,
        ]);

        try {
            $api->auth();
        } catch (Exception $exception) {
            if ($exception->getCode() == 401) {
                throw new RuntimeException('Не удалось авторизоваться');
            }
        }

        DB::table('wildberries_integrations')
            ->where('id', $id)
            ->update(['connection_status' => true]);

        return 'success';
    }

}
