<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Warehouses\LoadFBSWarehousesJob;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

readonly class LoadFbsAction
{
    /**
     * @throws WBSellerException
     * @throws Exception
     * @throws Throwable
     */
    public function run(string $integrationId): void
    {
        $integration = DB::table('wildberries_integrations')
            ->where('id', $integrationId)
            ->first();

        $token = decrypt($integration->token);
        $job = new LoadFBSWarehousesJob(
            cabinetId: $integration->cabinet_id,
            token: $token,
            integrationId: $integration->id
        );
        $job->handle();
    }
}
