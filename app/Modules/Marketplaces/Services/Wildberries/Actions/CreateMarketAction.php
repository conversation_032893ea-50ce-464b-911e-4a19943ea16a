<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use App\Modules\Marketplaces\Services\Wildberries\Data\MarketData;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

readonly class CreateMarketAction
{
    use HasOrderedUuid;
    private array $syncTables;

    public function __construct()
    {
        $this->syncTables = [
            'wildberries_price_settings',
            'wildberries_order_settings',
            'wildberries_report_settings'
        ];
    }

    public function run(MarketData $data): string
    {
        $integrationId = $this->generateUuid();
        $time = Carbon::now();

        try {
            DB::beginTransaction();

            $salesChannelId = $this->generateUuid();

            $typeId = DB::table('sales_channel_types')
                ->where('name', 'Маркетплейс')
                ->value('id');

            DB::table('sales_channels')
                ->insert([
                    'id' => $salesChannelId,
                    'created_at' => $time,
                    'updated_at' => $time,
                    'cabinet_id' => $data->cabinetId,
                    'name' => $data->shopName,
                    'sales_channel_type_id' => $typeId,
                    'is_common' => true
                ]);

            DB::table('wildberries_integrations')
                ->insert([
                    'id' => $integrationId,
                    'created_at' => $time,
                    'updated_at' => $time,

                    'cabinet_id' => $data->cabinetId,
                    'shop_name' => $data->shopName,

                    'token' => encrypt($data->token),

                    'legal_entity_id' => $data->legalEntityId,
                    'department_id' => $data->departmentId,
                    'contractor_id' => $data->contractorId,
                    'commission_contract_id' => $data->comissionContractId,
                    'sales_channel_id' => $salesChannelId
                ]);

            foreach ($this->syncTables as $syncTable) {
                $toInsert = [
                    'id' => Str::orderedUuid()->toString(),
                    'integration_id' => $integrationId,

                    'created_at' => $time,
                    'updated_at' => $time
                ];

                DB::table($syncTable)
                    ->insert($toInsert);
            }

            DB::commit();
        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw new RuntimeException($e->getMessage());
        }

        return $integrationId;
    }

}
