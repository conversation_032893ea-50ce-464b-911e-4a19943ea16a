<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wildberries_matched_products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            // Связь с кабинетом и интеграцией
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('wildberries_integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();

            // Связь с товаром в системе
            $table->foreignUuid('product_id')->nullable()->references('id')->on('products')->nullOnDelete();

            // Данные товара из Wildberries
            $table->unsignedBigInteger('wb_id')->comment('Артикул WB (nmID)');
            $table->string('vendor_code')->nullable()->comment('Артикул продавца');
            $table->string('title')->comment('Наименование товара');

            // Данные о размере
            $table->string('size_id')->nullable()->comment('ID размера (chrtID)');
            $table->string('size_name')->nullable()->comment('Название размера');

            // Штрихкоды
            $table->json('skus')->nullable()->comment('Штрихкоды товара');

            // Тип сопоставления
            $table->string('match_type')->comment('Тип сопоставления (SKU, наименование, артикул, код, штрихкод, артикул WB - штрихкод)');

            // Флаг создания товара
            $table->boolean('is_created')->default(false)->comment('Флаг создания товара в системе');

            // Индексы
            $table->index(['cabinet_id', 'wildberries_integration_id']);
            $table->index(['wb_id', 'size_id']);
            $table->index('product_id');

            // Уникальный индекс для предотвращения дублирования сопоставлений
            $table->unique(['wildberries_integration_id', 'wb_id', 'size_id'], 'wb_matched_products_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_matched_products');
    }
};
