<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Создаем базовую функцию для общих полей заказов
        $addCommonOrderFields = function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();
            $table->foreignUuid('legal_entity_id')->nullable()->constrained();
            $table->foreignUuid('contractor_id')->nullable()->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->foreignUuid('warehouse_id')->nullable()->constrained();
            $table->foreignUuid('currency_id')->nullable()->references('id')->on('cabinet_currencies');
            $table->foreignUuid('customer_order_id')->nullable()->constrained();

            // Статусы заказа
            $table->string('module_status')->default('new'); // Статус в нашей системе
            $table->string('supplier_status')->nullable(); // Статус поставщика
            $table->string('wb_status')->nullable(); // Статус в Wildberries

            // Признаки состояния заказа
            $table->boolean('has_unmatched_items')->default(false);
            $table->boolean('needs_warehouse_mapping')->default(false);

            // Информация о заказе
            $table->string('number')->nullable(); // Номер заказа в нашей системе
            $table->unsignedBigInteger('wb_number')->nullable(); // Номер заказа в Wildberries
            $table->string('tracking_number')->nullable(); // Номер отслеживания
            $table->text('comment')->nullable(); // Комментарий к заказу
            $table->decimal('total_price', 15, 2)->nullable(); // Общая сумма заказа
            $table->boolean('reserve')->default(false); // Признак резервирования
            $table->date('delivery_date')->nullable(); // Дата доставки
            $table->string('delivery_type')->nullable(); // Тип доставки
            $table->integer('cargo_type')->nullable(); // Тип груза

            // Дополнительная информация от Wildberries
            $table->unsignedBigInteger('wb_warehouse_id')->nullable()->comment('Идентификатор склада WB');
            $table->text('office_list')->nullable()->comment('Список офисов, куда следует привезти товар');

            $table->timestamps();
            $table->softDeletes();

            // Общие индексы
            $table->index(['cabinet_id', 'integration_id']);
            $table->index(['wb_number']);
            $table->index(['module_status']);
            $table->index(['created_at']);
        };

        // Таблица FBS заказов
        Schema::create('wildberries_fbs_orders', function (Blueprint $table) use ($addCommonOrderFields) {
            $addCommonOrderFields($table);
        });

        // Таблица DBS заказов
        Schema::create('wildberries_dbs_orders', function (Blueprint $table) use ($addCommonOrderFields) {
            $addCommonOrderFields($table);

            // Специфичные поля для DBS заказов
            $table->string('delivery_address')->nullable()->comment('Адрес доставки');
            $table->string('delivery_contact')->nullable()->comment('Контактное лицо для доставки');
            $table->string('delivery_phone')->nullable()->comment('Телефон для доставки');
            $table->dateTime('delivery_time_from')->nullable()->comment('Время доставки с');
            $table->dateTime('delivery_time_to')->nullable()->comment('Время доставки до');

            // Дополнительные индексы для DBS
            $table->index(['delivery_date']);
        });

        // Таблица заказов самовывоза
        Schema::create('wildberries_self_delivery_orders', function (Blueprint $table) use ($addCommonOrderFields) {
            $addCommonOrderFields($table);

            $table->string('delivery_phone')->nullable();
            $table->string('first_name')->nullable();
            $table->string('warehouse_address')->nullable();
            $table->string('pay_mode')->default('unknown');
        });

        Schema::table('wildberries_fbs_orders', function (Blueprint $table) {
            $table->boolean('need_reshipment')->default(false);

            $table->index(['need_reshipment']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_self_delivery_orders');
        Schema::dropIfExists('wildberries_dbs_orders');
        Schema::dropIfExists('wildberries_fbs_orders');
    }
};
