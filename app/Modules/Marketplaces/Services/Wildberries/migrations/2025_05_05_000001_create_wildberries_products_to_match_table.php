<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wildberries_products_to_match', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            // Связь с кабинетом и интеграцией
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('wildberries_integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();

            // Данные товара из Wildberries
            $table->unsignedBigInteger('wb_id')->comment('Артикул WB (nmID)');
            $table->string('vendor_code')->nullable()->comment('Артикул продавца');
            $table->string('title')->comment('Наименование товара');
            $table->text('description')->nullable()->comment('Описание товара');

            // Данные о размере
            $table->string('size_id')->nullable()->comment('ID размера (chrtID)');
            $table->string('size_name')->nullable()->comment('Название размера');
            $table->string('tech_size')->nullable()->comment('Технический размер');

            // Штрихкоды
            $table->json('skus')->nullable()->comment('Штрихкоды товара');

            // Цены
            $table->string('price')->nullable()->comment('Цена товара');
            $table->string('discount_price')->nullable()->comment('Цена со скидкой');

            // Статус сопоставления
            $table->boolean('is_matched')->default(false)->comment('Флаг сопоставления');
            $table->uuid('matched_product_id')->nullable()->comment('ID сопоставленного товара');

            // Предложенные варианты сопоставления
            $table->uuid('suggested_matches')->nullable()->comment('ID предлагаемого товара для сопоставления');

            // Индексы
            $table->index(['cabinet_id', 'wildberries_integration_id']);
            $table->index(['wb_id', 'size_id']);
            $table->index('is_matched');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_products_to_match');
    }
};
