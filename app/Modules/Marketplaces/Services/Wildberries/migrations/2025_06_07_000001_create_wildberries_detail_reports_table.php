<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wildberries_detail_reports', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();

            $table->unsignedBigInteger('realizationreport_id')->nullable();
            $table->date('date_from')->nullable();
            $table->date('date_to')->nullable();
            $table->date('create_dt')->nullable();
            $table->string('currency_name')->nullable();
            $table->text('suppliercontract_code')->nullable();
            $table->unsignedBigInteger('rrd_id')->nullable();
            $table->unsignedBigInteger('gi_id')->nullable();
            $table->decimal('dlv_prc')->nullable();
            $table->date('fix_tariff_date_from')->nullable();
            $table->date('fix_tariff_date_to')->nullable();
            $table->string('subject_name')->nullable();
            $table->unsignedBigInteger('nm_id')->nullable();
            $table->string('brand_name')->nullable();
            $table->string('sa_name')->nullable();
            $table->string('ts_name')->nullable();
            $table->string('barcode')->nullable();
            $table->string('doc_type_name')->nullable();
            $table->unsignedBigInteger('quantity')->nullable();
            $table->string('retail_price')->nullable();
            $table->string('retail_amount')->nullable();
            $table->unsignedBigInteger('sale_percent')->nullable();
            $table->string('commission_percent')->nullable();
            $table->string('office_name')->nullable();
            $table->string('supplier_oper_name')->nullable();
            $table->dateTimeTz('order_dt')->nullable();
            $table->dateTimeTz('sale_dt')->nullable();
            $table->dateTimeTz('rr_dt')->nullable();
            $table->unsignedBigInteger('shk_id')->nullable();
            $table->string('retail_price_withdisc_rub')->nullable();
            $table->unsignedBigInteger('delivery_amount')->nullable();
            $table->unsignedBigInteger('return_amount')->nullable();
            $table->string('delivery_rub')->nullable();
            $table->string('gi_box_type_name')->nullable();
            $table->string('product_discount_for_report')->nullable();
            $table->string('supplier_promo')->nullable();
            $table->unsignedBigInteger('rid')->nullable();
            $table->string('ppvz_spp_prc')->nullable();
            $table->string('ppvz_kvw_prc_base')->nullable();
            $table->string('ppvz_kvw_prc')->nullable();
            $table->string('sup_rating_prc_up')->nullable();
            $table->string('is_kgvp_v2')->nullable();
            $table->string('ppvz_sales_commission')->nullable();
            $table->string('ppvz_for_pay')->nullable();
            $table->string('ppvz_reward')->nullable();
            $table->string('acquiring_fee')->nullable();
            $table->string('acquiring_percent')->nullable();
            $table->string('payment_processing')->nullable();
            $table->string('acquiring_bank')->nullable();
            $table->string('ppvz_vw')->nullable();
            $table->string('ppvz_vw_nds')->nullable();
            $table->string('ppvz_office_name')->nullable();
            $table->unsignedBigInteger('ppvz_office_id')->nullable();
            $table->unsignedBigInteger('ppvz_supplier_id')->nullable();
            $table->string('ppvz_supplier_name')->nullable();
            $table->string('ppvz_inn')->nullable();
            $table->string('declaration_number')->nullable();
            $table->string('bonus_type_name')->nullable();
            $table->string('sticker_id')->nullable();
            $table->string('site_country')->nullable();
            $table->boolean('srv_dbs')->nullable();
            $table->string('penalty')->nullable();
            $table->string('additional_payment')->nullable();
            $table->string('rebill_logistic_cost')->nullable();
            $table->string('rebill_logistic_org')->nullable();
            $table->string('storage_fee')->nullable();
            $table->string('deduction')->nullable();
            $table->string('acceptance')->nullable();
            $table->unsignedBigInteger('assembly_id')->nullable();
            $table->string('kiz')->nullable();
            $table->string('srid')->nullable();
            $table->integer('report_type')->nullable();
            $table->boolean('is_legal_entity')->nullable();
            $table->string('trbx_id')->nullable();
            $table->string('installment_cofinancing_amount')->nullable();
            $table->string('wibes_wb_discount_percent')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_detail_reports');
    }
};
