<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderNumberingTypeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Главный Job для синхронизации всех типов заказов из Wildberries
 * Запускает специализированные Jobs для каждого типа заказов
 */
class SyncOrdersJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public function __construct(
        protected readonly string                 $cabinetId,
        protected readonly string                 $token,
        protected readonly string                 $integrationId,
        protected readonly string                 $sellerId,
        protected readonly string                 $legalEntityId,
        protected readonly string                 $employeeId,
        protected readonly string                 $departmentId,
        protected readonly string                 $contractorId,
        protected readonly OrderNumberingTypeEnum $orderNumberingType,
        protected readonly bool                   $addPrefix,
        protected readonly ?string                $prefix,
        protected readonly bool                   $reserve,
        protected readonly ?string                $dateFrom,
        protected readonly ?string                $dateTo,
        protected readonly string                 $retailPriceId,
        protected readonly ?string                $salePriceId = null
    ) {
        // Конструктор только сохраняет параметры
    }

    /**
     * Основной метод обработки задачи - запускает все специализированные Jobs
     */
    public function handle(): void
    {
        try {
            // Запускаем специализированные Jobs для каждого типа заказов
            $this->dispatchFbsOrdersJob();
            $this->dispatchDbsOrdersJob();
            $this->dispatchSelfDeliveryOrdersJob();
        } catch (Throwable $e) {
            Log::error('Error in SyncOrdersJob: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'cabinet_id' => $this->cabinetId
            ]);

            throw $e;
        }
    }

    /**
     * Запуск Job для синхронизации FBS заказов
     * @throws Throwable
     */
    private function dispatchFbsOrdersJob(): void
    {
        $job = new SyncFBSOrdersJob(
            $this->cabinetId,
            $this->token,
            $this->integrationId,
            $this->sellerId,
            $this->legalEntityId,
            $this->employeeId,
            $this->departmentId,
            $this->contractorId,
            $this->orderNumberingType,
            $this->showPriceType,
            $this->addPrefix,
            $this->prefix,
            $this->reserve,
            $this->dateFrom,
            $this->dateTo,
            $this->retailPriceId,
            $this->salePriceId
        );

        $job->handle();
    }



    /**
     * Запуск Job для синхронизации DBS заказов
     * @throws Throwable
     */
    private function dispatchDbsOrdersJob(): void
    {
        $job = new SyncDBSOrdersJob(
            $this->cabinetId,
            $this->token,
            $this->integrationId,
            $this->sellerId,
            $this->legalEntityId,
            $this->employeeId,
            $this->departmentId,
            $this->contractorId,
            $this->orderNumberingType,
            $this->showPriceType,
            $this->addPrefix,
            $this->prefix,
            $this->reserve,
            $this->dateFrom,
            $this->dateTo,
            $this->retailPriceId,
            $this->salePriceId
        );

        $job->handle();
    }

    /**
     * Запуск Job для синхронизации заказов самовывоза
     * @throws WBSellerException|Throwable
     */
    private function dispatchSelfDeliveryOrdersJob(): void
    {
        $job = new SyncSelfDeliveryOrdersJob(
            $this->cabinetId,
            $this->token,
            $this->integrationId,
            $this->sellerId,
            $this->legalEntityId,
            $this->employeeId,
            $this->departmentId,
            $this->contractorId,
            $this->orderNumberingType,
            $this->showPriceType,
            $this->addPrefix,
            $this->prefix,
            $this->reserve,
            $this->dateFrom,
            $this->dateTo,
            $this->retailPriceId,
            $this->salePriceId
        );

        $job->handle();
    }
}
