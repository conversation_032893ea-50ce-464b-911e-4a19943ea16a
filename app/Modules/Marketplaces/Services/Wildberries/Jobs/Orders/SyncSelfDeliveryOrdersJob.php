<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders;

use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\Base\BaseOrdersSyncJob;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для синхронизации заказов самовывоза из Wildberries
 */
class SyncSelfDeliveryOrdersJob extends BaseOrdersSyncJob
{
    /**
     * Основной метод обработки заказов самовывоза
     * @throws Throwable
     */
    public function handle(): void
    {
        try {
            $this->preloadData();
            $this->processSelfDeliveryOrders();
        } catch (Throwable $e) {
            Log::error('Error in SyncSelfDeliveryOrdersJob: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'cabinet_id' => $this->cabinetId
            ]);

            throw $e;
        }
    }

    /**
     * Обработка заказов самовывоза в streaming режиме для избежания проблем с памятью
     */
    private function processSelfDeliveryOrders(): void
    {
        try {
            // Обрабатываем заказы в streaming режиме
            $this->processSelfDeliveryOrdersStreaming();
        } catch (Exception $e) {
            Log::error('Error processing self-delivery orders: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ]);
            throw $e;
        }
    }

    /**
     * Обработка заказов самовывоза в streaming режиме
     */
    private function processSelfDeliveryOrdersStreaming(): void
    {
        // Обрабатываем новые заказы самовывоза
        $this->processNewSelfDeliveryOrders();

        // Обрабатываем заказы из статистики
        $this->processStatisticsOrders();
    }

    /**
     * Обработка новых заказов самовывоза
     */
    private function processNewSelfDeliveryOrders(): void
    {
        $newOrders = $this->fetchOrdersWithRateLimit(
            fn () => $this->api->Marketplace()->selfDelivery()->getNewOrders(),
            'self_delivery'
        );

        if (!empty($newOrders)) {
            $this->processOrdersBatch($newOrders, OrderDeliveryTypeEnum::SELF_DELIVERY->value);
            unset($newOrders);
        }
    }

    /**
     * Обработка заказов из статистики
     */
    private function processStatisticsOrders(): void
    {
        try {
            $dateFrom = $this->dateFrom
                ? new DateTime($this->dateFrom)
                : new DateTime('-' . self::DEFAULT_DAYS_PERIOD . ' days');

            $response = $this->fetchOrdersWithRateLimit(
                fn () => $this->api->Statistics()->ordersFromDate($dateFrom),
                'statistics'
            );

            if (!empty($response)) {
                // Фильтруем только заказы самовывоза более эффективно
                $selfDeliveryOrders = [];
                foreach ($response as $order) {
                    if (isset($order->deliveryType) && $order->deliveryType === 'self_delivery') {
                        $selfDeliveryOrders[] = $order;
                    }
                }

                if (!empty($selfDeliveryOrders)) {
                    $this->processOrdersBatch($selfDeliveryOrders, OrderDeliveryTypeEnum::SELF_DELIVERY->value);
                    unset($selfDeliveryOrders);
                }
            }

            unset($response);
        } catch (Exception $e) {
            Log::warning('Error getting self-delivery orders from statistics: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }
    }

    /**
     * Получить имя таблицы заказов для Self Delivery
     */
    protected function getOrdersTableName(): string
    {
        return 'wildberries_self_delivery_orders';
    }

    /**
     * Получить имя таблицы товаров заказов для Self Delivery
     */
    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_self_delivery_order_items';
    }

    /**
     * Получить имя таблицы информации о доставке для Self Delivery
     */
    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_self_delivery_order_delivery_infos';
    }

    /**
     * Получить тип заказа для Self Delivery
     */
    protected function getOrderType(): string
    {
        return OrderDeliveryTypeEnum::SELF_DELIVERY->value;
    }

    protected function preloadOrders(): void
    {
        $this->preloadOrdersFromTable($this->getOrdersTableName());
    }
}
