<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\Base;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Traits\HasCabinetPrices;
use App\Modules\Marketplaces\Services\Traits\HasStatuses;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderNumberingTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderStatusEnum;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\RateLimiter\Facades\RateLimiter;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Базовый класс для Jobs синхронизации заказов Wildberries
 */
abstract class BaseOrdersSyncJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    use HasOrderedUuid;
    use PrecisionCalculator;
    use HasCabinetPrices;
    use HasStatuses;

    // Константы для API запросов
    protected const API_LIMIT = 1000;
    protected const API_OFFSET = 0;
    protected const DEFAULT_DAYS_PERIOD = 7;

    // Кэши для оптимизации запросов к БД
    protected API $api;
    protected array $ordersCache = [];
    protected array $warehouseMappingsCache = [];
    protected ?object $defaultCurrency = null;
    protected array $statusesCache = [];
    protected array $cabinetPricesCache = [];
    protected array $matchedProductsCache = [];

    /**
     * @throws WBSellerException
     */
    public function __construct(
        protected readonly string                 $cabinetId,
        protected readonly string                 $token,
        protected readonly string                 $integrationId,
        protected readonly string                 $sellerId,
        protected readonly string                 $legalEntityId,
        protected readonly string                 $employeeId,
        protected readonly string                 $departmentId,
        protected readonly string                 $contractorId,
        protected readonly OrderNumberingTypeEnum $orderNumberingType,
        protected readonly bool                   $addPrefix,
        protected readonly ?string                $prefix,
        protected readonly bool                   $reserve,
        protected readonly ?string                $dateFrom,
        protected readonly ?string                $dateTo
    ) {
        $this->api = new API(['masterkey' => $this->token]);
    }

    /**
     * Предзагрузка данных для оптимизации запросов
     */
    protected function preloadData(): void
    {
        $this->preloadOrders();
        $this->preloadWarehouseMappings();
        $this->preloadMatchedProducts();

        $this->defaultCurrency = DB::table('cabinet_currencies')
            ->where('cabinet_id', $this->cabinetId)
            ->where('is_accouting', true)
            ->first();

        $this->preloadStatuses('customer_orders');
        $this->preloadCabinetPrices();
    }

    /**
     * Предзагрузка существующих заказов
     */
    abstract protected function preloadOrders(): void;

    /**
     * Предзагрузка заказов из конкретной таблицы
     */
    protected function preloadOrdersFromTable(string $tableName): void
    {
        // Используем более эффективный способ загрузки
        $orders = DB::table($tableName)
            ->where('integration_id', $this->integrationId)
            ->whereNotNull('wb_number')
            ->select(['id', 'wb_number', 'delivery_type'])
            ->get();

        foreach ($orders as $order) {
            $key = "{$order->wb_number}_{$order->delivery_type}";
            $this->ordersCache[$key] = $order->id; // Храним только ID для экономии памяти
        }
    }

    /**
     * Предзагрузка сопоставлений складов
     */
    protected function preloadWarehouseMappings(): void
    {
        // Используем pluck для лучшей производительности
        $this->warehouseMappingsCache = DB::table('wildberries_warehouses_fbs')
            ->where('wildberries_integration_id', $this->integrationId)
            ->where('cabinet_id', $this->cabinetId)
            ->pluck('warehouse_id', 'seller_warehouse_id')
            ->toArray();
    }

    /**
     * Предзагрузка сопоставленных товаров
     */
    protected function preloadMatchedProducts(): void
    {
        // Загружаем сопоставленные товары из wildberries_matched_products
        try {
            $matchedProducts = DB::table('wildberries_matched_products')
                ->where('cabinet_id', $this->cabinetId)
                ->where('wildberries_integration_id', $this->integrationId)
                ->whereNotNull('product_id')
                ->select(['wb_id', 'size_id', 'product_id'])
                ->get();

            foreach ($matchedProducts as $product) {
                $key = $product->size_id ? "{$product->wb_id}_{$product->size_id}" : $product->wb_id;
                $this->matchedProductsCache[$key] = $product->product_id;
            }
        } catch (Exception $e) {
            Log::warning('Error loading matched products: ' . $e->getMessage());
        }
    }

    /**
     * Создание цены по умолчанию
     */
    protected function createDefaultPrice(string $priceName): void
    {
        $priceId = Str::orderedUuid()->toString();
        $now = Carbon::now();

        $price = [
            'id' => $priceId,
            'cabinet_id' => $this->cabinetId,
            'name' => $priceName,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        DB::table('cabinet_prices')->insert($price);
        $this->cabinetPricesCache[$priceName] = (object)$price;
    }

    /**
     * Выполнение API запроса с учетом ограничений по частоте
     */
    protected function fetchOrdersWithRateLimit(callable $apiCall, string $rateLimiterKey): array
    {
        RateLimiter::throttle($rateLimiterKey);

        try {
            $response = $apiCall();

            // Если ответ содержит пагинацию (next), возвращаем полный ответ
            if (is_object($response) && property_exists($response, 'next')) {
                return [
                    'orders' => $response->orders ?? [],
                    'next' => $response->next ?? null
                ];
            }

            // Для обратной совместимости возвращаем только заказы
            return $response->orders ?? [];
        } catch (Exception $e) {
            if ($e->getCode() == 409) {
                RateLimiter::registerError409($rateLimiterKey);
            }

            Log::warning("API request error: {$e->getMessage()}", [
                'exception' => $e,
                'rate_limiter_key' => $rateLimiterKey
            ]);

            return [];
        }
    }

    /**
     * Обработка пакета заказов
     */
    protected function processOrdersBatch(array $orders, string $deliveryType): void
    {
        // Получаем статусы заказов
        $orderStatuses = $this->fetchOrderStatuses($orders);

        // Увеличиваем размер батча для лучшей производительности
        $batches = array_chunk($orders, 200);

        foreach ($batches as $batchIndex => $batch) {
            $this->processSingleBatch($batch, $deliveryType, $orderStatuses);

            // Очищаем батч из памяти
            unset($batches[$batchIndex]);
        }

        // Очищаем весь массив заказов из памяти
        unset($orders, $orderStatuses);
    }

    /**
     * Получение статусов заказов из API
     */
    protected function fetchOrderStatuses(array $orders): array
    {
        $orderIds = [];
        $orderStatuses = [];

        // Собираем ID заказов
        foreach ($orders as $order) {
            if (isset($order->id)) {
                $orderIds[] = $order->id;
            }
        }

        if (empty($orderIds)) {
            return [];
        }

        // Разбиваем на чанки по 1000 ID для API запросов
        $chunks = array_chunk($orderIds, self::API_LIMIT);

        foreach ($chunks as $chunk) {
            $statuses = $this->fetchOrdersWithRateLimit(
                fn () => $this->api->Marketplace()->getOrdersStatuses($chunk),
                'marketplace'
            );

            if (!empty($statuses)) {
                foreach ($statuses as $status) {
                    if (isset($status->id)) {
                        $orderStatuses[$status->id] = $status;
                    }
                }
            }
        }

        return $orderStatuses;
    }

    /**
     * Обработка одного пакета заказов
     */
    protected function processSingleBatch(array $batch, string $deliveryType, array $orderStatuses): void
    {
        // Сначала создаем товары к сопоставлению для уникальных nmId
        $this->createProductsToMatchForBatch($batch);

        $wildberriesOrders = [];
        $wildberriesOrderItems = [];
        $wildberriesOrderDeliveryInfos = [];

        // Кэшируем Carbon::now() для всего батча
        $now = Carbon::now();

        // Предварительно генерируем UUID для лучшей производительности
        $orderUuids = [];
        $itemUuids = [];
        $deliveryUuids = [];

        $totalOrders = count($batch);

        for ($i = 0; $i < $totalOrders; $i++) {
            $orderUuids[] = $this->generateUuid();
            $itemUuids[] = $this->generateUuid();
            $deliveryUuids[] = $this->generateUuid();
        }

        $orderIndex = 0;

        foreach ($batch as $order) {
            try {
                // Пропускаем существующие заказы
                $existingOrder = $this->findExistingOrder($order, $deliveryType);
                if ($existingOrder) {
                    continue;
                }

                // Получаем ID склада и проверяем необходимость сопоставления
                [$warehouseId, $needWarehouseMapping] = $this->resolveWarehouseMapping($order);

                // Получаем статус заказа
                $orderStatus = isset($order->id, $orderStatuses[$order->id]) ? $orderStatuses[$order->id] : null;

                // Подготавливаем данные заказа
                $orderData = $this->prepareWildberriesOrderData($order, $warehouseId, $deliveryType, $orderStatus, $now, $orderUuids[$orderIndex]);
                if (!$orderData) {
                    continue;
                }

                // Устанавливаем флаг необходимости сопоставления склада
                if ($needWarehouseMapping) {
                    $orderData['needs_warehouse_mapping'] = true;
                }

                // Находим ID продукта и проверяем необходимость сопоставления
                [$productId, $needProductMapping] = $this->resolveProductMapping($order);

                // Устанавливаем статус заказа в модуле
                $orderData['module_status'] = $this->mapWildberriesStatusToModuleStatus(
                    $order->status ?? null,
                    $order->supplierStatus ?? null,
                    $order->wbStatus ?? null
                );

                // Устанавливаем флаг наличия несопоставленных товаров
                $orderData['has_unmatched_items'] = $needProductMapping || ($productId && !$this->isProductMatched($productId));

                // Добавляем заказ в массив для вставки
                $wildberriesOrders[] = $orderData;

                // Создаем данные для товаров заказа
                $orderItemData = $this->createOrderItemData($order, $orderData['id'], $productId, $orderData['total_price'], $now, $itemUuids[$orderIndex]);
                $wildberriesOrderItems[] = $orderItemData;

                // Создаем данные для информации о доставке
                $deliveryData = $this->createDeliveryInfoData($order, $orderData['id'], $now, $deliveryUuids[$orderIndex]);
                if ($deliveryData) {
                    $wildberriesOrderDeliveryInfos[] = $deliveryData;
                }

                $orderIndex++;

            } catch (Exception $e) {
                Log::warning('Error processing order: ' . $e->getMessage(), [
                    'exception' => $e,
                    'order_id' => $order->id ?? 'unknown',
                    'delivery_type' => $deliveryType
                ]);
                $orderIndex++;
            }
        }

        // Сохраняем данные в БД
        $this->saveOrdersData($wildberriesOrders, $wildberriesOrderItems, $wildberriesOrderDeliveryInfos);
    }

    /**
     * Создание товаров к сопоставлению для уникальных nmId в батче
     */
    protected function createProductsToMatchForBatch(array $batch): void
    {
        // Собираем уникальные nmId из батча заказов более эффективно
        $uniqueNmIds = [];
        foreach ($batch as $order) {
            if (isset($order->nmId)) {
                $uniqueNmIds[$order->nmId] = true; // Используем ключи для уникальности
            }
        }

        if (empty($uniqueNmIds)) {
            return;
        }

        $uniqueNmIds = array_keys($uniqueNmIds);

        // Проверяем, какие nmId уже обработаны более эффективно
        $nmIdsToProcess = [];
        foreach ($uniqueNmIds as $nmId) {
            // Проверяем, есть ли уже сопоставленный товар в кэше
            $hasMatchedProduct = isset($this->matchedProductsCache[$nmId]);

            if (!$hasMatchedProduct) {
                // Проверяем с size_id
                foreach ($this->matchedProductsCache as $key => $productId) {
                    if (str_starts_with($key, $nmId . '_')) {
                        $hasMatchedProduct = true;
                        break;
                    }
                }
            }

            if (!$hasMatchedProduct) {
                $nmIdsToProcess[] = $nmId;
            }
        }

        if (empty($nmIdsToProcess)) {
            return;
        }

        // Проверяем, какие товары уже есть в таблице товаров к сопоставлению
        $existingProductsToMatch = DB::table('wildberries_products_to_match')
            ->where('cabinet_id', $this->cabinetId)
            ->where('wildberries_integration_id', $this->integrationId)
            ->whereIn('wb_id', $nmIdsToProcess)
            ->pluck('wb_id', 'wb_id') // Используем ключи для O(1) lookup
            ->toArray();

        // Создаем товары к сопоставлению только для новых nmId
        $productsToInsert = [];
        $now = Carbon::now();

        foreach ($nmIdsToProcess as $nmId) {
            if (!isset($existingProductsToMatch[$nmId])) {
                try {
                    $productData = $this->prepareProductToMatchData($nmId, $now);
                    if ($productData) {
                        $productsToInsert[] = $productData;
                    }
                } catch (Exception $e) {
                    Log::warning('Error preparing product to match for nmId: ' . $e->getMessage(), [
                        'exception' => $e,
                        'nm_id' => $nmId
                    ]);
                }
            }
        }

        // Batch insert для лучшей производительности
        if (!empty($productsToInsert)) {
            try {
                foreach (array_chunk($productsToInsert, 500) as $chunk) {
                    DB::table('wildberries_products_to_match')->insert($chunk);
                }
            } catch (Exception $e) {
                Log::error('Error bulk inserting products to match: ' . $e->getMessage(), [
                    'exception' => $e,
                    'count' => count($productsToInsert)
                ]);
            }
        }
    }

    /**
     * Подготовка данных товара для сопоставления (упрощенная версия без API вызовов)
     */
    protected function prepareProductToMatchData(int $nmId, Carbon $now): ?array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => $this->cabinetId,
            'wildberries_integration_id' => $this->integrationId,
            'wb_id' => $nmId,
            'vendor_code' => null,
            'title' => "Товар из заказа: {$nmId}",
            'description' => 'Товар создан автоматически при обработке заказа',
            'size_id' => null,
            'size_name' => null,
            'tech_size' => null,
            'skus' => null,
            'price' => null,
            'discount_price' => null,
            'is_matched' => false,
            'suggested_matches' => null,
            'created_at' => $now,
            'updated_at' => $now,
        ];
    }

    /**
     * Определение склада и необходимости сопоставления
     */
    protected function resolveWarehouseMapping(object $order): array
    {
        $warehouseId = $this->getWarehouseIdForOrder($order);
        $needWarehouseMapping = false;

        if (!$warehouseId) {
            $warehouseId = null;
            $needWarehouseMapping = true;
        }

        return [$warehouseId, $needWarehouseMapping];
    }

    /**
     * Определение продукта и необходимости сопоставления
     */
    protected function resolveProductMapping(object $order): array
    {
        $productId = $this->findProductBySkus($order);
        $needProductMapping = !$productId;

        return [$productId, $needProductMapping];
    }

    /**
     * Создание данных для товара заказа
     */
    protected function createOrderItemData(object $order, string $orderId, ?string $productId, string $price, ?Carbon $now = null, ?string $itemId = null): array
    {
        $now = $now ?? Carbon::now();
        $itemId = $itemId ?? $this->generateUuid();
        $isProductMatched = $productId && $this->isProductMatched($productId);

        $orderItemData = [
            'id' => $itemId,
            'order_id' => $orderId,
            'product_id' => $productId,
            'quantity' => 1,
            'price' => $price,
            'is_matched' => $isProductMatched,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        if (isset($order->chrtId)) {
            $orderItemData['chrt_id'] = $order->chrtId;
        }

        if (isset($order->nmId)) {
            $orderItemData['nm_id'] = $order->nmId;
        }

        if (isset($order->article)) {
            $orderItemData['article'] = $order->article;
        }

        $orderItemData['sku'] = $order->skus[0];

        return $orderItemData;
    }

    /**
     * Создание данных для информации о доставке
     */
    protected function createDeliveryInfoData(object $order, string $orderId, ?Carbon $now = null, ?string $deliveryId = null): ?array
    {
        $address = $order->address ?? null;
        if (!$address) {
            return null;
        }

        $now = $now ?? Carbon::now();
        $deliveryId = $deliveryId ?? $this->generateUuid();
        $deliveryData = [
            'id' => $deliveryId,
            'order_id' => $orderId,
            'full_address' => $address->fullAddress ?? null,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        if (isset($address->fullAddress)) {
            $this->parseAddressComponents($address->fullAddress, $deliveryData);
        }

        return $deliveryData;
    }

    /**
     * Сохранение данных заказов в БД
     */
    protected function saveOrdersData(array $orders, array $orderItems, array $deliveryInfos): void
    {
        if (empty($orders)) {
            return;
        }

        try {
            DB::transaction(function () use ($orders, $orderItems, $deliveryInfos) {

                $ordersTableName = $this->getOrdersTableName();
                $itemsTableName = $this->getOrderItemsTableName();
                $deliveryInfoTableName = $this->getOrderDeliveryInfoTableName();

                // Use chunked inserts for better performance with large datasets
                if (!empty($orders)) {
                    foreach (array_chunk($orders, 1000) as $chunk) {
                        DB::table($ordersTableName)->insert($chunk);
                    }
                }

                if (!empty($orderItems)) {
                    foreach (array_chunk($orderItems, 2000) as $chunk) {
                        DB::table($itemsTableName)->insert($chunk);
                    }
                }

                if (!empty($deliveryInfos)) {
                    foreach (array_chunk($deliveryInfos, 1000) as $chunk) {
                        DB::table($deliveryInfoTableName)->insert($chunk);
                    }
                }
            });
        } catch (Exception $e) {
            Log::error('Error saving orders data: ' . $e->getMessage(), [
                'exception' => $e,
                'orders_count' => count($orders),
                'order_items_count' => count($orderItems),
                'delivery_infos_count' => count($deliveryInfos)
            ]);
            throw $e;
        }
    }

    /**
     * Поиск существующего заказа в кэше
     */
    protected function findExistingOrder(object $order, string $deliveryType): bool
    {
        if (isset($order->id)) {
            $key = "{$order->id}_{$deliveryType}";
            return isset($this->ordersCache[$key]);
        }

        return false;
    }

    /**
     * Преобразование статуса Wildberries в статус модуля
     */
    protected function mapWildberriesStatusToModuleStatus(?string $status, ?string $supplierStatus, ?string $wbStatus): string
    {
        // Приоритет статусов: supplierStatus > status > wbStatus

        // Проверяем статус поставщика
        if ($supplierStatus) {
            return match ($supplierStatus) {
                'confirm' => OrderStatusEnum::CONFIRMED->value,
                'complete' => OrderStatusEnum::PACKED->value,
                'cancel' => OrderStatusEnum::CANCELED->value,
                'receive' => OrderStatusEnum::DELIVERED->value,
                'reject' => OrderStatusEnum::RETURNED->value,
                default => OrderStatusEnum::NEW->value,
            };
        }

        // Проверяем основной статус
        if ($status) {
            return match ($status) {
                'confirmed' => OrderStatusEnum::CONFIRMED->value,
                'packed' => OrderStatusEnum::PACKED->value,
                'canceled' => OrderStatusEnum::CANCELED->value,
                'delivered' => OrderStatusEnum::DELIVERED->value,
                'returned' => OrderStatusEnum::RETURNED->value,
                default => OrderStatusEnum::NEW->value,
            };
        }

        // Проверяем статус Wildberries
        if ($wbStatus) {
            return match ($wbStatus) {
                'sorted' => OrderStatusEnum::PACKED->value,
                'sold' => OrderStatusEnum::DELIVERED->value,
                'canceled' => OrderStatusEnum::CANCELED->value,
                'returned' => OrderStatusEnum::RETURNED->value,
                default => OrderStatusEnum::NEW->value,
            };
        }

        // По умолчанию - новый заказ
        return OrderStatusEnum::NEW->value;
    }

    /**
     * Получение ID склада для заказа
     */
    protected function getWarehouseIdForOrder(object $order): ?string
    {
        $wbWarehouseId = $order->warehouseId ?? null;

        if (!$wbWarehouseId) {
            return null;
        }

        return $this->warehouseMappingsCache[$wbWarehouseId] ?? null;
    }

    /**
     * Подготовка данных заказа Wildberries
     */
    protected function prepareWildberriesOrderData(object $order, ?string $warehouseId, string $orderType, ?object $orderStatus = null, ?Carbon $now = null, ?string $orderId = null): ?array
    {
        try {
            $now = $now ?? Carbon::now();
            $orderId = $orderId ?? $this->generateUuid();
            $orderNumber = $this->generateOrderNumber($order);

            $wbWarehouseId = $order->warehouseId ?? ($order->storeId ?? null);

            $officeList = $this->formatOfficeList($order);

            $comment = $this->generateOrderComment($order, $orderType, $wbWarehouseId, $officeList);

            return [
                'id' => $orderId,
                'cabinet_id' => $this->cabinetId,
                'integration_id' => $this->integrationId,
                'employee_id' => $this->employeeId,
                'department_id' => $this->departmentId,
                'legal_entity_id' => $this->legalEntityId,
                'contractor_id' => $this->contractorId,
                'warehouse_id' => $warehouseId,
                'currency_id' => $this->defaultCurrency?->id,
                'number' => $orderNumber,
                'wb_number' => $order->id,
                'tracking_number' => $order->trackingNumber ?? null,
                'wb_status' => $this->getOrderStatusValue($order, $orderStatus, 'wbStatus'),
                'supplier_status' => $this->getOrderStatusValue($order, $orderStatus, 'supplierStatus'),
                'module_status' => $this->mapWildberriesStatusToModuleStatus(
                    $this->getOrderStatusValue($order, $orderStatus, 'status'),
                    $this->getOrderStatusValue($order, $orderStatus, 'supplierStatus'),
                    $this->getOrderStatusValue($order, $orderStatus, 'wbStatus')
                ),
                'has_unmatched_items' => false,
                'needs_warehouse_mapping' => false,
                'total_price' => $order->price,
                'reserve' => $this->reserve,
                'cargo_type' => $order->cargoType ?? null,
                'delivery_date' => isset($order->deliveryDate) ? Carbon::parse($order->deliveryDate)->format('Y-m-d') : null,
                'delivery_type' => $order->deliveryType,
                'comment' => $comment,
                'wb_warehouse_id' => $wbWarehouseId,
                'office_list' => $officeList,
                'created_at' => Carbon::parse($order->createdAt ?? 'now')->toDateTimeString(),
                'updated_at' => $now->toDateTimeString(),
            ];
        } catch (Exception $e) {
            Log::warning('Error preparing order data: ' . $e->getMessage(), [
                'exception' => $e,
                'order_id' => $order->id ?? 'unknown'
            ]);
            return null;
        }
    }

    /**
     * Генерация номера заказа
     */
    protected function generateOrderNumber(object $order): string
    {
        $documentNumberGenerator = new DocumentNumberGenerator(
            $this->getOrdersTableName(),
            $this->cabinetId,
            null,
            $this->legalEntityId
        );

        $orderNumber = $documentNumberGenerator->generateNumber();

        // Добавляем префикс, если нужно
        if ($this->addPrefix && $this->prefix) {
            $orderNumber = "{$this->prefix}-{$orderNumber}";
        }

        // Используем номер Wildberries, если указан соответствующий тип нумерации
        if ($this->orderNumberingType === OrderNumberingTypeEnum::WILDBERRIES) {
            $wbId = $order->id ?? ($order->orderID ?? null);
            if ($wbId) {
                $orderNumber = "WB-{$wbId}";
            }
        }

        return $orderNumber;
    }

    /**
     * Форматирование списка офисов
     */
    protected function formatOfficeList(object $order): string
    {
        if (isset($order->offices) && is_array($order->offices)) {
            return implode(', ', $order->offices);
        }

        return '';
    }

    /**
     * Генерация комментария к заказу
     */
    protected function generateOrderComment(
        object $order,
        string $deliveryType,
        ?string $wbWarehouseId,
        string $officeList
    ): string {
        $orderId = $order->id ?? ($order->orderID ?? 'Не указан');
        $comment = "Номер задания в Wildberries: {$orderId}\n";
        $comment .= "Тип доставки: {$deliveryType}\n";

        $warehouseIdText = $wbWarehouseId ?: 'Не указан';
        $comment .= "Идентификатор склада WB: {$warehouseIdText}\n";

        $officeListText = $officeList ?: 'Не указан';
        $comment .= "Список офисов, куда следует привезти товар: {$officeListText}\n";

        if (!empty($order->comment)) {
            $comment .= "\nКомментарий: {$order->comment}";
        }

        return $comment;
    }

    /**
     * Получение значения статуса заказа
     */
    protected function getOrderStatusValue(object $order, ?object $orderStatus, string $statusField): ?string
    {
        if ($orderStatus && isset($orderStatus->$statusField)) {
            return $orderStatus->$statusField;
        }

        return $order->$statusField ?? null;
    }

    /**
     * Поиск продукта по SKU
     */
    protected function findProductBySkus(object $order): ?string
    {
        // Проверяем наличие ID товара Wildberries
        if (!isset($order->nmId)) {
            return null;
        }

        // Сначала проверяем кэш сопоставленных товаров
        $key = isset($order->chrtId) ? "{$order->nmId}_{$order->chrtId}" : $order->nmId;
        if (isset($this->matchedProductsCache[$key])) {
            return $this->matchedProductsCache[$key];
        }

        // Если в кэше нет, ищем в БД
        $matchedProduct = DB::table('wildberries_matched_products')
            ->where('cabinet_id', $this->cabinetId)
            ->where('wildberries_integration_id', $this->integrationId)
            ->where('wb_id', $order->nmId)
            ->where(function ($query) use ($order) {
                $query->where('size_id', $order->chrtId ?? null)
                    ->orWhereNull('size_id');
            })
            ->whereNotNull('product_id')
            ->first();

        if ($matchedProduct) {
            // Сохраняем в кэш для будущих запросов
            $this->matchedProductsCache[$key] = $matchedProduct->product_id;
            return $matchedProduct->product_id;
        }

        // Товар не найден, но товары к сопоставлению уже созданы в createProductsToMatchForBatch
        return null;
    }

    /**
     * Проверка сопоставления продукта
     */
    protected function isProductMatched(string $productId): bool
    {
        try {
            return DB::table('wildberries_matched_products')
                ->where('product_id', $productId)
                ->where('cabinet_id', $this->cabinetId)
                ->exists();
        } catch (Exception $e) {
            Log::warning('Error checking product matching: ' . $e->getMessage(), [
                'product_id' => $productId
            ]);
            return false;
        }
    }

    /**
     * Создание записи для сопоставления продукта по nmId
     */
    protected function createProductToMatchByNmId(int $nmId): void
    {
        try {
            // Получаем карточку товара
            RateLimiter::throttle('content');
            $productInfo = $this->api->Content()->getCardByNmID((string)$nmId);

            if (!$productInfo || empty($productInfo->cards)) {
                Log::warning('Product not found in Wildberries API', ['nm_id' => $nmId]);
                return;
            }

            $wbProduct = $productInfo->cards[0];

            $pricesData = $this->api->Prices()->getPrices(1, 1, $nmId);
            $listGoods = $pricesData->data->listGoods ?? [];

            $mainGood = $listGoods[0];
            $priceMap = [];
            $discountMap = [];

            if (!($mainGood->editableSizePrice ?? false)) {
                // Одинаковая цена на все размеры
                $price = $mainGood->sizes[0]->price ?? null;
                $discount = $mainGood->sizes[0]->discountedPrice ?? null;

                foreach ($wbProduct->sizes as $size) {
                    $priceMap[$size->chrtID] = $price;
                    $discountMap[$size->chrtID] = $discount;
                }
            } else {
                // Индивидуальные цены по размерам
                $sizePrices = $this->api->Prices()->getNmIdSizesPrices($nmId);
                foreach ($sizePrices->data->listGoods ?? [] as $good) {
                    foreach ($good->sizes ?? [] as $size) {
                        $sizeId = $size->sizeID ?? null;
                        if (!$sizeId) {
                            continue;
                        }

                        $priceMap[$sizeId] = $size->price ?? null;
                        $discountMap[$sizeId] = $size->discountedPrice ?? null;
                    }
                }
            }

            $existingSizeIds = DB::table('wildberries_products_to_match')
                ->where('cabinet_id', $this->cabinetId)
                ->where('wildberries_integration_id', $this->integrationId)
                ->where('wb_id', $nmId)
                ->pluck('size_id')
                ->toArray();

            $now = Carbon::now();
            $toInsert = [];

            foreach ($wbProduct->sizes as $size) {
                $sizeId = $size->chrtID;

                if (in_array($sizeId, $existingSizeIds, true)) {
                    continue;
                }

                $toInsert[] = [
                    'id' => Str::orderedUuid()->toString(),
                    'cabinet_id' => $this->cabinetId,
                    'wildberries_integration_id' => $this->integrationId,
                    'wb_id' => $nmId,
                    'vendor_code' => $wbProduct->vendorCode,
                    'title' => $wbProduct->title,
                    'description' => $wbProduct->description,
                    'size_id' => $sizeId,
                    'size_name' => $size->wbSize,
                    'tech_size' => $size->techSize,
                    'skus' => json_encode($size->skus ?? [], JSON_THROW_ON_ERROR),
                    'price' => $priceMap[$sizeId] ?? null,
                    'discount_price' => $discountMap[$sizeId] ?? null,
                    'is_matched' => false,
                    'suggested_matches' => null,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if (!empty($toInsert)) {
                DB::table('wildberries_products_to_match')->insert($toInsert);
            }

        } catch (Exception $e) {
            if ($e->getCode() === 409) {
                RateLimiter::registerError409('content');
            }

            Log::warning('Error creating product to match from API', [
                'exception' => $e,
                'nm_id' => $nmId
            ]);
        }
    }

    /**
     * Разбор компонентов адреса
     */
    protected function parseAddressComponents(string $fullAddress, array &$deliveryData): void
    {
        // Извлекаем страну/город
        if (preg_match('/(?:\b|г\.\s*)([\p{L}\s-]+)(?:,|$)/u', $fullAddress, $matches)) {
            $deliveryData['country'] = trim($matches[1]);
            $deliveryData['city'] = trim($matches[1]);
        }

        // Извлекаем улицу
        if (preg_match('/(?:\b|ул\.\s*)([\p{L}\s-]+)(?:,|$)/u', $fullAddress, $matches)) {
            $deliveryData['street'] = trim($matches[1]);
        }

        // Извлекаем дом
        if (preg_match('/(?:\b|д\.\s*)(\d+[\p{L}\d\/-]*)/u', $fullAddress, $matches)) {
            $deliveryData['house'] = trim($matches[1]);
        }

        // Извлекаем квартиру
        if (preg_match('/(?:\b|кв\.\s*)(\d+[\p{L}\d\/-]*)/u', $fullAddress, $matches)) {
            $deliveryData['apartment'] = trim($matches[1]);
        }

        // Извлекаем почтовый индекс
        if (preg_match('/\b(\d{6})\b/', $fullAddress, $matches)) {
            $deliveryData['post_code'] = $matches[1];
        }
    }

    /**
     * Получить имя таблицы заказов для конкретного типа
     *
     * @return string
     */
    abstract protected function getOrdersTableName(): string;

    /**
     * Получить имя таблицы товаров заказов для конкретного типа
     *
     * @return string
     */
    abstract protected function getOrderItemsTableName(): string;

    /**
     * Получить имя таблицы информации о доставке для конкретного типа
     *
     * @return string
     */
    abstract protected function getOrderDeliveryInfoTableName(): string;

    /**
     * Получить тип заказа для конкретной реализации
     *
     * @return string
     */
    abstract protected function getOrderType(): string;
}
