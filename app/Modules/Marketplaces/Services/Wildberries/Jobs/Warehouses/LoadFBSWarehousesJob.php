<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Warehouses;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Enums\FBSWarehouseStatusEnum;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для загрузки FBS складов из Wildberries
 */
class LoadFBSWarehousesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use HasOrderedUuid;

    private string $defaultWarehouseId;

    /**
     * Создает новый экземпляр задачи.
     *
     * @param string $cabinetId ID кабинета
     * @param string $token Токен API Wildberries
     * @param string $integrationId ID интеграции с Wildberries
     */
    public function __construct(
        private readonly string $cabinetId,
        private readonly string $token,
        private readonly string $integrationId,
    ) {
    }

    /**
     * @throws WBSellerException|Throwable
     */
    public function handle(): void
    {
        try {
            $api = new API(['masterkey' => $this->token]);
            $warehouses = $this->fetchWarehouses($api);

            $this->processWarehouses($warehouses);

            Log::info('Wildberries FBS warehouses successfully loaded', [
                'cabinet_id' => $this->cabinetId,
                'count' => $warehouses->count()
            ]);

        } catch (Exception $e) {
            Log::error('Error loading Wildberries FBS warehouses', [
                'cabinet_id' => $this->cabinetId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Получает список складов из API Wildberries.
     *
     * @param API $api Клиент API Wildberries
     * @return Collection Коллекция складов
     * @throws WBSellerException
     */
    private function fetchWarehouses(API $api): Collection
    {
        return collect($api->Marketplace()->Warehouses()->list());
    }

    /**
     * Обрабатывает данные о складах и сохраняет их в БД.
     *
     * @param Collection $warehouses Коллекция складов
     * @return void
     * @throws Exception|Throwable
     */
    private function processWarehouses(Collection $warehouses): void
    {
        DB::beginTransaction();

        try {
            $currentWarehouses = DB::table('wildberries_warehouses_fbs')
                ->where('cabinet_id', $this->cabinetId)
                ->select('seller_warehouse_id')
                ->pluck('seller_warehouse_id');

            $this->defaultWarehouseId = DB::table('warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->where('is_default', true)
                ->value('id');

            $this->processNewWarehouses(
                $warehouses,
                $currentWarehouses->toArray()
            );

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Обрабатывает новые склады и добавляет их в БД.
     *
     * @param Collection $warehouses Коллекция всех складов
     * @param array $currentWarehouseIds ID существующих складов
     * @return void
     */
    private function processNewWarehouses(Collection $warehouses, array $currentWarehouseIds): void
    {
        $warehouseIds = $warehouses->pluck('id')->toArray();
        $newWarehouseIds = array_diff($warehouseIds, $currentWarehouseIds);

        if (empty($newWarehouseIds)) {
            return;
        }

        $newWarehouses = $warehouses->whereIn('id', $newWarehouseIds);

        $toInsertArray = [];
        foreach ($newWarehouses as $warehouse) {
            $toInsertArray[] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $this->cabinetId,
                'wildberries_integration_id' => $this->integrationId,
                'office_id' => $warehouse->officeId,
                'seller_warehouse_id' => $warehouse->id,
                'name' => $warehouse->name,
                'status' => FBSWarehouseStatusEnum::WAITING->value,
                'cargo_type' => $warehouse->cargoType,
                'delivery_type' => $warehouse->deliveryType,
                'warehouse_id' => $this->defaultWarehouseId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($toInsertArray)) {
            DB::table('wildberries_warehouses_fbs')->insert($toInsertArray);
        }
    }
}
