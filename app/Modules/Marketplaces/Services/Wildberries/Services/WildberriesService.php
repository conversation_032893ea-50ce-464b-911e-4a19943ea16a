<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services;

use App\Clients\WB\Exception\WBSellerException;
use App\Enums\Api\Internal\TypeProductEnum;
use App\Modules\Marketplaces\DTO\CostAccounting\WildberriesDto;
use App\Modules\Marketplaces\Services\Wildberries\Actions\ConnectMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\CreateMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\DeleteMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\DisconnectMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadPricesAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadProductsAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadReportsAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadResiduesAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\CancelMatchAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\ConfirmMatchAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\CreateProductFromMatchAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\FindSuggestedMatchesAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\GetMatchedProductsAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\GetProductsToMatchAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\ManualMatchAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Products\UpdateMatchedProductAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\UpdateMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\ViewMarketAction;
use App\Modules\Marketplaces\Services\Wildberries\Data\MarketData;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use JsonException;
use RuntimeException;
use Throwable;

class WildberriesService
{
    public function createMarket(MarketData $data): string
    {
        return (new CreateMarketAction())->run($data);
    }

    public function updateMarket(MarketData $data, string $id): void
    {
        (new UpdateMarketAction())->run($data, $id);
    }

    /**
     * @throws JsonException
     */
    public function getMarket(string $id): ?object
    {
        return (new ViewMarketAction())->run($id);
    }

    public function deleteMarket(string $id): void
    {
        (new DeleteMarketAction())->run($id);
    }

    public function connect(string $id): string
    {
        return (new ConnectMarketAction())->run($id);
    }

    public function disconnect(string $id): string
    {
        return (new DisconnectMarketAction())->run($id);
    }

    /**
     * @throws Throwable
     */
    public function loadProducts(string $id): void
    {
        (new LoadProductsAction())->run($id);
    }

    /**
     * @throws BindingResolutionException
     * @throws WBSellerException
     */
    public function loadPrices(string $id): void
    {
        (new LoadPricesAction())->run($id);
    }

    /**
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function loadResidues(string $id): void
    {
        (new LoadResiduesAction())->run($id);
    }

    /**
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function loadOrders(string $id, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadOrdersAction())->run($id, $dateFrom, $dateTo);
    }
    /**
     * Загрузка отчетов о продажах по реализации
     *
     * @param string $id ID интеграции
     * @param string $dateFrom Дата начала периода (RFC3339)
     * @param string $dateTo Дата окончания периода (RFC3339)
     * @throws Throwable
     */
    public function loadReports(string $id, string $dateFrom, string $dateTo): void
    {
        (new LoadReportsAction())->run($id, $dateFrom, $dateTo);
    }

    /**
     * Получает список сопоставленных товаров для указанного кабинета и интеграции
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     * @return Collection Коллекция сопоставленных товаров
     */
    public function getMatchedProducts(string $cabinetId, string $integrationId): Collection
    {
        return (new GetMatchedProductsAction())->run($cabinetId, $integrationId);
    }

    /**
     * Получает список товаров к сопоставлению для указанного кабинета и интеграции
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     * @return Collection Коллекция товаров к сопоставлению
     */
    public function getProductsToMatch(string $cabinetId, string $integrationId): Collection
    {
        return (new GetProductsToMatchAction())->run($cabinetId, $integrationId);
    }

    /**
     * Находит предполагаемые соответствия для всех товаров интеграции и возвращает список товаров к сопоставлению
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $integrationId  ID интеграции
     * @param  string  $matchType  Тип сопоставления (из енума ProductMatchingTypeEnum)
     * @return Collection Список товаров к сопоставлению
     *
     * @throws Exception
     */
    public function findSuggestedMatches(string $cabinetId, string $integrationId, string $matchType): Collection
    {
        return (new FindSuggestedMatchesAction())->run($cabinetId, $integrationId, $matchType);
    }

    /**
     * Подтверждает сопоставление товаров из Wildberries с товарами в системе
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  array  $matches  Массив сопоставлений в формате [['product_to_match_id' => '...', 'product_id' => '...', 'match_type' => '...'], ...]
     */
    public function confirmMatch(string $cabinetId, array $matches): void
    {
        (new ConfirmMatchAction())->run($cabinetId, $matches);
    }

    /**
     * Создает новый товар в системе из товара к сопоставлению и сопоставляет их
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $productToMatchId  ID товара к сопоставлению
     * @return string ID созданного сопоставления
     */
    public function createProductFromMatch(string $cabinetId, string $productToMatchId): string
    {
        return (new CreateProductFromMatchAction())->run($cabinetId, $productToMatchId);
    }

    /**
     * Ручное сопоставление товара из Wildberries с товаром в системе
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $productToMatchId  ID товара к сопоставлению
     * @param  string  $productId  ID товара в системе
     * @return string ID созданного сопоставления
     */
    public function manualMatch(string $cabinetId, string $productToMatchId, string $productId): string
    {
        return (new ManualMatchAction())->run($cabinetId, $productToMatchId, $productId);
    }

    /**
     * Отменяет сопоставление товаров из Wildberries и возвращает их в список товаров к сопоставлению
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  array  $matchedProductIds  Массив ID сопоставленных товаров
     */
    public function cancelMatch(string $cabinetId, array $matchedProductIds): void
    {
        (new CancelMatchAction())->run($cabinetId, $matchedProductIds);
    }

    /**
     * Изменяет товар в системе, к которому сопоставлен товар из Wildberries
     *
     * @param  string  $cabinetId  ID кабинета
     * @param  string  $matchedProductId  ID сопоставленного товара
     * @param  string  $productId  Новый ID товара в системе
     */
    public function updateMatchedProduct(string $cabinetId, string $matchedProductId, string $productId): void
    {
        (new UpdateMatchedProductAction())->run($cabinetId, $matchedProductId, $productId);
    }

    public function updateCostAccountingSettings(WildberriesDto $dto, string $id): void
    {
        $serviceIds = array_filter([
            $dto->finesServiceId,
            $dto->otherServiceId,
            $dto->paidAcceptanceServiceId,
            $dto->logisticServiceId,
            $dto->storageServiceId,
            $dto->extraPaymentServiceId
        ]);

        $services = DB::table('products')
            ->whereIn('id', $serviceIds)
            ->get();

        if ($services->some(fn ($s) => $s->type !== TypeProductEnum::SERVICE->value)) {
            throw new RuntimeException('One or more products are not of type SERVICE.');
        }

        $now = now();

        $data = [
            'id' => Str::orderedUuid()->toString(),
            'integration_id' => $id,
            'created_at' => $now,
            'updated_at' => $now,
            'logistics' => $dto->logistics,
            'logistic_service_id' => $dto->logisticServiceId,
            'extra_payments' => $dto->extraPayments,
            'extra_payment_service_id' => $dto->extraPaymentServiceId,
            'storage' => $dto->storage,
            'storage_service_id' => $dto->storageServiceId,
            'paid_acceptance' => $dto->paidAcceptance,
            'paid_acceptance_service_id' => $dto->paidAcceptanceServiceId,
            'other' => $dto->other,
            'other_service_id' => $dto->otherServiceId,
            'fines' => $dto->fines,
            'fines_service_id' => $dto->finesServiceId
        ];

        DB::table('wildberries_additional_cost_settings')->upsert(
            $data,
            ['integration_id'],
            array_diff(array_keys($data), ['id', 'created_at'])
        );
    }
}
