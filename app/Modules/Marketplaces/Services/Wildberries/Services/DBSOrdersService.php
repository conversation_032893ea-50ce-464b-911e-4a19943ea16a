<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services;

use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS\AssembleDBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS\CancelDBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS\CollectDBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS\ConfirmDBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\DBS\LoadDBSOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetOrdersAction;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Throwable;

/**
 * Сервис для работы с заказами Wildberries
 */
class DBSOrdersService
{
    /**
     * Получение списка заказов с фильтрацией
     */
    public function getOrders(string $integrationId, array $filters = []): Collection
    {
        return (new GetOrdersAction())->run($integrationId, 'dbs', $filters);
    }

    /**
     * Загрузка DBS заказов
     *
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception|Throwable
     */
    public function loadDBSOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadDBSOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }

    /**
     * Подтверждение DBS заказа
     * @throws NotFoundException
     * @throws NotFoundException
     */
    public function confirmDBSOrder(string $orderId): void
    {
        (new ConfirmDBSOrderAction())->run($orderId);
    }

    /**
     * Отмена DBS заказа
     */
    public function cancelDBSOrder(string $orderId): void
    {
        (new CancelDBSOrderAction())->run($orderId);
    }

    /**
     * Сборка DBS заказа
     */
    public function collectDBSOrder(string $orderId): void
    {
        (new CollectDBSOrderAction())->run($orderId);
    }

    /**
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function assembleDBSOrder(string $orderId): void
    {
        (new AssembleDBSOrderAction())->run($orderId);
    }
}
