<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetOrderDetailsAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetUnmatchedItemsAction;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use JsonException;

/**
 * Сервис для работы с заказами Wildberries
 */
class OrdersService
{
    /**
     * Получение списка заказов с фильтрацией
     */
    public function getOrders(string $integrationId,string $type, array $filters = []): Collection
    {
        return (new GetOrdersAction())->run($integrationId, $type, $filters);
    }

    /**
     * Получение детальной информации о заказе
     * @throws JsonException
     */
    public function getOrderDetails(string $orderId): ?object
    {
        return (new GetOrderDetailsAction())->run($orderId);
    }

    /**
     * Получение списка несопоставленных товаров в заказе
     */
    public function getUnmatchedItems(string $orderId): Collection
    {
        return (new GetUnmatchedItemsAction())->run($orderId);
    }

    /**
     * Загрузка всех типов заказов
     *
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function loadAllOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }
}
