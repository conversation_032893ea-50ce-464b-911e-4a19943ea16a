<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services;

use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery\CancelSelfDeliveryOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery\CollectSelfDeliveryOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery\ConfirmSelfDeliveryOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery\LoadSelfDeliveryOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\SelfDelivery\PrepareSelfDeliveryOrderAction;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

/**
 * Сервис для работы с заказами Wildberries
 */
class SelfDeliveryOrdersService
{
    /**
     * Получение списка заказов с фильтрацией
     */
    public function getOrders(string $integrationId, array $filters = []): Collection
    {
        return (new GetOrdersAction())->run($integrationId, 'self_delivery', $filters);
    }

    /**
     * Загрузка заказов самовывоза
     *
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function loadSelfDeliveryOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadSelfDeliveryOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }

    /**
     * Подтверждение заказа самовывоза
     * @throws NotFoundException
     */
    public function confirmSelfDeliveryOrder(string $orderId): void
    {
        (new ConfirmSelfDeliveryOrderAction())->run($orderId);
    }

    /**
     * Отмена заказа самовывоза
     */
    public function cancelSelfDeliveryOrder(string $orderId): void
    {
        (new CancelSelfDeliveryOrderAction())->run($orderId);
    }

    /**
     * Сборка заказа самовывоза
     */
    public function collectSelfDeliveryOrder(string $orderId): void
    {
        (new CollectSelfDeliveryOrderAction())->run($orderId);
    }

    public function prepareSelfDeliveryOrder(string $orderId): void
    {
        (new PrepareSelfDeliveryOrderAction())->run($orderId);
    }
}
