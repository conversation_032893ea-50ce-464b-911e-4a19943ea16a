<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services\FBS;

use App\Clients\WB\API;
use App\Clients\WB\Exception\ApiClientException;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\AddOrderToSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\CancelFBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\ConfirmFBSOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\DBW\AssembleFBSDBWOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\DBW\CollectFBSDBWOrderAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\LoadFBSOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies\CreateSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\GetOrdersAction;
use App\Modules\Marketplaces\Services\Wildberries\DTO\OrderDTO;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

/**
 * Сервис для работы с заказами Wildberries
 */
class FBSOrdersService
{
    /**
     * Получение списка заказов с фильтрацией
     */
    public function getOrders(string $integrationId, array $filters = []): Collection
    {
        return (new GetOrdersAction())->run($integrationId, 'fbs', $filters);
    }

    /**
     * Добавление заказа в новую поставку
     *
     * @throws WBSellerException
     * @throws ApiClientException
     * @throws ApiTimeRestrictionsException
     * @throws NotFoundException
     */
    public function addOrderToNewSupply(string $name, string $orderId): string
    {
        $order = $this->getOrderForSupply($orderId);

        $token = decrypt($order->token);
        $supplyId = (new CreateSupplyAction())->run($name, $order->integration_id, $order->cabinet_id, $token);

        app(AddOrderToSupplyAction::class)->run(
            OrderDTO::fromArray(collect($order)->toArray()),
            $supplyId,
            $token
        );

        return $supplyId;
    }

    /**
     * Добавление заказа в существующую поставку
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function addOrderToSupply(string $orderId, string $supplyId): void
    {
        $order = $this->getOrderForSupply($orderId);

        $token = decrypt($order->token);
        app(AddOrderToSupplyAction::class)->run(
            OrderDTO::fromArray(collect($order)->toArray()),
            $supplyId,
            $token
        );
    }

    /**
     * Получение стикеров для заказов
     *
     * @throws NotFoundException
     * @throws WBSellerException
     */
    public function getOrderStickers(array $orderIds): array
    {
        // Ищем заказы во всех таблицах типов заказов
        $integration = null;
        $orders = collect();

        // Проверяем FBS заказы
        $fbsOrders = DB::table('wildberries_fbs_orders')
            ->join('wildberries_integrations', 'wildberries_fbs_orders.integration_id', '=', 'wildberries_integrations.id')
            ->whereIn('wildberries_fbs_orders.id', $orderIds)
            ->select(['wildberries_fbs_orders.wb_number', 'wildberries_integrations.token as token'])
            ->get();

        if ($fbsOrders->isNotEmpty()) {
            $integration = $fbsOrders->first();
            $orders = $orders->merge($fbsOrders);
        }

        // Проверяем DBS заказы
        if ($orders->isEmpty()) {
            $dbsOrders = DB::table('wildberries_dbs_orders')
                ->join('wildberries_integrations', 'wildberries_dbs_orders.integration_id', '=', 'wildberries_integrations.id')
                ->whereIn('wildberries_dbs_orders.id', $orderIds)
                ->select(['wildberries_dbs_orders.wb_number', 'wildberries_integrations.token as token'])
                ->get();

            if ($dbsOrders->isNotEmpty()) {
                $integration = $dbsOrders->first();
                $orders = $orders->merge($dbsOrders);
            }
        }

        // Проверяем Self Delivery заказы
        if ($orders->isEmpty()) {
            $selfDeliveryOrders = DB::table('wildberries_self_delivery_orders')
                ->join('wildberries_integrations', 'wildberries_self_delivery_orders.integration_id', '=', 'wildberries_integrations.id')
                ->whereIn('wildberries_self_delivery_orders.id', $orderIds)
                ->select(['wildberries_self_delivery_orders.wb_number', 'wildberries_integrations.token as token'])
                ->get();

            if ($selfDeliveryOrders->isNotEmpty()) {
                $integration = $selfDeliveryOrders->first();
                $orders = $orders->merge($selfDeliveryOrders);
            }
        }

        if ($orders->isEmpty() || !$integration) {
            throw new NotFoundException('Orders not found');
        }

        $ids = $orders->pluck('wb_number')->toArray();

        $token = decrypt($integration->token);
        $api = new API(['masterkey' => $token]);
        $stickers = $api->Marketplace()->getOrdersStickers($ids, 'png', '58x40');

        return [
            'stickers' => $stickers,
            'filetype' => 'png',
        ];
    }

    /**
     * Загрузка FBS заказов
     *
     * @throws WBSellerException
     * @throws BindingResolutionException
     * @throws Exception
     * @throws Throwable
     */
    public function loadOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadFBSOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }

    /**
     * Подтверждение FBS заказа
     * @throws NotFoundException
     */
    public function confirmFBSOrder(string $orderId): void
    {
        (new ConfirmFBSOrderAction())->run($orderId);
    }

    /**
     * Отмена FBS заказа
     */
    public function cancelOrder(string $orderId): void
    {
        (new CancelFBSOrderAction())->run($orderId);
    }

    /**
     * Сборка FBS заказа
     */
    public function collectDBWOrder(string $orderId): void
    {
        (new CollectFBSDBWOrderAction())->run($orderId);
    }



    /**
     * Получение заказа для добавления в поставку с валидацией
     */
    private function getOrderForSupply(string $orderId): object
    {
        $order = DB::table('wildberries_fbs_orders')
            ->join('wildberries_integrations', 'wildberries_fbs_orders.integration_id', '=', 'wildberries_integrations.id')
            ->where('wildberries_fbs_orders.id', $orderId)
            ->whereIn('wildberries_fbs_orders.module_status', ['confirmed','new'])
            ->whereIn('wildberries_fbs_orders.supplier_status', ['new', 'confirm'])
            ->select(['wildberries_integrations.cabinet_id', 'wildberries_integrations.token', 'wildberries_fbs_orders.*'])
            ->first();

        if (!$order) {
            throw new RuntimeException('FBS Order not found or not in correct status');
        }

        if ($order->has_unmatched_items) {
            throw new RuntimeException('Order has unmatched items');
        }

        if ($order->needs_warehouse_mapping) {
            throw new RuntimeException('Order needs warehouse mapping');
        }

        return $order;
    }

    public function assembleDBWOrder(string $orderId): void
    {
        (new AssembleFBSDBWOrderAction())->run($orderId);
    }
}
