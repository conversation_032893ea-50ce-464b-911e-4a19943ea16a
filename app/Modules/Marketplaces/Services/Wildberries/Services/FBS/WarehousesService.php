<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services\FBS;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\LoadFbsAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\UpdateFbsWarehouseAction;
use App\Modules\Marketplaces\Services\Wildberries\DTO\FBSUpdateDTO;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * Сервис для работы со складами Wildberries
 */
class WarehousesService
{
    /**
     * Получение списка FBS складов
     */
    public function getFbsWarehouses(string $integrationId): Collection
    {
        return DB::table('wildberries_warehouses_fbs')
            ->where('wildberries_integration_id', $integrationId)
            ->get();
    }

    /**
     * Загрузка FBS складов из API Wildberries
     *
     * @throws WBSellerException
     * @throws Exception
     * @throws Throwable
     */
    public function loadFbsWarehouses(string $integrationId): void
    {
        (new LoadFbsAction())->run($integrationId);
    }

    /**
     * Обновление настроек FBS склада
     */
    public function updateFbsWarehouse(FBSUpdateDTO $dto): void
    {
        (new UpdateFbsWarehouseAction())->run($dto);
    }
}
