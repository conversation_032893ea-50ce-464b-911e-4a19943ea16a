<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\Marketplaces\Services\Wildberries\Models\WildberriesMatchedProduct;

class WildberriesProductToMatch extends Model
{
    /**
     * Таблица, связанная с моделью.
     *
     * @var string
     */
    protected $table = 'wildberries_products_to_match';

    /**
     * Первичный ключ модели.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Тип первичного ключа модели.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Указывает, должна ли модель иметь временные метки.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * Атрибуты, которые можно массово назначать.
     *
     * @var array
     */
    protected $fillable = [
        'cabinet_id',
        'wildberries_integration_id',
        'wb_id',
        'vendor_code',
        'title',
        'description',
        'size_id',
        'size_name',
        'tech_size',
        'skus',
        'price',
        'discount_price',
        'is_matched',
        'matched_product_id',
        'suggested_matches',
    ];

    /**
     * Атрибуты, которые должны быть приведены к определенным типам.
     *
     * @var array
     */
    protected $casts = [
        'skus' => 'array',
        'suggested_matches' => 'array',
        'is_matched' => 'boolean',
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
    ];

    /**
     * Получить сопоставленный товар.
     */
    public function matchedProduct(): BelongsTo
    {
        return $this->belongsTo(WildberriesMatchedProduct::class, 'matched_product_id');
    }
}
