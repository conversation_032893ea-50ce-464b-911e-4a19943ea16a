<?php

namespace App\Modules\Marketplaces\Services\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait HasCabinetPrices
{
    protected function preloadCabinetPrices(): void
    {
        $prices = DB::table('cabinet_prices')
            ->where('cabinet_id', $this->cabinetId)
            ->get();

        foreach ($prices as $price) {
            $this->cabinetPricesCache[$price->name] = $price;
        }

        if (!isset($this->cabinetPricesCache['Розничная цена'])) {
            $this->createDefaultPrice('Розничная цена');
        }

        if (!isset($this->cabinetPricesCache['Цена со скидкой'])) {
            $this->createDefaultPrice('Цена со скидкой');
        }
    }

    protected function createDefaultPrice(string $priceName): void
    {
        $priceId = $this->generateUuid();
        $now = Carbon::now();

        $price = [
            'id' => $priceId,
            'cabinet_id' => $this->cabinetId,
            'name' => $priceName,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        DB::table('cabinet_prices')->insert($price);
        $this->cabinetPricesCache[$priceName] = (object)$price;
    }
}
