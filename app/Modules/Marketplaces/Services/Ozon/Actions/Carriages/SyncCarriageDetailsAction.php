<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages;

use App\Clients\Ozon\API;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Action для синхронизации детальной информации об отгрузках с Ozon
 */
class SyncCarriageDetailsAction
{
    /**
     * Синхронизировать детальную информацию об отгрузках
     *
     * @param int $maxAttempts Максимальное количество попыток синхронизации
     * @param int $batchSize Размер батча для обработки
     * @return array Статистика синхронизации
     */
    public function run(int $maxAttempts = 5, int $batchSize = 50): array
    {
        $stats = [
            'processed' => 0,
            'synced' => 0,
            'failed' => 0,
            'skipped' => 0,
        ];

        // Получаем отгрузки, которые нужно синхронизировать
        $carriagesToSync = $this->getCarriagesToSync($maxAttempts, $batchSize);

        foreach ($carriagesToSync as $carriage) {
            $stats['processed']++;

            try {
                $syncResult = $this->syncCarriageDetails($carriage);
                
                if ($syncResult) {
                    $stats['synced']++;
                } else {
                    $stats['skipped']++;
                }

            } catch (Exception $e) {
                $stats['failed']++;
                $this->handleSyncError($carriage, $e);
            }
        }

        Log::info('Carriage sync completed', $stats);

        return $stats;
    }

    /**
     * Получить отгрузки для синхронизации
     *
     * @param int $maxAttempts Максимальное количество попыток
     * @param int $batchSize Размер батча
     * @return \Illuminate\Support\Collection Коллекция отгрузок
     */
    protected function getCarriagesToSync(int $maxAttempts, int $batchSize): \Illuminate\Support\Collection
    {
        return DB::table('ozon_carriages as c')
            ->join('ozon_integrations as i', 'c.integration_id', '=', 'i.id')
            ->where('c.is_synced', false)
            ->where('c.sync_attempts', '<', $maxAttempts)
            ->where(function ($query) {
                // Синхронизируем только если прошло более 5 минут с последней попытки
                $query->whereNull('c.last_sync_attempt')
                      ->orWhere('c.last_sync_attempt', '<', now()->subMinutes(5));
            })
            ->select([
                'c.id',
                'c.ozon_carriage_id',
                'c.status',
                'c.sync_attempts',
                'i.api_key',
                'i.client_id'
            ])
            ->limit($batchSize)
            ->get();
    }

    /**
     * Синхронизировать детальную информацию об отгрузке
     *
     * @param object $carriage Данные отгрузки
     * @return bool Успешность синхронизации
     */
    protected function syncCarriageDetails(object $carriage): bool
    {
        // Обновляем счетчик попыток
        $this->updateSyncAttempt($carriage->id);

        try {
            // Получаем информацию об отгрузке из Ozon
            $carriageInfo = $this->getCarriageInfoFromOzon($carriage);

            // Сохраняем детальную информацию
            $this->saveCarriageDetails($carriage->id, $carriageInfo);

            // Помечаем как синхронизированную
            $this->markAsSynced($carriage->id);

            return true;

        } catch (Exception $e) {
            // Если отгрузка еще не готова в Ozon, это нормально
            if ($this->isCarriageNotReadyError($e)) {
                Log::info("Carriage {$carriage->ozon_carriage_id} not ready yet", [
                    'carriage_id' => $carriage->id,
                    'attempt' => $carriage->sync_attempts + 1
                ]);
                return false;
            }

            throw $e;
        }
    }

    /**
     * Получить информацию об отгрузке из Ozon API
     *
     * @param object $carriage Данные отгрузки
     * @return object Информация об отгрузке
     */
    protected function getCarriageInfoFromOzon(object $carriage): object
    {
        $api = new API(
            apiKey: decrypt($carriage->api_key),
            clientId: decrypt($carriage->client_id)
        );

        return $api->FBS()->getCarriageInfo((int) $carriage->ozon_carriage_id);
    }

    /**
     * Сохранить детальную информацию об отгрузке
     *
     * @param string $carriageId ID отгрузки в нашей системе
     * @param object $carriageInfo Информация из Ozon API
     * @return void
     */
    protected function saveCarriageDetails(string $carriageId, object $carriageInfo): void
    {
        $detailsData = [
            'id' => Str::orderedUuid()->toString(),
            'carriage_id' => $carriageId,
            'act_type' => $carriageInfo->act_type ?? null,
            'is_waybill_enabled' => $carriageInfo->is_waybill_enabled ?? false,
            'is_econom' => $carriageInfo->is_econom ?? false,
            'arrival_pass_ids' => isset($carriageInfo->arrival_pass_ids) ? json_encode($carriageInfo->arrival_pass_ids) : null,
            'available_actions' => isset($carriageInfo->available_actions) ? json_encode($carriageInfo->available_actions) : null,
            'cancel_availability' => isset($carriageInfo->cancel_availability) ? json_encode($carriageInfo->cancel_availability) : null,
            'company_id' => $carriageInfo->company_id ?? null,
            'containers_count' => $carriageInfo->containers_count ?? null,
            'ozon_created_at' => isset($carriageInfo->created_at) ? Carbon::parse($carriageInfo->created_at) : null,
            'first_mile_type' => $carriageInfo->first_mile_type ?? null,
            'has_postings_for_next_carriage' => $carriageInfo->has_postings_for_next_carriage ?? false,
            'integration_type' => $carriageInfo->integration_type ?? null,
            'is_container_label_printed' => $carriageInfo->is_container_label_printed ?? false,
            'is_partial' => $carriageInfo->is_partial ?? false,
            'partial_num' => $carriageInfo->partial_num ?? null,
            'retry_count' => $carriageInfo->retry_count ?? 0,
            'ozon_status' => $carriageInfo->status ?? null,
            'tpl_provider_id' => $carriageInfo->tpl_provider_id ?? null,
            'ozon_updated_at' => isset($carriageInfo->updated_at) ? Carbon::parse($carriageInfo->updated_at) : null,
            'ozon_warehouse_id' => $carriageInfo->warehouse_id ?? null,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Удаляем существующие детали (если есть) и вставляем новые
        DB::table('ozon_carriage_details')->where('carriage_id', $carriageId)->delete();
        DB::table('ozon_carriage_details')->insert($detailsData);
    }

    /**
     * Обновить счетчик попыток синхронизации
     *
     * @param string $carriageId ID отгрузки
     * @return void
     */
    protected function updateSyncAttempt(string $carriageId): void
    {
        DB::table('ozon_carriages')
            ->where('id', $carriageId)
            ->update([
                'sync_attempts' => DB::raw('sync_attempts + 1'),
                'last_sync_attempt' => now(),
                'updated_at' => now(),
            ]);
    }

    /**
     * Пометить отгрузку как синхронизированную
     *
     * @param string $carriageId ID отгрузки
     * @return void
     */
    protected function markAsSynced(string $carriageId): void
    {
        DB::table('ozon_carriages')
            ->where('id', $carriageId)
            ->update([
                'is_synced' => true,
                'updated_at' => now(),
            ]);
    }

    /**
     * Обработать ошибку синхронизации
     *
     * @param object $carriage Данные отгрузки
     * @param Exception $exception Исключение
     * @return void
     */
    protected function handleSyncError(object $carriage, Exception $exception): void
    {
        Log::error('Failed to sync carriage details', [
            'carriage_id' => $carriage->id,
            'ozon_carriage_id' => $carriage->ozon_carriage_id,
            'attempt' => $carriage->sync_attempts + 1,
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Проверить, является ли ошибка признаком того, что отгрузка еще не готова
     *
     * @param Exception $exception Исключение
     * @return bool
     */
    protected function isCarriageNotReadyError(Exception $exception): bool
    {
        $message = strtolower($exception->getMessage());
        
        // Типичные сообщения об ошибках, когда отгрузка еще не готова
        $notReadyMessages = [
            'carriage not found',
            'not found',
            'carriage is not ready',
            'processing',
        ];

        foreach ($notReadyMessages as $notReadyMessage) {
            if (str_contains($message, $notReadyMessage)) {
                return true;
            }
        }

        return false;
    }
}
