<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Products;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetMatchedProductsAction
{
    /**
     * Получает список сопоставленных товаров для указанного кабинета и интеграции
     *
     * @param string $cabinetId ID кабинета
     * @param string $integrationId ID интеграции
     * @return Collection Коллекция сопоставленных товаров
     */
    public function run(string $cabinetId, string $integrationId): Collection
    {
        return DB::table('ozon_matched_products')
            ->where('ozon_matched_products.cabinet_id', $cabinetId)
            ->where('ozon_matched_products.integration_id', $integrationId)
            ->join('products', 'ozon_matched_products.product_id', '=', 'products.id')
            ->select([
                'ozon_matched_products.*',
                'products.title as product_title',
                'products.article as product_article',
                'products.code as product_code'
            ])
            ->orderBy('ozon_matched_products.created_at', 'desc')
            ->get();
    }
}
