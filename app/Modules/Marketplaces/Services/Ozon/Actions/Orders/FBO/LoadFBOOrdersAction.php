<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBO;

use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base\BaseLoadOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Jobs\Orders\SyncFBOOrdersJob;
use Illuminate\Support\Facades\Queue;

class LoadFBOOrdersAction extends BaseLoadOrdersAction
{
    public function run(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $integration = $this->getIntegrationData($integrationId);

        $parameters = $this->prepareJobParameters($integration, $dateFrom, $dateTo);

        Queue::push(new SyncFBOOrdersJob(
            cabinetId: $parameters['cabinet_id'],
            apiKey: $parameters['api_key'],
            clientId: $parameters['client_id'],
            integrationId: $parameters['integration_id'],
            legalEntityId: $parameters['legal_entity_id'],
            employeeId: $parameters['employee_id'],
            departmentId: $parameters['department_id'],
            contractorId: $parameters['contractor_id'],
            orderNumberingType: $parameters['order_numbering_type'],
            addPrefix: $parameters['add_prefix'],
            prefix: $parameters['prefix'],
            reserve: $parameters['reserve'],
            dateFrom: $parameters['date_from'],
            dateTo: $parameters['date_to']
        ));
    }
}
