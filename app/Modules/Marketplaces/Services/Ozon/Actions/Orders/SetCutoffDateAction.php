<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Ozon\Enums\FbsOrderAvaibleActionsEnum;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class SetCutoffDateAction
{
    public function run(string $orderId, string $newCutoffDate): void
    {
        $order = $this->getOrder($orderId);

        $this->validateCutoffAction($order, $newCutoffDate);

        $response = $this->setCutoffDateInOzon($order, $newCutoffDate);

        if($response->result !== true) {
            throw new RuntimeException($response->message);
        }

        $this->updateOrderCutoffDate($order->id, $newCutoffDate);
    }

    protected function getOrder(string $orderId): object
    {
        $order = DB::table('ozon_fbs_orders as o')
            ->join('ozon_integrations as i', 'o.integration_id', '=', 'i.id')
            ->where('o.id', $orderId)
            ->select([
                'o.id',
                'o.posting_number',
                'o.status',
                'o.shipment_date',
                'o.available_actions',
                'i.api_key',
                'i.client_id'
            ])
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        return $order;
    }

    private function validateCutoffAction(object $order, string $newCutoffDate): void
    {
        $availableActions = json_decode($order->available_actions, true, 512, JSON_THROW_ON_ERROR) ?? [];

        if (!in_array(FbsOrderAvaibleActionsEnum::SET_CUTOFF->value, $availableActions)) {
            throw new RuntimeException('Set cutoff action is not available for this order');
        }

        if ($order->shipment_date) {
            $shipmentDate = Carbon::parse($order->shipment_date);
            $cutoffDate = Carbon::parse($newCutoffDate);

            if ($cutoffDate->isAfter($shipmentDate)) {
                throw new RuntimeException("New cutoff date ({$newCutoffDate}) cannot be after shipment date ({$order->shipment_date})");
            }
        }
    }

    protected function setCutoffDateInOzon(object $order, string $newCutoffDate): object
    {
        $api = new API(
            apiKey: decrypt($order->api_key),
            clientId: decrypt($order->client_id)
        );

        return $api->FBS()->setCutoffDate($order->posting_number, $newCutoffDate);
    }

    protected function updateOrderCutoffDate(string $orderId, string $newCutoffDate): void
    {
        DB::table('ozon_fbs_orders')
            ->where('id', $orderId)
            ->update([
                'cutoff_date' => $newCutoffDate,
                'updated_at' => now(),
            ]);
    }
}
