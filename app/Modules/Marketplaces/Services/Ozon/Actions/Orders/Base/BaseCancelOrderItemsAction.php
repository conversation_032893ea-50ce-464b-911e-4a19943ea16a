<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base;

use App\Exceptions\NotFoundException;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

/**
 * Базовый класс для Actions отмены заказов
 */
abstract class BaseCancelOrderItemsAction
{
    /**
     * Получение заказа с проверкой типа
     *
     * @param string $orderId ID заказа
     * @return object Данные заказа
     * @throws NotFoundException
     * @throws RuntimeException
     */
    protected function getOrderWithValidation(string $orderId): object
    {
        $tableName = $this->getOrderTableName();

        $order = DB::table($tableName)
            ->join('ozon_integrations', "{$tableName}.integration_id", '=', 'ozon_integrations.id')
            ->where("{$tableName}.id", $orderId)
            ->select([
                "{$tableName}.*",
                'ozon_integrations.api_key as api_key',
                'ozon_integrations.client_id as client_id',
            ])
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        return $order;
    }

    /**
     * Отмена отдельных товаров в заказе
     *
     * Если отменяется часть товаров, количество в БД уменьшается на указанное количество.
     * Если отменяется всё количество товара, запись удаляется из БД.
     */
    public function run(
        string $orderId,
        int $cancelReasonId,
        string $cancelReasonMessage,
        array $items,
    ): bool {
        $order = $this->getOrderWithValidation($orderId);

        $itemsToCancel = $this->getOrderItemsForCancellation($orderId, $items);

        if (empty($itemsToCancel)) {
            throw new NotFoundException('No valid items found for cancellation');
        }

        try {
            DB::beginTransaction();

            $this->cancelOrderItemsInOzon(
                postingNumber: $order->posting_number,
                cancelReasonId: $cancelReasonId,
                cancelReasonMessage: $cancelReasonMessage,
                items: $itemsToCancel,
                clientId: decrypt($order->client_id),
                apiKey: decrypt($order->api_key),
            );

            $this->updateOrderItemsQuantity($orderId, $items);

            DB::commit();
        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }

        return true;
    }

    /**
     * Получить товары заказа для отмены с их SKU
     */
    protected function getOrderItemsForCancellation(string $orderId, array $items): array
    {
        $itemIds = array_column($items, 'id');
        $itemQuantities = array_column($items, 'quantity', 'id');

        $orderItems = DB::table($this->getOrderItemsTableName())
            ->where($this->getOrderIdFieldName(), $orderId)
            ->whereIn('id', $itemIds)
            ->select(['id', 'sku', 'quantity'])
            ->get();

        if ($orderItems->isEmpty()) {
            throw new NotFoundException('Order items not found');
        }

        $itemsToCancel = [];
        foreach ($orderItems as $orderItem) {
            $requestedQuantity = $itemQuantities[$orderItem->id] ?? 0;

            if ($requestedQuantity <= 0) {
                throw new \InvalidArgumentException(
                    "Requested quantity must be greater than 0 for item {$orderItem->id}"
                );
            }

            if ($requestedQuantity > $orderItem->quantity) {
                throw new \InvalidArgumentException(
                    "Requested quantity ({$requestedQuantity}) exceeds available quantity ({$orderItem->quantity}) for item {$orderItem->id}"
                );
            }

            $itemsToCancel[] = [
                'sku' => (int) $orderItem->sku,
                'quantity' => $requestedQuantity,
            ];
        }

        return $itemsToCancel;
    }

    /**
     * Обновить количество товаров в заказе после отмены
     */
    protected function updateOrderItemsQuantity(string $orderId, array $items): void
    {
        $itemIds = array_column($items, 'id');

        $currentItems = DB::table($this->getOrderItemsTableName())
            ->where($this->getOrderIdFieldName(), $orderId)
            ->whereIn('id', $itemIds)
            ->select(['id', 'quantity'])
            ->get()
            ->keyBy('id');

        $itemsToDelete = [];
        $itemsToUpdate = [];

        foreach ($items as $item) {
            $currentItem = $currentItems->get($item['id']);

            if (!$currentItem) {
                continue;
            }

            $newQuantity = $currentItem->quantity - $item['quantity'];

            if ($newQuantity <= 0) {
                $itemsToDelete[] = $item['id'];
            } else {
                $itemsToUpdate[] = [
                    'id' => $item['id'],
                    'quantity' => $newQuantity
                ];
            }
        }

        if (!empty($itemsToDelete)) {
            DB::table($this->getOrderItemsTableName())
                ->where($this->getOrderIdFieldName(), $orderId)
                ->whereIn('id', $itemsToDelete)
                ->delete();
        }

        if (!empty($itemsToUpdate)) {
            $this->bulkUpdateQuantities($orderId, $itemsToUpdate);
        }
    }

    /**
     * Массовое обновление количества товаров одним SQL запросом
     */
    protected function bulkUpdateQuantities(string $orderId, array $itemsToUpdate): void
    {
        if (empty($itemsToUpdate)) {
            return;
        }

        $tableName = $this->getOrderItemsTableName();
        $orderIdField = $this->getOrderIdFieldName();

        $caseStatement = 'CASE id ';
        $ids = [];

        foreach ($itemsToUpdate as $item) {
            $caseStatement .= "WHEN '{$item['id']}' THEN {$item['quantity']} ";
            $ids[] = "'{$item['id']}'";
        }

        $caseStatement .= 'END';
        $idsString = implode(',', $ids);

        DB::statement("
            UPDATE {$tableName}
            SET quantity = {$caseStatement}
            WHERE {$orderIdField} = ? AND id IN ({$idsString})
        ", [$orderId]);
    }

    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderTableName(): string;

    /**
     * Получить имя поля для связи с заказом в связанных таблицах
     *
     * @return string Имя поля
     */
    abstract protected function getOrderIdFieldName(): string;

    /**
     * Получить имя таблицы товаров заказа
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderItemsTableName(): string;

    /**
     * Отменить товары заказа в Ozon через API
     *
     * @param string $postingNumber Номер отправления
     * @param int $cancelReasonId ID причины отмены
     * @param string $cancelReasonMessage Сообщение причины отмены
     * @param array $items Массив товаров для отмены
     * @param int $clientId ID клиента
     * @param string $apiKey API ключ
     * @return void
     */
    abstract protected function cancelOrderItemsInOzon(
        string $postingNumber,
        int $cancelReasonId,
        string $cancelReasonMessage,
        array $items,
        int $clientId,
        string $apiKey,
    ): void;
}
