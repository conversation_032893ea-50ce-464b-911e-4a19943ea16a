<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\DB;

/**
 * Action для получения списка доступных стран-изготовителей
 */
class GetCountryListAction
{
    /**
     * Получить список доступных стран-изготовителей
     *
     * @param string $integrationId ID интеграции
     * @return object Список стран с ISO кодами
     * @throws NotFoundException
     */
    public function run(string $integrationId): object
    {
        // Получаем интеграцию
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->select(['api_key', 'client_id'])
            ->first();

        if (!$integration) {
            throw new NotFoundException('Integration not found');
        }

        // Вызываем API Ozon
        $api = new API(
            apiKey: decrypt($integration->api_key),
            clientId: decrypt($integration->client_id)
        );

        return $api->FBS()->getCountryList();
    }
}
