<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS;

use App\Clients\Ozon\API;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base\BaseCancelOrderAction;

/**
 * Action для отмены FBS заказов
 */
class CancelFBSOrderAction extends BaseCancelOrderAction
{
    /**
     * Получить имя таблицы заказов
     */
    protected function getOrderTableName(): string
    {
        return 'ozon_fbs_orders';
    }

    /**
     * Получить имя поля для связи с заказом в связанных таблицах
     */
    protected function getOrderIdFieldName(): string
    {
        return 'order_id';
    }

    protected function cancelOrderInOzon(
        string $postingNumber,
        int $cancelReasonId,
        ?string $cancelReasonMessage,
        int $clientId,
        string $apiKey
    ): void {
        $api = new API(
            apiKey: $apiKey,
            clientId: $clientId
        );
        $api->FBS()->cancelOrder($postingNumber, $cancelReasonId, $cancelReasonMessage);
    }
}
