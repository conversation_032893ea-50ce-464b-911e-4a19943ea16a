<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class SetLastMileStatusAction
{
    public function run(string $integrationId, array $orderIds): void
    {
        $orders = $this->getOrdersWithIntegration($integrationId, $orderIds);

        if ($orders->isEmpty()) {
            throw new NotFoundException('Orders not found');
        }

        $postingNumbers = $orders->pluck('posting_number')->toArray();
        $integrationData = $orders->first();

        $ozonResponse = $this->setLastMileStatusInOzon($integrationData, $postingNumbers);

        if($ozonResponse->result !== true) {
            throw new RuntimeException($ozonResponse->result->error . ' on posting numbers: ' . $ozonResponse->result->posting_number);
        }

        $this->updateOrdersStatus($orderIds);
    }

    protected function getOrdersWithIntegration(string $integrationId, array $orderIds): Collection
    {
        return DB::table('ozon_fbs_orders as o')
            ->join('ozon_integrations as i', 'o.integration_id', '=', 'i.id')
            ->whereIn('o.id', $orderIds)
            ->where('o.integration_id', $integrationId)
            ->select([
                'o.id',
                'o.posting_number',
                'o.status',
                'i.api_key',
                'i.client_id'
            ])
            ->get();
    }

    protected function setLastMileStatusInOzon(object $integrationData, array $postingNumbers): object
    {
        $api = new API(
            apiKey: decrypt($integrationData->api_key),
            clientId: decrypt($integrationData->client_id)
        );

        return $api->FBS()->setLastMileStatus($postingNumbers);
    }

    protected function updateOrdersStatus(array $orderIds): void
    {
        DB::table('ozon_fbs_orders')
            ->whereIn('id', $orderIds)
            ->update([
                'status' => 'last_mile',
                'updated_at' => now(),
            ]);
    }
}
