<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs;

use App\Clients\Ozon\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class LoadFBOWarehousesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use HasOrderedUuid;

    private string $defaultWarehouseId;
    private API $api;

    public function __construct(
        private readonly string $cabinetId,
        private readonly string $integrationId,
        protected readonly string $apiKey,
        protected readonly int $clientId,
    ) {
    }

    /**
     * @throws WBSellerException|Throwable
     */
    public function handle(): void
    {
        $this->api = new API(
            apiKey: $this->apiKey,
            clientId: $this->clientId
        );
        $warehouses = $this->fetchWarehouses();

        $this->processWarehouses($warehouses);
    }

    private function fetchWarehouses(): Collection
    {
        $response = $this->api->FboWarehouses()->list();
        return collect($response->result);
    }

    private function processWarehouses(Collection $warehouses): void
    {
        DB::beginTransaction();

        try {
            $currentWarehouses = DB::table('ozon_fbo_warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->select('ozon_warehouse_id')
                ->pluck('ozon_warehouse_id');

            $this->defaultWarehouseId = DB::table('warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->where('is_default', true)
                ->value('id');

            $this->processNewWarehouses(
                $warehouses,
                $currentWarehouses->toArray()
            );

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function processNewWarehouses(object $warehouses, array $currentWarehouseIds): void
    {
        $warehouseIds = $warehouses->pluck('warehouse.id')->toArray();
        $newWarehouseIds = array_diff($warehouseIds, $currentWarehouseIds);

        if (empty($newWarehouseIds)) {
            return;
        }

        $newWarehouses = $warehouses->whereIn('warehouse.id', $newWarehouseIds)->pluck('warehouse');

        $toInsertArray = [];
        foreach ($newWarehouses as $warehouse) {
            $toInsertArray[] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $this->cabinetId,
                'integration_id' => $this->integrationId,
                'created_at' => now(),
                'updated_at' => now(),
                'ozon_warehouse_id' => $warehouse->id,
                'name' => $warehouse->name,
                'warehouse_id' => $this->defaultWarehouseId,
            ];
        }

        if (!empty($toInsertArray)) {
            DB::table('ozon_fbo_warehouses')->insert($toInsertArray);
        }
    }
}
