<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs\Stocks;

use App\Clients\Ozon\API;
use App\Services\Api\RateLimiter\Facades\RateLimiter;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class SyncStocksJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    private API $api;

    public function __construct(
        private readonly string $cabinetId,
        private readonly string $apiKey,
        private readonly int $clientId,
        private readonly string $integrationId
    ) {
        $this->api = new API(
            apiKey: $this->apiKey,
            clientId: $this->clientId
        );
    }

    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $matchedProducts = DB::table('ozon_matched_products')
                ->join('products', 'ozon_matched_products.product_id', '=', 'products.id')
                ->where('ozon_matched_products.cabinet_id', $this->cabinetId)
                ->where('ozon_matched_products.integration_id', $this->integrationId)
                ->select('ozon_matched_products.*', 'products.article', 'products.code')
                ->get();

            $warehouses = DB::table('ozon_fbs_warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->where('integration_id', $this->integrationId)
                ->get();

            $stocksByWarehouse = [];

            foreach ($matchedProducts as $product) {
                foreach ($warehouses as $warehouse) {
                    $warehouseId = $warehouse->warehouse_id;
                    $ozonWarehouseId = $warehouse->ozon_warehouse_id;

                    $stockQuantity = $this->getProductStock($product->product_id, $warehouseId);

                    if (!isset($stocksByWarehouse[$ozonWarehouseId])) {
                        $stocksByWarehouse[$ozonWarehouseId] = [];
                    }

                    $stocksByWarehouse[$ozonWarehouseId][] = [
                        'product_id' => $product->ozon_product_id,
                        'offer_id' => $product->offer_id,
                        'stock' => $stockQuantity,
                        'warehouse_id' => $ozonWarehouseId
                    ];
                }
            }

            foreach ($stocksByWarehouse as $stocks) {
                $chunks = array_chunk($stocks, 100);

                foreach ($chunks as $chunk) {
                    RateLimiter::throttle('ozon_stocks');

                    try {
                        $this->api->products()->updateStocks($chunk);

                    } catch (Exception $e) {
                        if (str_contains($e->getMessage(), 'TOO_MANY_REQUESTS') || $e->getCode() == 409) {
                            RateLimiter::registerError409('ozon_stocks');
                        }
                        throw $e;
                    }
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function getProductStock(string $productId, string $warehouseId): int
    {
        $stock = DB::table('warehouse_items')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', 'in_stock')
            ->sum('quantity');

        return (int)$stock;
    }
}
