<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs;

use App\Modules\Marketplaces\Services\Ozon\Actions\Carriages\CheckDocumentsStatusAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckCarriageDocumentsJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly int $maxAttempts = 5,
        private readonly int $batchSize = 50
    ) {
    }

    public function handle(): void
    {
        (new CheckDocumentsStatusAction())->run($this->maxAttempts, $this->batchSize);
    }
}
