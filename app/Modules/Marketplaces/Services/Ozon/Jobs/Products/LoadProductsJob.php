<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs\Products;

use App\Clients\Ozon\API;
use App\Traits\HasOrderedUuid;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для загрузки товаров из Ozon
 */
class LoadProductsJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use HasOrderedUuid;

    private API $api;

    public function __construct(
        private readonly string $cabinetId,
        private readonly string $apiKey,
        protected readonly int $clientId,
        private readonly string $integrationId,
    ) {
        $this->api = new API(
            apiKey: $this->apiKey,
            clientId: $this->clientId
        );
    }

    /**
     * @throws BindingResolutionException|Throwable
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $products = $this->fetchProductsFromOzon();

            $this->processProducts($products);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function fetchProductsFromOzon(): Collection
    {
        $products = collect();

        try {
            $request = $this->api->products()->list();

            if (empty($request->result->items)) {
                return collect();
            }

            $products->push($request->result->items);
            $totalRemaining = $request->result->total - 1000;

            while ($totalRemaining > 0) {
                try {
                    $request = $this->api->products()->list(
                        lastId: $request->last_id
                    );

                    $products->push($request->result->items);
                    $totalRemaining -= count($request->result->items);
                } catch (Exception $e) {
                    Log::error('Error fetching products from Ozon', [
                        'cabinet_id' => $this->cabinetId,
                        'error' => $e->getMessage(),
                    ]);

                    if (isset($request->result)) {
                        $totalRemaining -= 1000;
                    } else {
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            Log::error('Error fetching initial products from Ozon', [
                'cabinet_id' => $this->cabinetId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }

        return $products->flatten(1);
    }

    /**
     * @throws Exception
     */
    private function processProducts(Collection $products): void
    {
        if ($products->isEmpty()) {
            return;
        }

        // Получаем список уже сопоставленных товаров
        $existingMatchedProducts = DB::table('ozon_matched_products')
            ->where('cabinet_id', $this->cabinetId)
            ->where('integration_id', $this->integrationId)
            ->get();

        // Создаем карту соответствий внешних ID и ID размеров для сопоставленных товаров
        $existingMatchedIds = [];
        foreach ($existingMatchedProducts as $matchedProduct) {
            $key = "{$matchedProduct->product_id}-{$matchedProduct->offer_id}";
            $existingMatchedIds[$key] = $matchedProduct->id;
        }

        // Получаем список товаров к сопоставлению
        $existingProductsToMatch = DB::table('ozon_products_to_match')
            ->where('cabinet_id', $this->cabinetId)
            ->where('integration_id', $this->integrationId)
            ->get();

        // Создаем карту соответствий внешних ID и ID размеров для товаров к сопоставлению
        $existingProductsToMatchIds = [];
        foreach ($existingProductsToMatch as $productToMatch) {
            $key = "{$productToMatch->ozon_product_id}-{$productToMatch->offer_id}";
            $existingProductsToMatchIds[$key] = $productToMatch->id;
        }

        $infoMap = $this->fetchGoodsInfoFromOzon($products->pluck('product_id')->toArray());

        $toInsert = [];
        foreach ($products as $ozonProduct) {
            $key = "{$ozonProduct->product_id}-{$ozonProduct->offer_id}";

            // Если товар уже сопоставлен или товар уже добавлен в список товаров к сопоставлению, пропускаем его
            if (isset($existingMatchedIds[$key]) || isset($existingProductsToMatchIds[$key])) {
                continue;
            }

            $price = null;
            $minPrice = null;
            $prediscountPrice = null;
            $title = null;
            $description = null;
            $sku = null;
            $barcodes = null;
            foreach ($infoMap as $infoKey => $infoData) {
                if (str_starts_with($infoKey, "{$ozonProduct->product_id}-")) {
                    $minPrice = $infoData['min_price'];
                    $prediscountPrice = $infoData['prediscount_price'];
                    $price = $infoData['price'];
                    $title = $infoData['name'];
                    $description = $infoData['description'];
                    $sku = $infoData['sku'] ?? null;
                    $barcodes = json_encode($infoData['barcodes'] ?? [], JSON_THROW_ON_ERROR);
                    break;
                }
            }

            $toInsert[] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $this->cabinetId,
                'integration_id' => $this->integrationId,
                'ozon_product_id' => $ozonProduct->product_id,
                'offer_id' => $ozonProduct->offer_id,
                'title' => $title,
                'description' => $description,
                'sku' => $sku,
                'barcodes' => $barcodes,
                'price' => $price,
                'prediscount_price' => $prediscountPrice,
                'min_price' => $minPrice,
                'is_matched' => false,
                'suggested_matches' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($toInsert)) {
            DB::table('ozon_products_to_match')->insert($toInsert);
        }
    }

    private function fetchGoodsInfoFromOzon(array $productIds): array
    {
        if (empty($productIds)) {
            return [];
        }

        $infoMap = [];

        $batches = array_chunk($productIds, 1000);

        foreach ($batches as $batch) {
            $basicInfo = $this->fetchBasicProductInfo($batch);

            $attributesInfo = $this->fetchProductAttributes($batch);

            $this->mergeProductInfo($infoMap, $basicInfo, $attributesInfo);
        }

        return $infoMap;
    }

    private function fetchBasicProductInfo(array $batch): array
    {
        $basicInfo = [];

        try {
            $response = $this->api->products()->getInfoList(
                productIds: $batch
            );

            if (empty($response->items)) {
                return $basicInfo;
            }

            foreach ($response->items as $item) {
                $key = "{$item->id}-{$item->offer_id}";
                $basicInfo[$key] = [
                    'product_id' => $item->id,
                    'offer_id' => $item->offer_id,
                    'prediscount_price' => $item->old_price ?? null,
                    'min_price' => $item->min_price ?? null,
                    'price' => $item->price,
                    'name' => $item->name,
                    'description' => /*$this->api->products()->getDescription($item->id)?->result->description ??*/ null,
                ];
            }
        } catch (Exception $e) {
            Log::error('Error fetching basic product info from ozon', [
                'cabinet_id' => $this->cabinetId,
                'batch_size' => count($batch),
                'error' => $e->getMessage()
            ]);
        }

        return $basicInfo;
    }

    private function fetchProductAttributes(array $batch): array
    {
        $attributesInfo = [];

        try {
            $filter = (object) [
                'product_id' => $batch
            ];

            $lastId = null;

            do {
                $response = $this->api->products()->getProductAttributesInfo(
                    filter: $filter,
                    lastId: $lastId,
                    limit: 1000,
                    sortBy: 'id',
                    sortDir: 'desc'
                );

                $items = $response->result;

                if (empty($items)) {
                    break;
                }

                foreach ($items as $item) {
                    $key = "{$item->id}-{$item->offer_id}";
                    $attributesInfo[$key] = [
                        'product_id' => $item->id,
                        'offer_id' => $item->offer_id,
                        'sku' => $item->sku ?? null,
                        'barcodes' => $item->barcodes ?? [],
                    ];
                }

                $lastId = $response->last_id ?? null;

            } while (!empty($lastId));

        } catch (Exception $e) {
            Log::error('Error fetching product attributes from ozon', [
                'cabinet_id' => $this->cabinetId,
                'batch_size' => count($batch),
                'error' => $e->getMessage()
            ]);
        }

        return $attributesInfo;
    }

    private function mergeProductInfo(array &$infoMap, array $basicInfo, array $attributesInfo): void
    {
        foreach ($basicInfo as $key => $info) {
            $infoMap[$key] = $info;

            if (isset($attributesInfo[$key])) {
                $infoMap[$key]['sku'] = $attributesInfo[$key]['sku'];
                $infoMap[$key]['barcodes'] = $attributesInfo[$key]['barcodes'];
            } else {
                $infoMap[$key]['sku'] = null;
                $infoMap[$key]['barcodes'] = [];
            }
        }

        foreach ($attributesInfo as $key => $info) {
            if (!isset($infoMap[$key])) {
                $infoMap[$key] = [
                    'product_id' => $info['product_id'],
                    'offer_id' => $info['offer_id'],
                    'prediscount_price' => null,
                    'min_price' => null,
                    'price' => null,
                    'name' => null,
                    'description' => null,
                    'sku' => $info['sku'],
                    'barcodes' => $info['barcodes'],
                ];
            }
        }
    }
}
