<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs;

use App\Clients\Ozon\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Ozon\Actions\Warehouses\LoadWarehouseDeliveryMethodsAction;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use JsonException;
use Log;
use Throwable;

class LoadFBSWarehousesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use HasOrderedUuid;

    private string $defaultWarehouseId;
    private API $api;

    /**
     * Создает новый экземпляр задачи.
     *
     * @param string $cabinetId ID кабинета
     * @param string $integrationId ID интеграции
     * @param string $apiKey
     * @param int $clientId
     */
    public function __construct(
        private readonly string $cabinetId,
        private readonly string $integrationId,
        protected readonly string $apiKey,
        protected readonly int $clientId,
    ) {
    }

    /**
     * @throws WBSellerException|Throwable
     */
    public function handle(): void
    {
        $this->api = new API(
            apiKey: $this->apiKey,
            clientId: $this->clientId
        );
        $warehouses = $this->fetchWarehouses();

        $this->processWarehouses($warehouses);

        // Загружаем методы доставки для складов
        $this->loadDeliveryMethods();
    }

    /**
     * Получает список складов из API Wildberries.
     *
     * @return Collection Коллекция складов
     */
    private function fetchWarehouses(): Collection
    {
        return collect($this->api->FbsWarehouses()->list());
    }

    /**
     * Обрабатывает данные о складах и сохраняет их в БД.
     *
     * @param Collection $warehouses Коллекция складов
     * @return void
     * @throws Exception|Throwable
     */
    private function processWarehouses(Collection $warehouses): void
    {
        DB::beginTransaction();

        try {
            $currentWarehouses = DB::table('ozon_fbs_warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->select('ozon_warehouse_id')
                ->pluck('ozon_warehouse_id');

            $this->defaultWarehouseId = DB::table('warehouses')
                ->where('cabinet_id', $this->cabinetId)
                ->where('is_default', true)
                ->value('id');

            $this->processNewWarehouses(
                $warehouses,
                $currentWarehouses->toArray()
            );

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Обрабатывает новые склады и добавляет их в БД.
     *
     * @param Collection $warehouses Коллекция всех складов
     * @param array $currentWarehouseIds ID существующих складов
     * @return void
     * @throws JsonException
     */
    private function processNewWarehouses(Collection $warehouses, array $currentWarehouseIds): void
    {
        $warehouseIds = $warehouses->pluck('warehouse_id')->toArray();
        $newWarehouseIds = array_diff($warehouseIds, $currentWarehouseIds);

        if (empty($newWarehouseIds)) {
            return;
        }

        $newWarehouses = $warehouses->whereIn('id', $newWarehouseIds);

        $toInsertArray = [];
        foreach ($newWarehouses['result'] as $warehouse) {
            $toInsertArray[] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $this->cabinetId,
                'integration_id' => $this->integrationId,
                'created_at' => now(),
                'updated_at' => now(),
                'ozon_warehouse_id' => $warehouse->warehouse_id,
                'name' => $warehouse->name,
                'warehouse_id' => $this->defaultWarehouseId,
                'is_rfbs' => $warehouse->is_rfbs,
                'has_entrusted_acceptance' => $warehouse->has_entrusted_acceptance,
                'can_print_act_in_advance' => $warehouse->can_print_act_in_advance,
                'has_postings_limit' => $warehouse->has_postings_limit,
                'is_karantin' => $warehouse->is_karantin,
                'is_kgt' => $warehouse->is_kgt,
                'is_economy' => $warehouse->is_economy,
                'is_timetable_editable' => $warehouse->is_timetable_editable,
                'min_postings_limit' => $warehouse->min_postings_limit,
                'postings_limit' => $warehouse->postings_limit,
                'min_working_days' => $warehouse->min_working_days,
                'status' => $warehouse->status,
                'working_days' => json_encode($warehouse->working_days, JSON_THROW_ON_ERROR),
                'first_mile_type' => json_encode($warehouse->first_mile_type, JSON_THROW_ON_ERROR),
            ];
        }

        if (!empty($toInsertArray)) {
            DB::table('ozon_fbs_warehouses')->insert($toInsertArray);
        }
    }

    /**
     * Загружает методы доставки для всех складов интеграции
     *
     * @return void
     */
    private function loadDeliveryMethods(): void
    {
        try {
            $action = new LoadWarehouseDeliveryMethodsAction();
            $action->run($this->integrationId);
        } catch (Exception $e) {
            Log::warning('Failed to load delivery methods for warehouses', [
                'integration_id' => $this->integrationId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
