<?php

namespace App\Modules\Marketplaces\Services\Ozon\Enums;

/**
 * Enum для статусов заказов Ozon
 */
enum OrderStatusEnum: string
{
    case AWAITING_REGISTRATION = 'awaiting_registration';
    case ACCEPTANCE_IN_PROGRESS = 'acceptance_in_progress';
    case AWAITING_APPROVE = 'awaiting_approve';
    case AWAITING_PACKAGING = 'awaiting_packaging';
    case AWAITING_DELIVER = 'awaiting_deliver';
    case ARBITRATION = 'arbitration';
    case CLIENT_ARBITRATION = 'client_arbitration';
    case DELIVERING = 'delivering';
    case DRIVER_PICKUP = 'driver_pickup';
    case DELIVERED = 'delivered';
    case CANCELLED = 'cancelled';
    case NOT_ACCEPTED = 'not_accepted';
    case SENT_BY_SELLER = 'sent_by_seller';

    /**
     * Получить описание статуса на русском языке
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::AWAITING_REGISTRATION => 'Ожидает регистрации',
            self::ACCEPTANCE_IN_PROGRESS => 'Идёт приёмка',
            self::AWAITING_APPROVE => 'Ожидает подтверждения',
            self::AWAITING_PACKAGING => 'Ожидает упаковки',
            self::AWAITING_DELIVER => 'Ожидает отгрузки',
            self::ARBITRATION => 'Арбитраж',
            self::CLIENT_ARBITRATION => 'Клиентский арбитраж доставки',
            self::DELIVERING => 'Доставляется',
            self::DRIVER_PICKUP => 'У водителя',
            self::DELIVERED => 'Доставлено',
            self::CANCELLED => 'Отменено',
            self::NOT_ACCEPTED => 'Не принят на сортировочном центре',
            self::SENT_BY_SELLER => 'Отправлено продавцом',
        };
    }
}
