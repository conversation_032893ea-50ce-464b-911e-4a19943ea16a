<?php

namespace App\Modules\Marketplaces\Services\Ozon\Enums;

enum FBOOrderStatusEnum: string
{
    case AWAITING_PACKAGING = 'awaiting_packaging';
    case AWAITING_DELIVER = 'awaiting_deliver';
    case DELIVERING = 'delivering';
    case DELIVERED = 'delivered';
    case CANCELLED = 'cancelled';

    public function getDescription(): string
    {
        return match ($this) {
            self::AWAITING_PACKAGING => 'Ожидает упаковки',
            self::AWAITING_DELIVER => 'Ожидает отгрузки',
            self::DELIVERING => 'Доставляется',
            self::DELIVERED => 'Доставлено',
            self::CANCELLED => 'Отменено',
        };
    }
}
