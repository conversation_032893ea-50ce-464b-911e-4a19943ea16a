<?php

namespace App\Modules\Marketplaces\Services\Ozon\Data;

use App\Modules\Marketplaces\Services\Ozon\Enums\OrderNumberingTypeEnum;

class OrderSyncSettingsData
{
    public function __construct(
        public OrderNumberingTypeEnum $numType,
        public bool $addPrefix = false,
        public string $prefix = 'Озон',
        public bool $useCommonBlockContract = false,
        public bool $reserve = true,
        public bool $syncOrderStatuses = false,
        public bool $autoAcceptOrders = false,
        public bool $fairMark = false,
        public bool $autoSync = false,
    ) {
    }
}
