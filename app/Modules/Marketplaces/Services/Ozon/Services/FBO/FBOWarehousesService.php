<?php

namespace App\Modules\Marketplaces\Services\Ozon\Services\FBO;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Ozon\Actions\Warehouses\LoadFBOAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Warehouses\UpdateFBOWarehouseAction;
use App\Modules\Marketplaces\Services\Ozon\DTO\UpdateFBOWarehouseDTO;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для работы со складами Wildberries
 */
class FBOWarehousesService
{
    public function load(string $integrationId): void
    {
        (new LoadFBOAction())->run($integrationId);
    }

    public function get(string $integrationId): Collection
    {
        return DB::table('ozon_fbo_warehouses as w')
            ->where('w.integration_id', $integrationId)
            ->get();
    }

    public function update(UpdateFBOWarehouseDTO $dto): void
    {
        (new UpdateFBOWarehouseAction())->run($dto);
    }

    public function getClusters(string $integrationId, string $type)
    {
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->select('client_id', 'api_key')
            ->first();

        if (!$integration) {
            throw new NotFoundException('Integration not found');
        }

        $client = new API(
            apiKey: decrypt($integration->api_key),
            clientId: decrypt($integration->client_id)
        );

        $clusters = $client->FboWarehouses()->clustersList($type);
    }
}
