<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_arrival_passes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('carriage_id')->references('id')->on('ozon_carriages')->cascadeOnDelete();
            
            // ID пропуска от Ozon API
            $table->string('ozon_pass_id')->nullable(); // ID пропуска, возвращаемый API
            
            // Данные водителя
            $table->string('driver_name'); // ФИО водителя
            $table->string('driver_phone'); // Номер телефона водителя
            
            // Данные автомобиля
            $table->string('vehicle_license_plate'); // Номер автомобиля
            $table->string('vehicle_model'); // Модель автомобиля
            
            // Дополнительные параметры
            $table->boolean('with_returns')->default(false); // Вывоз возвратов
            
            // Статус пропуска
            $table->string('status')->default('created'); // created, active, expired, cancelled
            
            $table->timestamps();
            
            // Индексы
            $table->index(['carriage_id', 'status']);
            $table->index('ozon_pass_id');
            $table->index('driver_phone');
            $table->index('vehicle_license_plate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_arrival_passes');
    }
};
