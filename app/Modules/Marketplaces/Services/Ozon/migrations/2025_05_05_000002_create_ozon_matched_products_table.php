<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_matched_products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            // Связь с кабинетом и интеграцией
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('integration_id')->references('id')->on('ozon_integrations')->cascadeOnDelete();

            // Связь с товаром в системе
            $table->foreignUuid('product_id')->nullable()->references('id')->on('products')->nullOnDelete();

            // Данные товара из ozon
            $table->unsignedBigInteger('ozon_product_id')->comment('Идентификатор товара в системе продавца — product_id.');
            $table->string('offer_id')->nullable()->comment('Идентификатор товара в системе продавца — артикул.');
            $table->string('title')->comment('Наименование товара');

            // Штрихкоды
            $table->string('sku')->nullable()->comment('Идентификатор товара в системе Ozon — SKU.');
            $table->json('barcodes')->nullable()->comment('Все штрихкоды товара.');

            // Тип сопоставления
            $table->string('match_type')->comment('Тип сопоставления (SKU, наименование, артикул, код, штрихкод, артикул WB - штрихкод)');

            // Флаг создания товара
            $table->boolean('is_created')->default(false)->comment('Флаг создания товара в системе');

            // Индексы
            $table->index(['cabinet_id', 'integration_id']);
            $table->index('product_id');

            // Уникальный индекс для предотвращения дублирования сопоставлений
            $table->unique(['integration_id', 'ozon_product_id'], 'ozon_matched_products_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_matched_products');
    }
};
