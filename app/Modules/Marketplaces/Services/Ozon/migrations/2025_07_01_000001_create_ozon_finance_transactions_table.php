<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_finance_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('integration_id')->references('id')->on('ozon_integrations')->cascadeOnDelete();

            // Основные данные операции
            $table->unsignedBigInteger('operation_id')->unique();
            $table->dateTime('operation_date')->nullable();
            $table->string('operation_type')->nullable();
            $table->text('operation_type_name')->nullable();

            // Данные отправления
            $table->string('posting_number')->nullable();
            $table->string('delivery_schema')->nullable()->comment('FBO, FBS, CROSSBORDER, RFBS, FBP, etc.');
            $table->dateTime('order_date')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();

            // Финансовые данные
            $table->decimal('amount', 15, 2)->default(0)->comment('Итоговая сумма операции');
            $table->decimal('accruals_for_sale', 15, 2)->default(0)->comment('Стоимость товаров с учётом скидок');
            $table->decimal('sale_commission', 15, 2)->default(0)->comment('Комиссия за продажу');
            $table->decimal('delivery_charge', 15, 2)->default(0)->comment('Стоимость доставки');
            $table->decimal('return_delivery_charge', 15, 2)->default(0)->comment('Плата за возвраты');

            // JSON данные
            $table->json('items')->nullable()->comment('Информация о товарах в операции');
            $table->json('services')->nullable()->comment('Информация об услугах');

            // Тип транзакции
            $table->string('type')->nullable()->comment('orders, returns, services, compensation, etc.');

            // Индексы для быстрого поиска
            $table->index(['integration_id', 'operation_date']);
            $table->index(['posting_number']);
            $table->index(['operation_type']);
            $table->index(['type']);
            $table->index(['operation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_finance_transactions');
    }
}; 