<?php

namespace App\Modules\Marketplaces\Drivers;

use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\DTO\MarketplaceDto;
use App\Modules\Marketplaces\DTO\OzonMarketplaceDto;
use App\Modules\Marketplaces\Services\Ozon\Data\MarketData;
use App\Modules\Marketplaces\Services\Ozon\Data\OrderSyncSettingsData;
use App\Modules\Marketplaces\Services\Ozon\Data\PriceSyncSettingsData;
use App\Modules\Marketplaces\Services\Ozon\Data\ReportSyncSettingsData;
use App\Modules\Marketplaces\Services\Ozon\Enums\OrderNumberingTypeEnum;
use App\Modules\Marketplaces\Services\Ozon\Services\OzonService;
use Exception;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Throwable;

class OzonDriver extends MarketplaceDriver
{
    protected readonly OzonService $service;

    public function __construct()
    {
        $this->service = new OzonService();
    }

    public function create(MarketplaceDto $dto): string
    {
        if (!($dto instanceof OzonMarketplaceDto)) {
            throw new InvalidArgumentException('Invalid DTO type for Ozon driver');
        }

        try {
            return $this->service->createMarket(
                $this->getMarketData($dto)
            );
        } catch (InvalidArgumentException $e) {
            throw new InvalidArgumentException("Failed to create Ozon marketplace: " . $e->getMessage());
        }
    }

    public function show(string $id): ?object
    {
        return $this->service->getMarket($id);
    }

    public function update(MarketplaceDto $dto, string $id): void
    {
        if (!($dto instanceof OzonMarketplaceDto)) {
            throw new InvalidArgumentException('Invalid DTO type for Ozon driver');
        }

        $this->service->updateMarket($this->getMarketData($dto), $id);
    }

    public function delete(string $id): void
    {
        $this->service->deleteMarket($id);
    }

    /**
     * @param OzonMarketplaceDto $dto
     * @return MarketData
     */
    private function getMarketData(OzonMarketplaceDto $dto): MarketData
    {
        return new MarketData(
            cabinetId: $dto->cabinetId,
            shopName: $dto->shopName,
            clientId: $dto->clientId,
            apiKey: $dto->apiKey,
            legalEntityId: $dto->legalEntityId,
            contractorId: $dto->contractorId,
            comissionContractId: $dto->comissionAgreementId,
            departmentId: $dto->departmentId,
            priceSyncSettingsData: new PriceSyncSettingsData(
                yourPriceId: $dto->priceSync['your_price_id'] ?? null,
                autoSync: $dto->priceSync['auto_sync'] ?? false,
                prediscountPriceId: $dto->priceSync['prediscount_price_id'] ?? null,
                minPriceId: $dto->priceSync['min_price_id'] ?? null
            ),
            orderSyncSettingsData: new OrderSyncSettingsData(
                numType: isset($dto->orderSync['num_type']) ?
                    OrderNumberingTypeEnum::from($dto->orderSync['num_type']) :
                    OrderNumberingTypeEnum::LOCAL,
                addPrefix: $dto->orderSync['add_prefix'] ?? false,
                prefix: $dto->orderSync['prefix'] ?? 'Озон',
                useCommonBlockContract: $dto->orderSync['use_common_block_contract'] ?? false,
                reserve: $dto->orderSync['reserve'] ?? true,
                syncOrderStatuses: $dto->orderSync['sync_order_statuses'] ?? false,
                autoAcceptOrders: $dto->orderSync['auto_accept_orders'] ?? false,
                fairMark: $dto->orderSync['fair_mark'] ?? false,
                autoSync: $dto->orderSync['auto_sync'] ?? false
            ),
            reportSyncSettingsData: new ReportSyncSettingsData(
                autoSync: $dto->reportSync['auto_sync'] ?? false
            )
        );
    }

    public function connect(string $id): string
    {
        return $this->service->connect($id);
    }

    /**
     * @throws Throwable
     */
    public function loadProducts(string $id): void
    {
        $this->service->loadProducts($id);
    }

    public function loadPrices(string $id): void
    {
        $this->service->loadPrices($id);
    }

    public function loadResidues(string $id): void
    {
        $this->service->loadResidues($id);
    }

    public function loadOrders(string $id, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $this->service->loadOrders($id, $dateFrom, $dateTo);
    }

    public function loadReports(string $id, string $dateFrom, string $dateTo): void
    {
        $this->service->loadReports($id, $dateFrom, $dateTo);
    }

    public function disconnect(string $id): string
    {
        return $this->service->disconnect($id);
    }

    public function getMatchedProducts(string $cabinetId, string $integrationId): Collection
    {
        return $this->service->getMatchedProducts($cabinetId, $integrationId);
    }

    public function getProductsToMatch(string $cabinetId, string $integrationId): Collection
    {
        return $this->service->getProductsToMatch($cabinetId, $integrationId);
    }

    /**
     * @throws Exception
     */
    public function findSuggestedMatches(string $cabinetId, string $integrationId, string $matchType): Collection
    {
        return $this->service->findSuggestedMatches($cabinetId, $integrationId, $matchType);
    }

    public function confirmMatch(string $cabinetId, array $matches): void
    {
        $this->service->confirmMatch($cabinetId, $matches);
    }

    public function createProductFromMatch(string $cabinetId, string $productToMatchId): string
    {
        return $this->service->createProductFromMatch($cabinetId, $productToMatchId);
    }

    public function manualMatch(string $cabinetId, string $productToMatchId, string $productId): string
    {
        return $this->service->manualMatch($cabinetId, $productToMatchId, $productId);
    }

    public function cancelMatch(string $cabinetId, array $matchedProductIds): void
    {
        $this->service->cancelMatch($cabinetId, $matchedProductIds);
    }

    public function updateMatchedProduct(string $cabinetId, string $matchedProductId, string $productId): void
    {
        $this->service->updateMatchedProduct($cabinetId, $matchedProductId, $productId);
    }

    public function updateCostAccountingSettings(CostAccountingDto $dto, string $id): void
    {
        // TODO: Implement updateCostAccountingSettings() method.
    }
}
