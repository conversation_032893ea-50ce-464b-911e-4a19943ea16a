<?php

use App\Modules\Marketplaces\Http\Controllers\Wildberries\SelfDelivery\SelfDeliveryOrdersController;
use Illuminate\Support\Facades\Route;

Route::prefix('wildberries/self-delivery')->group(function () {
    Route::get('/{integration_id}/orders', [SelfDeliveryOrdersController::class, 'index']);
    Route::post('/{integration_id}/orders/load', [SelfDeliveryOrdersController::class, 'loadSelfDeliveryOrders']);

    Route::post('/orders/{order_id}/confirm', [SelfDeliveryOrdersController::class, 'confirmSelfDeliveryOrder']);
    Route::post('/orders/{order_id}/cancel', [SelfDeliveryOrdersController::class, 'cancelSelfDeliveryOrder']);
    Route::post('/orders/{order_id}/collect', [SelfDeliveryOrdersController::class, 'collectSelfDeliveryOrder']);
    Route::post('/orders/{order_id}/prepare', [SelfDeliveryOrdersController::class, 'prepareSelfDeliveryOrder']);
});
