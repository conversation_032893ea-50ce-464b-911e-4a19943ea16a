<?php

namespace App\Modules\Marketplaces\Policies\Wildberries;

use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\DTO\CostAccounting\WildberriesDto;
use App\Modules\Marketplaces\DTO\MarketplaceDto;
use App\Modules\Marketplaces\DTO\WildberriesMarketplaceDto;
use App\Modules\Marketplaces\Policies\BaseMarketplacePolicy;
use InvalidArgumentException;

class WildberriesPolicy extends BaseMarketplacePolicy
{
    public function create(MarketplaceDto $dto): void
    {
        if (!$dto instanceof WildberriesMarketplaceDto) {
            throw new InvalidArgumentException('Invalid DTO type for WB policy');
        }

        $this->moduleAuth->hasAccessToCabinet($dto->cabinetId);

        $this->moduleAuth->validateRelationAccess(
            'legal_entities',
            $dto->legalEntityId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'contractors',
            $dto->contractorId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'departments',
            $dto->departmentId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'contracts',
            $dto->comissionContractId,
            $dto->cabinetId
        );
    }

    public function update(MarketplaceDto $dto, string $id): void
    {
        if (!$dto instanceof WildberriesMarketplaceDto) {
            throw new InvalidArgumentException('Invalid DTO type for WB policy');
        }

        $integration = $this->moduleAuth->validateRelationAccess(
            'wildberries_integrations',
            $id
        );

        if ($dto->legalEntityId != $integration->legal_entity_id) {
            $this->moduleAuth->validateRelationAccess(
                'legal_entities',
                $dto->legalEntityId,
                $integration->cabinet_id
            );
        }

        if ($dto->comissionContractId != $integration->commission_contract_id) {
            $this->moduleAuth->validateRelationAccess(
                'contracts',
                $dto->comissionContractId,
                $integration->cabinet_id
            );
        }

        if ($dto->contractorId != $integration->contractor_id) {
            $this->moduleAuth->validateRelationAccess(
                'contractors',
                $dto->contractorId,
                $integration->cabinet_id
            );
        }

        if ($dto->departmentId != $integration->department_id) {
            $this->moduleAuth->validateRelationAccess(
                'departments',
                $dto->departmentId,
                $integration->cabinet_id
            );
        }

        if (isset($dto->priceSync['your_price_id'])) {
            $this->moduleAuth->validateRelationAccess(
                'cabinet_prices',
                $dto->priceSync['your_price_id'],
                $integration->cabinet_id
            );
        }

        if (isset($dto->priceSync['discount_price_id'])) {
            $this->moduleAuth->validateRelationAccess(
                'cabinet_prices',
                $dto->priceSync['discount_price_id'],
                $integration->cabinet_id
            );
        }
    }

    public function checkPermissionsToIntegration(string $id): void
    {
        $this->moduleAuth->validateRelationAccess(
            'wildberries_integrations',
            $id
        );
    }

    public function updateFbs(string $id, string $warehouseId): void
    {
        $fbs = $this->moduleAuth->validateRelationAccess(
            'wildberries_warehouses_fbs',
            $id
        );

        $this->moduleAuth->validateRelationAccess(
            'warehouses',
            $warehouseId,
            $fbs->cabinet_id
        );
    }

    public function updateAccountingCostSettings(CostAccountingDto $dto, string $id): void
    {
        if (!$dto instanceof WildberriesDto) {
            throw new InvalidArgumentException('Invalid DTO type for WB policy');
        }

        $integration = $this->moduleAuth->validateRelationAccess(
            'wildberries_integrations',
            $id
        );

        if ($dto->extraPaymentServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->extraPaymentServiceId,
                $integration->cabinet_id
            );
        }

        if ($dto->logisticServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->logisticServiceId,
                $integration->cabinet_id
            );
        }

        if ($dto->paidAcceptanceServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->paidAcceptanceServiceId,
                $integration->cabinet_id
            );
        }

        if ($dto->otherServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->otherServiceId,
                $integration->cabinet_id
            );
        }

        if ($dto->storageServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->storageServiceId,
                $integration->cabinet_id
            );
        }

        if ($dto->finesServiceId) {
            $this->moduleAuth->validateRelationAccess(
                'products',
                $dto->finesServiceId,
                $integration->cabinet_id
            );
        }
    }
}
