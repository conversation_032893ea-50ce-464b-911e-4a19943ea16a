<?php

namespace App\Entities;

class OzonV3PostingFbsListEntity extends BaseEntity
{
    public static string $table = 'ozon_orders';

    public static array $fields = [
        'id',
        'cabinet_id',
        'employee_id',
        'department_id',
        'ozon_company_id',
        'order_number',
        'inner_order_number',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function item(): RelationBuilder
    {
        return $this->hasMany(OzonV3PostingFbsListItemEntity::class, 'id', 'ozon_order_id');
    }

}
