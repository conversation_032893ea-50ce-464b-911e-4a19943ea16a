<?php

namespace App\Entities;

class RelationBuilder
{
    private string $type;
    private string $entityClass;
    private string $localKey;
    private string $foreignKey;
    private string $parentTable;
    private array $fields = [];
    private array $filters = [];

    public function __construct(string $type, string $entityClass, string $localKey, string $foreignKey, string $parentTable)
    {
        $this->type = $type;
        $this->entityClass = $entityClass;
        $this->localKey = $localKey;
        $this->foreignKey = $foreignKey;
        $this->parentTable = $parentTable;
    }

    public function fields(array $fields): self
    {
        $this->fields = $fields;
        return $this;
    }

    public function filter(array $filters): self
    {
        $this->filters = $filters;
        return $this;
    }

    public function build(): array
    {
        return [
            'type' => $this->type,
            'entityClass' => $this->entityClass,
            'localKey' => $this->localKey,
            'foreignKey' => $this->foreignKey,
            'fields' => $this->fields,
            'filters' => $this->filters,
        ];
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function getLocalKey(): string
    {
        return $this->localKey;
    }

    public function getForeignKey(): string
    {
        return $this->foreignKey;
    }

    public function getParentTable(): string
    {
        return $this->parentTable;
    }

    public function getFields(): array
    {
        return $this->fields;
    }

    public function getFilters(): array
    {
        return $this->filters;
    }
}
