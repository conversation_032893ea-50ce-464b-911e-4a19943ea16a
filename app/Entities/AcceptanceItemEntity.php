<?php

namespace App\Entities;

class AcceptanceItemEntity extends BaseEntity
{
    public static string $table = 'acceptance_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'acceptance_id',
        'product_id',
        'quantity',
        'price',
        'vat_rate_id',
        'discount',
        'total_price',
        'country_id',
        'gtd_number',
        'recidual'
    ];

    public function product(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }
}
