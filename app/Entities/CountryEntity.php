<?php

namespace App\Entities;

class CountryEntity extends BaseEntity
{
    public static string $table = 'countries';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'name',
        'full_name',
        'code',
        'iso2',
        'iso3',
        'employee_id',
        'department_id',
        'is_default',
        'is_common',
    ];

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
