<?php

namespace App\Entities;

class ProfitTaxRateEntity extends BaseEntity
{
    public static string $table = 'profit_tax_rates';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'rate',
        'description',
        'is_default',
    ];

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
