<?php

namespace App\Entities;

class ContractEntity extends BaseEntity
{
    public static string $table = 'contracts';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'archived_at',
        'cabinet_id',
        'number',
        'date_from',
        'legal_entity_id',
        'contractor_id',
        'currency_id',
        'code',
        'amount',
        'comment',
        'employee_id',
        'department_id',
        'status_id',
        'type',
        'is_printed',
        'is_sended',
        'shared_access'
    ];

    public function status(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id')
            ->fields(['name','color']);
    }

    public function contractor(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }
}
