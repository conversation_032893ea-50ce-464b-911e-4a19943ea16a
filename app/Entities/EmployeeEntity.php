<?php

namespace App\Entities;

class EmployeeEntity extends BaseEntity
{
    public static string $table = 'employees';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'archived_at',
        'user_id',
        'lastname',
        'firstname',
        'patronymic',
        'telephone',
        'email',
        'status',
        'job_number',
        'citizenship',
        'gender',
        'inn',
        'id_card',
        'passport_series',
        'passport_number',
        'passport_issue_date',
        'who_issued_passport',
        'division_code',
        'registration_address',
        'temporary_registration_address',
        'driver_license_series',
        'driver_license_number',
        'driver_license_issue_date',
        'driver_license_expiration_date',
        'driver_license_category',
        'military_card',
        'hire_date',
        'dismissal_date',
        'position',
        'salary',
        'labor_fund',
        'planned_advance',
        'work_schedule',
        'role_id',
        'department_id'
    ];

    public function cabinetEmployee(): RelationBuilder
    {
        return $this->hasMany(CabinetEmployeeEntity::class, 'id', 'employee_id');
    }
}
