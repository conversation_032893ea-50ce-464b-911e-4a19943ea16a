<?php

namespace App\Http\Requests\Api\Internal\CabinetSettings;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\NumberingType;
use App\Services\Api\Internal\Workspace\CabinetSettingsService\DTO\CabinetSettingsDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CabinetSettingsUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'numbering_type' => 'required|' . Rule::in(NumberingType::cases()),
            'email' => 'nullable|email',
            'global_numbering' => 'boolean',
            'use_cabinet_email' => 'boolean',
            'check_stock' => 'boolean',
            'check_min_price' => 'boolean',
            'use_bin' => 'boolean',
            'use_product_series' => 'boolean',
            'auto_update_purchase_price' => 'boolean',
            'logo_image_id' => 'nullable|UUID',
        ];
    }

    public function toDTO(): CabinetSettingsDTO
    {
        return CabinetSettingsDTO::fromArray(array_merge(
            $this->validated(),
            ['resource_id' => $this->route('cabinet')]
        ));
    }
}
