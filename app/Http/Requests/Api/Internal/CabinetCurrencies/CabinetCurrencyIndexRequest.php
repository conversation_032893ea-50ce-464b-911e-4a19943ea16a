<?php

namespace App\Http\Requests\Api\Internal\CabinetCurrencies;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\CabinetCurrencyEntity;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Contracts\Requests\ToDtoContract;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Api\Internal\FilterConditionEnum;

class CabinetCurrencyIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly CabinetCurrencyEntity $entity
    ) {
        parent::__construct();
    }
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'nullable|array',

            'filters.show_only.value' => 'string|' . Rule::in(ShowOnlyEnum::cases()),

            'filters.name.value' => 'string' . Rule::excludeIf(function () {
                $condition = $this->input('filters.name.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.name.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.name.value');
                    $condition = $this->input('filters.name.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.short_name.value' => 'string',
            'filters.num_code.value' => 'string',

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.employee_owners.value');
                $condition = $this->input('filters.employee_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.department_owners.value');
                $condition = $this->input('filters.department_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.is_common.value' => 'boolean',
            'filters.search.value' => 'string',

            'filters.updated_at.from' => 'date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',

            'fields' => 'nullable|array',
            'fields.*' => ['string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
