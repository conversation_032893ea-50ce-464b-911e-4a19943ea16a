<?php

namespace App\Http\Requests\Api\Internal\Warehouses\StorageArea;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\DTO\WarehouseStorageAreaDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseStorageAreaUpdateRequest extends FormRequest implements ToDTOContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'description' => 'nullable|string',
            'temperature_from' => 'nullable|numeric',
            'temperature_to' => 'nullable|numeric|after_or_equal:temperature_from',

            'cells_id' => 'nullable|array',
            'cells_id.*' => 'exclude_if:cells_id,null|UUID',

            'products_id' => 'nullable|array',
            'products_id.*' => 'exclude_if:products_id,null|UUID',
        ];
    }

    public function toDTO(): WarehouseStorageAreaDTO
    {
        return WarehouseStorageAreaDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
