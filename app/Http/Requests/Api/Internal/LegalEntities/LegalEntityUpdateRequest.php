<?php

namespace App\Http\Requests\Api\Internal\LegalEntities;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\LegalEntityType;
use App\Rules\PhoneNumber;
use App\Services\Api\Internal\References\LegalEntitiesService\DTO\LegalEntityDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LegalEntityUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'short_name' => 'required|string|max:100',
            'code' => 'nullable|string|max:255',
            'phone' => ['nullable', 'string', new PhoneNumber()],
            'fax' => 'nullable|string|max:255',
            'email' => 'nullable|email',
            'discount_card' => 'nullable|string|max:255',
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'logo_image_id' => 'nullable|UUID',

            'address.postcode' => 'nullable|string',
            'address.country' => 'nullable|string',
            'address.region' => 'nullable|string',
            'address.city' => 'nullable|string',
            'address.street' => 'nullable|string',
            'address.house' => 'nullable|string',
            'address.office' => 'nullable|string',
            'address.other' => 'nullable|string',
            'address.comment' => 'nullable|string',

            'head.head_name' => 'nullable|string',
            'head.head_position' => 'nullable|string',
            'head.accountant_name' => 'nullable|string',

            'head.head_signature_image_id' => 'nullable|UUID',
            'head.accountant_signature_image_id' => 'nullable|UUID',
            'head.stamp_image_id' => 'nullable|UUID',

            'detail.type' => ['required', Rule::enum(LegalEntityType::class)],
            'detail.prefix' => 'required_if:detail.type,' . LegalEntityType::LEGAL->value . '|string|max:4',
            'detail.inn' => 'nullable|string|max_digits:12',
            'detail.kpp' => 'nullable|string|max_digits:9',
            'detail.ogrn' => ['nullable', 'string', 'regex:/^(\d{13}|\d{15})$/'],
            'detail.okpo' => 'nullable|string|max_digits:10',
            'detail.full_name' => 'required_if::detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.firstname' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.patronymic' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.lastname' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.ogrnip' => 'nullable|string',
            'detail.certificate_number' => 'nullable|string',
            'detail.certificate_date' => 'nullable|date',
            'detail.taxation_type' => [Rule::enum(LegalEntityTaxation::class)],
            'detail.tax_rate' => 'nullable|uuid',
            'detail.vat_rate' => 'nullable|uuid',

            'detail.address.postcode' => 'nullable|string',
            'detail.address.country' => 'nullable|string',
            'detail.address.region' => 'nullable|string',
            'detail.address.city' => 'nullable|string',
            'detail.address.street' => 'nullable|string',
            'detail.address.house' => 'nullable|string',
            'detail.address.office' => 'nullable|string',
            'detail.address.other' => 'nullable|string',
            'detail.address.comment' => 'nullable|string',

            'accounts.*.bik' => 'nullable|string',
            'accounts.*.payment_account' => ['nullable','string', 'regex:/^[0-9]{20}$/'],
            'accounts.*.correspondent_account' => ['nullable','string', 'regex:/^[0-9]{15,34}$/'],
            'accounts.*.balance' => 'nullable|numeric',
            'accounts.*.bank' => 'nullable|string',
            'accounts.*.address' => 'nullable|string',
            'accounts.*.is_main' => 'nullable|boolean',
        ];
    }

    public function toDTO(): LegalEntityDTO
    {
        return LegalEntityDTO::fromArray(
            array_merge(
                $this->validated(),
                ['resource_id' => $this->route('id')]
            )
        );
    }
}
