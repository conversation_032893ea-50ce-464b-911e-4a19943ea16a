<?php

namespace App\Http\Requests\Api\Internal\ShipmentItems;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ShipmentItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'price' => 'nullable|integer|min:0',
            'discount' => 'nullable|integer|min:0',
            'vat_rate_id' => 'nullable|UUID',
        ];
    }

    public function toDTO(): ShipmentItemDTO
    {
        return ShipmentItemDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
