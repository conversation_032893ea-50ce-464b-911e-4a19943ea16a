<?php

namespace App\Http\Requests\Api\Internal\IncomingPayments;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\DTO\IncomingPaymentDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class IncomingPaymentUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'number' => 'nullable|string',
            'date_from' => 'nullable|date',
            'status_id' => 'nullable|UUID',
            'held' => 'nullable|boolean',
            'legal_entity_id' => 'required|UUID',
            'contractor_id' => 'required|UUID',
            'sales_channel_id' => 'nullable|UUID',
            'sum' => 'nullable|numeric',
            'included_vat' => 'nullable|numeric',
            'comment' => 'nullable|string',
            'is_common' => 'nullable|boolean',

            'currency_id' => 'required|UUID',
            'currency_value' => ['nullable', 'regex:/^\d{1,8}(\.\d{1,2})?$/'],

            'incoming_number' => 'nullable|string',
            'incoming_date' => 'nullable|date',
        ];
    }

    public function toDTO(): IncomingPaymentDTO
    {
        return IncomingPaymentDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
