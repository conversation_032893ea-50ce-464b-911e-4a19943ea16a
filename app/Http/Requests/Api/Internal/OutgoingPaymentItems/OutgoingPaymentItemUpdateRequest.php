<?php

namespace App\Http\Requests\Api\Internal\OutgoingPaymentItems;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\DTO\OutgoingPaymentItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class OutgoingPaymentItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'paid_in' => 'nullable|numeric',
        ];
    }

    public function toDTO(): OutgoingPaymentItemDTO
    {
        return OutgoingPaymentItemDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
