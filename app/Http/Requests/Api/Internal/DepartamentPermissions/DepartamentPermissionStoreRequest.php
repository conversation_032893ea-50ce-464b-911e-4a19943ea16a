<?php

namespace App\Http\Requests\Api\Internal\DepartamentPermissions;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\PermissionScopeEnum;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO\DepartmentPermissionDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DepartamentPermissionStoreRequest extends FormRequest implements ToDtoContract
{/**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'department_id' => 'required|UUID',
            'permissions.*.id' => 'nullable|UUID|exists:permissions,id',
            'permissions.*.scope' => 'nullable|' . Rule::in(PermissionScopeEnum::cases()),
        ];
    }



    public function toDTO(): DepartmentPermissionDTO
    {
        return DepartmentPermissionDTO::fromArray($this->validated());
    }
}
