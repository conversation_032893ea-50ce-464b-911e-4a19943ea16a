<?php

namespace App\Http\Requests\Api\Internal\Employees;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Entities\EmployeeEntity;
use App\Rules\ValidSortFieldRule;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class EmployeeIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly EmployeeEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'nullable|array',

            'filters.show_only.value' => 'string|' . Rule::in(ShowOnlyEnum::cases()),

            'filters.inn.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.inn.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.inn.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.inn.value');
                $condition = $this->input('filters.inn.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.phone.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.phone.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.phone.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.phone.value');
                $condition = $this->input('filters.phone.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.email.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.email.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.email.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.email.value');
                $condition = $this->input('filters.email.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.position.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.position.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.position.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.position.value');
                $condition = $this->input('filters.position.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.search.value' => 'nullable|string',

            'filters.updated_at.from' => 'nullable|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'nullable|date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
