<?php

namespace App\Http\Requests\Api\Internal\GoodsTransfer;

use App\Contracts\Requests\ToDtoContract;
use App\DTO\IndexRequestDTO;
use App\Entities\GoodsTransferEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GoodsTransferIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly GoodsTransferEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],

            'filters.to_warehouse.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.to_warehouse.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.to_warehouse.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.to_warehouse.value');
                    $condition = $this->input('filters.to_warehouse.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],
            'filters.from_warehouse.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.from_warehouse.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.from_warehouse.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.from_warehouse.value');
                    $condition = $this->input('filters.from_warehouse.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],
            'filters.in_warehouse.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.from_warehouse.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.in_warehouse.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.in_warehouse.value');
                    $condition = $this->input('filters.in_warehouse.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.legal_entity.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.legal_entity.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.legal_entity.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.legal_entity.value');
                    $condition = $this->input('filters.legal_entity.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.employee_owners.value');
                    $condition = $this->input('filters.employee_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.department_owners.value');
                    $condition = $this->input('filters.department_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.statuses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.statuses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.statuses.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.statuses.value');
                    $condition = $this->input('filters.statuses.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.is_common.value' => 'boolean',

            'filters.products.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.products.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.products.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.products.value');
                    $condition = $this->input('filters.products.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.is_held.value' => 'boolean',
            'filters.search.value' => 'string',

            'filters.period.from' => 'date_format:d.m.Y H:i',
            'filters.period.to' => 'date_format:d.m.Y H:i|after_or_equal:filters.period.from',

            'filters.updated_at.from' => 'exclude_if:filters.updated_at.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'exclude_if:filters.updated_at.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',

            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],

            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
