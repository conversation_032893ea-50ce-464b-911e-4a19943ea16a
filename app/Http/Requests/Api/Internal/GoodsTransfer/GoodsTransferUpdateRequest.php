<?php

namespace App\Http\Requests\Api\Internal\GoodsTransfer;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\GoodsTransferService\DTO\GoodsTransferDto;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class GoodsTransferUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'number' => 'nullable|string',
            'date_from' => 'nullable|date',
            'status_id' => 'nullable|UUID',
            'held' => 'nullable|boolean',
            'legal_entity_id' => 'required|UUID',
            'to_warehouse_id' => 'required|UUID',
            'from_warehouse_id' => 'required|UUID',
            'currency_id' => 'required|UUID',
            'currency_value' => ['nullable', 'regex:/^\d{1,8}(\.\d{1,2})?$/'],
            'comment' => 'nullable|string',
            'overhead_cost' => 'nullable|numeric',
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'is_common' => 'nullable|boolean',
        ];
    }

    public function toDTO(): GoodsTransferDto
    {
        return GoodsTransferDto::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
