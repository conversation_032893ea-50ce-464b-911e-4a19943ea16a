<?php

namespace App\Http\Requests\Api\Internal\CabinetInvites;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Workspace\CabinetInviteService\DTO\CabinetInviteDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CabinetInviteStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'role_id' => 'nullable|UUID',
            'email' => 'required|email',
            'department_id' => 'nullable|UUID',
        ];
    }

    public function toDTO(): CabinetInviteDTO
    {
        return CabinetInviteDTO::fromArray($this->validated());
    }
}
