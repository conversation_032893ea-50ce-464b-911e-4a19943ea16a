<?php

namespace App\Http\Requests\Api\Internal\ContractorGroups;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\DTO\ContractorGroupsDTO;
use Illuminate\Foundation\Http\FormRequest;

class ContractorGroupsUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
        ];
    }

    public function toDTO(): ContractorGroupsDTO
    {
        return ContractorGroupsDTO::fromArray(
            array_merge(
                $this->validated(),
                ['id' => $this->route('id')]
            )
        );
    }
}
