<?php

namespace App\Http\Requests\Api\Internal\Discounts;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\DiscountTypeEnum;
use App\Rules\ExclusiveFieldsRule;
use App\Rules\RequiredIfBonusProgramRule;
use App\Services\Api\Internal\References\DiscountsService\DTO\DiscountDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DiscountStoreRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',

            'cabinet_id' => 'required|UUID', // cabinet_id
            'type' => ['required', 'string', Rule::enum(DiscountTypeEnum::class)],// Тип Специальная цена, Бонусная программа, Накопительная скидка, Персональная скидка, Округление копеек
            'name' => 'required|string',                  // Название Скидки
            'status' => 'required|boolean',                // Статус

            'cabinet_price_id' => ['nullable', 'UUID', new ExclusiveFieldsRule(['cabinet_price_id', 'fixed_discount'])], // Использовать тип цен из карточки товара
            'fixed_discount'  => ['nullable', 'integer', new ExclusiveFieldsRule(['cabinet_price_id', 'fixed_discount'])],  // Использовать фиксированную скидку

            // относится к Бонусной программе RequiredIfBonusProgramRule
            'accrual_rule' => [  // Правило начисления
                'nullable',
                'integer',
                new RequiredIfBonusProgramRule(['accrual_rule', 'writeoff_rule', 'max_proc_payment', 'accrual_writeoff'])
            ],
            'writeoff_rule' => [  // Правило списания, руб, храним в копейках!
                'nullable',
                'integer',
                'max:1000000000',
                new RequiredIfBonusProgramRule(['accrual_rule', 'writeoff_rule', 'max_proc_payment', 'accrual_writeoff'])
            ],
            'max_proc_payment' => [   // Максимальный % оплаты
                'nullable',
                'integer',
                'max:100',
                new RequiredIfBonusProgramRule(['accrual_rule', 'writeoff_rule', 'max_proc_payment', 'accrual_writeoff'])
            ],
            'accrual_writeoff' => [  // Одновременное начисление и списание
                'nullable',
                'boolean',
                new RequiredIfBonusProgramRule(['accrual_rule', 'writeoff_rule', 'max_proc_payment', 'accrual_writeoff'])
            ],
            // к Бонусной программе

            'products_services' => ['nullable', 'boolean', new ExclusiveFieldsRule(['products_services', 'products'])], // Все товары и услуги - 0 Или Отдельный - 1
            'products' => ['sometimes', 'nullable', 'array', new ExclusiveFieldsRule(['products_services', 'products'])],
            'products.*.product_id'  => 'required_with:products|nullable|UUID',

            'contractors' => ['nullable', 'boolean', new ExclusiveFieldsRule(['contractors', 'contractor_groups'])],  // Все контрагенты - 0 Или Контрагенты из групп - 1
            'contractor_groups' => ['sometimes', 'nullable', 'array', new ExclusiveFieldsRule(['contractors', 'contractor_groups'])],
            'contractor_groups.*.group_id'   => 'required_with:contractor_groups|nullable|UUID',

            'savings'               => 'sometimes|nullable|array',          // для Накопительная скидка,
            'savings.*.amount'      => 'required_with:savings|nullable|integer', // храним в копейках!
            'savings.*.procent'     => 'required_with:savings|nullable|integer',

        ];
    }

    public function toDTO(): DiscountDTO
    {
        return DiscountDTO::fromArray($this->validated());
    }
}
