<?php

namespace App\Http\Requests\Api\Internal\Roles;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\PermissionScopeEnum;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\DTO\RoleDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RoleStoreRequest extends FormRequest implements ToDtoContract
{/**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => 'required|string',
            'permissions.*.id' => 'UUID|exists:permissions,id|distinct',
            'permissions.*.scope' => 'nullable|' . Rule::in(PermissionScopeEnum::cases()),
        ];
    }



    public function toDTO(): RoleDTO
    {
        return RoleDTO::fromArray($this->validated());
    }
}
