<?php

namespace App\Http\Requests\Api\Internal\CabinetPrices;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\CabinetPriceEnum;
use App\Services\Api\Internal\References\CabinetPricesService\DTO\CabinetPriceDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CabinetPriceUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'       => 'nullable|string|max:255',
            'type'        => [Rule::enum(CabinetPriceEnum::class)],
            'sort'        => 'nullable|integer',
        ];
    }

    public function toDTO(): CabinetPriceDTO
    {
        return CabinetPriceDTO::fromArray(array_merge(
            $this->validated(),
            ['resource_id' => $this->route('id')]
        ));
    }
}
