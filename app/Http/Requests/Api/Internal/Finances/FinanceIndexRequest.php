<?php

namespace App\Http\Requests\Api\Internal\Finances;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\IncomingPaymentItemEntity;
use App\Enums\Api\Internal\FinanceTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class FinanceIndexRequest extends FormRequest
{
    public function __construct(
        private readonly IncomingPaymentItemEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],

            'filters' => 'nullable|array',

            'filters.sales_channels.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.sales_channels.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.sales_channels.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.sales_channels.value');
                $condition = $this->input('filters.sales_channels.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.contractors.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractors.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractors.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.contractors.value');
                $condition = $this->input('filters.contractors.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.legals.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.legals.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.legals.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.legals.value');
                $condition = $this->input('filters.legals.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.type.value' => 'string|' . Rule::in(FinanceTypeEnum::cases()),

            'filters.without_closing_documents.value' => 'boolean',

            'filters.contractor_groups.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractor_groups.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractor_groups.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.contractor_groups.value');
                $condition = $this->input('filters.contractor_groups.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.contractor_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractor_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractor_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.contractor_owners.value');
                $condition = $this->input('filters.contractor_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.statuses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.statuses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.statuses.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.statuses.value');
                $condition = $this->input('filters.statuses.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.employee_owners.value');
                $condition = $this->input('filters.employee_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.department_owners.value');
                $condition = $this->input('filters.department_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.is_held.value' => 'boolean',
            'filters.is_common.value' => 'boolean',
            'filters.search.value' => 'string',


            'filters.period.from' => 'nullable|date_format:d.m.Y H:i',
            'filters.period.to' => 'nullable|date_format:d.m.Y H:i|after_or_equal:filters.period.from',
            'filters.updated_at.from' => 'nullable|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'nullable|date_format:d.m.Y H:i|after_or_equal:filters.period.from',
            'filters.sum.from' => 'nullable|numeric|min:0',
            'filters.sum.to' => 'nullable|numeric|min:0|after_or_equal:filters.sum.from',

            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],

            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
