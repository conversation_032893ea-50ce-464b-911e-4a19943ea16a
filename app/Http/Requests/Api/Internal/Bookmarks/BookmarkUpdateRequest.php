<?php

namespace App\Http\Requests\Api\Internal\Bookmarks;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Workspace\BookmarksService\DTO\BookmarkDto;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class BookmarkUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string'
        ];
    }

    public function toDTO(): BookmarkDto
    {
        return BookmarkDto::fromArray(
            array_merge($this->validated(), ['resource_id' => $this->route('id')])
        );
    }
}
