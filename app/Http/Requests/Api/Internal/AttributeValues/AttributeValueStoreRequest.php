<?php

namespace App\Http\Requests\Api\Internal\AttributeValues;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Goods\Other\AttributeValuesService\DTO\AttributeValueDto;
use Illuminate\Foundation\Http\FormRequest;

class AttributeValueStoreRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'attribute_id' => 'required|UUID',
            'value' => 'required|string|max:100',
        ];
    }

    public function toDTO(): AttributeValueDto
    {
        return AttributeValueDto::fromArray($this->validated());
    }
}
