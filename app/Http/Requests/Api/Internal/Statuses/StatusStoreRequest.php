<?php

namespace App\Http\Requests\Api\Internal\Statuses;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Workspace\StatusesService\DTO\StatusDTO;
use Illuminate\Foundation\Http\FormRequest;

class StatusStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'cabinet_id' => ['required', 'UUID'],
            'name' => ['required', 'string', 'max:255'],
            'color' => ['required', 'string'],
            'type_id' => ['required', 'UUID', 'exists:status_types,id'],
        ];
    }

    public function toDTO(): StatusDTO
    {
        return StatusDTO::fromArray($this->validated());
    }
}
