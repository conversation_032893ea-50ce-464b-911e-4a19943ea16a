<?php

namespace App\Http\Requests\Api\Internal\Ozon\Credentials;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\DTO\OzonCredentialsDTO;
use Illuminate\Foundation\Http\FormRequest;

class OzonCredentialsUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'name' => 'required|string|max:255',
            'api_key' => 'required|string|max:255',
            'client_id' => 'required|string|max:255',
        ];
    }

    public function toDTO(): OzonCredentialsDTO
    {
        return OzonCredentialsDTO::fromArray(
            array_merge($this->validated(), ['resource_id' => $this->route('id')])
        );
    }
}
