<?php

namespace App\Http\Requests\Api\Internal\Ozon\Warehouses;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\DTO\OzonWarehousesDTO;
use Illuminate\Foundation\Http\FormRequest;

class OzonWarehouseStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'ozon_credential_id' => 'required|uuid',
        ];
    }

    public function toDTO(): OzonWarehousesDTO
    {
        return OzonWarehousesDTO::fromArray($this->validated());
    }
}
