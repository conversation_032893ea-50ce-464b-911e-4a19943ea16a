<?php

namespace App\Http\Requests\Api\Internal\AcceptanceItems;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AcceptanceItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'price' => 'nullable|integer|min:0',
            'discount' => 'nullable|integer|min:0|max:100',
            'vat_rate_id' => 'nullable|UUID',
            'country_id' => 'nullable|UUID',
            'gtd_number' => 'nullable|string',
        ];
    }

    public function toDTO(): AcceptanceItemDto
    {
        return AcceptanceItemDto::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
