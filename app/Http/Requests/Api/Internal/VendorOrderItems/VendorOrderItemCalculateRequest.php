<?php

namespace App\Http\Requests\Api\Internal\VendorOrderItems;

use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemCalculateDTO;
use Illuminate\Foundation\Http\FormRequest;

class VendorOrderItemCalculateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'product_id' => 'required|uuid',
            'warehouse_id' => 'required|uuid',
            'date_from' => 'required|date',
            'cabinet_id' => 'required|uuid',
        ];
    }

    public function toDTO(): VendorOrderItemCalculateDTO
    {
        $validated = $this->validated();

        return new VendorOrderItemCalculateDTO(
            productId: $validated['product_id'],
            warehouseId: $validated['warehouse_id'],
            dateFrom: $validated['date_from'],
            cabinetId: $validated['cabinet_id']
        );
    }
}
