<?php

namespace App\Http\Requests\Api\Internal\RelatedDocuments;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\DTO\RelatedDocumentDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class RelatedDocumentStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'document_id' => 'required|UUID',
            'binded_document_id' => 'required|UUID',
        ];
    }

    public function toDTO(): RelatedDocumentDTO
    {
        return RelatedDocumentDTO::fromArray($this->validated());
    }
}
