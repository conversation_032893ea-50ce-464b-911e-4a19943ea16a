<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Container\Container;
use Symfony\Component\HttpFoundation\Response;
use App\Contracts\Services\AuthorizationServiceContract;
use Illuminate\Contracts\Container\BindingResolutionException;

class AuthPermissionMiddleware
{
    /**
     * @throws BindingResolutionException
     */
    public function handle(Request $request, Closure $next): Response
    {
        $authorizationService = Container::getInstance()->make(AuthorizationServiceContract::class);
        $authorizationService->init();

        return $next($request);
    }
}
