<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Http\Requests\Api\Internal\WorkSchedules\WorkScheduleIndexRequest;
use App\Http\Requests\Api\Internal\WorkSchedules\WorkScheduleStoreRequest;
use App\Http\Requests\Api\Internal\WorkSchedules\WorkScheduleUpdateRequest;
use App\Contracts\Services\Internal\Warehouses\WorkSchedulesServiceContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseWorkSchedulePolicyContract;

class WarehouseWorkScheduleController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly WorkSchedulesServiceContract $service,
        private readonly WarehouseWorkSchedulePolicyContract $policy
    ) {
    }

    public function index(WorkScheduleIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(WorkScheduleStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(WorkScheduleUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
