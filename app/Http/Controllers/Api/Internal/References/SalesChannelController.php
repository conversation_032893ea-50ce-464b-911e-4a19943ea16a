<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\SalesChannelPolicyContract;
use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Directories\SalesChannelsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\SalesChannels\SalesChannelBulkRequest;
use App\Http\Requests\Api\Internal\SalesChannels\SalesChannelIndexRequest;
use App\Http\Requests\Api\Internal\SalesChannels\SalesChannelStoreRequest;
use App\Http\Requests\Api\Internal\SalesChannels\SalesChannelUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SalesChannelController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly SalesChannelsServiceContract $service,
        private readonly SalesChannelPolicyContract $policy
    ) {
    }

    public function index(SalesChannelIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            return $this->successResponse($data);
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function store(SalesChannelStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function update(SalesChannelUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(SalesChannelBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        SalesChannelBulkRequest $request,
        ArchiveServiceContract $archiveService,
        SalesChannelsRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        SalesChannelBulkRequest $request,
        ArchiveServiceContract $archiveService,
        SalesChannelsRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    public function getTypes(SaleChannelTypesRepositoryContract $repository): JsonResponse
    {
        return $this->executeAction(function () use ($repository) {
            $data = $repository->get();
            return $this->successResponse($data);
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
