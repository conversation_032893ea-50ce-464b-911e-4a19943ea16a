<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\CabinetCurrenciesPolicyContract;
use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Directories\CabinetCurrenciesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CabinetCurrencies\CabinetCurrencyBulkRequest;
use App\Http\Requests\Api\Internal\CabinetCurrencies\CabinetCurrencyIndexRequest;
use App\Http\Requests\Api\Internal\CabinetCurrencies\CabinetCurrencySetAccoutingRequest;
use App\Http\Requests\Api\Internal\CabinetCurrencies\CabinetCurrencyStoreRequest;
use App\Http\Requests\Api\Internal\CabinetCurrencies\CabinetCurrencyUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CabinetCurrencyController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CabinetCurrenciesServiceContract $service,
        private readonly CabinetCurrenciesPolicyContract $policy
    ) {
    }

    public function index(CabinetCurrencyIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            return $this->successResponse($data);
        });
    }

    public function store(CabinetCurrencyStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeView($request, $id);
            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(CabinetCurrencyUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(CabinetCurrencyBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        CabinetCurrencyBulkRequest $request,
        ArchiveServiceContract $archiveService,
        CabinetCurrenciesRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        CabinetCurrencyBulkRequest $request,
        ArchiveServiceContract $archiveService,
        CabinetCurrenciesRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    public function setAccouting(CabinetCurrencySetAccoutingRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->setAccounting($data);

            $this->service->setAccounting($data);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
