<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\EmailVerificationRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function __invoke(EmailVerificationRequest $request): JsonResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return response()->json([
                "message" => 'Your mail already verified.',
                'errors' => [
                    'code' => ['Your mail already verified.']
                ]
            ], 422);
        }

        $data = $request->validated('code');

        $code = DB::table('email_verification_codes')
            ->where('user_id', $request->user()->id)
            ->where('code', $data)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($code) {
            $request->user()->markEmailAsVerified();

            DB::table('email_verification_codes')
                ->where('id', $code->id)
                ->delete();

            return response()->json(["message" => 'Your email has been verified.'], 200);
        }

        return response()->json([
            "message" => 'Incorrect code.',
            'errors' => [
                'code' => ['Incorrect code or code expired.']
            ]
        ], 422);
    }
}
