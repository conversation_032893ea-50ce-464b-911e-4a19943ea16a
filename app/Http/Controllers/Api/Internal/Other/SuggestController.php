<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Contracts\Services\Internal\SuggestsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Dadata\AddressRequest;
use App\Http\Requests\Api\Internal\Dadata\BankRequest;
use App\Http\Requests\Api\Internal\Dadata\FindPartyRequest;
use Illuminate\Http\JsonResponse;

class SuggestController extends Controller
{
    public function __construct(
        private readonly SuggestsServiceContract $service
    ) {
    }

    public function findParty(FindPartyRequest $request): JsonResponse
    {
        $result = $this->service->party($request->toDTO());

        return response()->json($result, 200);
    }

    public function bank(BankRequest $request): JsonResponse
    {
        $result = $this->service->bank($request->toDTO());

        return response()->json($result, 200);
    }

    public function address(AddressRequest $request): JsonResponse
    {
        $result = $this->service->address($request->toDTO());

        return response()->json($result, 200);
    }
}
