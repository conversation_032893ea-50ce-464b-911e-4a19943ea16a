<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\StatusesRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\LegalEntityType;
use App\Http\Controllers\Controller;
use App\Models\Acceptance;
use App\Models\Cabinet;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\LegalEntity;
use App\Models\LegalEntityDetail;
use App\Models\Product;
use App\Models\User;
use App\Models\UserSettings;
use App\Models\Warehouse;
use App\Services\CabinetSettingsService;
use App\Traits\HasOrderedUuid;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

ini_set('max_execution_time', 300); // 300 секунд
ini_set('max_input_time', 300);     // 300 секунд
ini_set('memory_limit', '512M');    // 512 MB памяти
class DevController extends Controller
{
    use HasOrderedUuid;

    public function __construct(
        private readonly EmployeeRepositoryContract $employeesRepository,
        private readonly CabinetEmployeeRepositoryContract $cabinetEmployeeRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepositoryContract,
        private readonly StatusesRepositoryContract $statusesRepositoryContract,
        private readonly ShipmentsRepositoryContract $shipmentsRepositoryContract,
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly SalesChannelsRepositoryContract $salesChannelsRepository
    ) {
    }

    public function fill(): JsonResponse
    {
        try {
            DB::beginTransaction();

            Log::info('Start dev controller fill');
            abort_unless(app()->environment('local'), 404);

            Artisan::call('migrate:fresh');
            Artisan::call('cache:clear');
            Artisan::call('app:fill-countries');
            Artisan::call('app:get-currencies');
            Artisan::call('app:fill-measurement-units');
            Artisan::call('app:fill-permissions');
            Artisan::call('app:fill-statuses');
            Artisan::call('db:seed', [
                '--class' => 'DatabaseSeeder',
            ]);

            $password = 123123123;
            $email = '<EMAIL>';
            $user = User::factory()->create(
                [
                    'password' => $password,
                    'email' => $email,
                    'email_verified_at' => now()
                ]
            );
            $userSettings = UserSettings::factory()->create([
                'user_id' => $user->id
            ]);

            $cabinet = Cabinet::factory()->create([
                'user_id' => $user->id
            ]);

            DB::table('cabinet_settings')
                ->insert([
                    'cabinet_id' => $cabinet->id,
                ]);

            $price = (new CabinetSettingsService())->createPriceSettings($cabinet->id);

            // $min_price = $price[0]->id;
            // $zakup_price = $price[1]->id;
            $prod_price = $price[0]->id;
            $prod_price1 = $price[1]->id;
            $prod_price2 = $price[2]->id;

            $employeeId = $this->generateUuid();

            $countryId = DB::table('countries')->inRandomOrder()->first()->id;
            $currencyId = DB::table('global_currencies')->inRandomOrder()->first()->id;

            $measurementUnitId = DB::table('measurement_units')->inRandomOrder()->first()->id;
            $measurementGroupId = DB::table('measurement_unit_groups')->inRandomOrder()->first()->id;
            $permissionId = DB::table('permissions')->inRandomOrder()->first()->id;
            $department = Department::factory()->create([
                'cabinet_id' => $cabinet->id
            ]);

            $contractorTypeId = DB::table('status_types')->where('name', 'contractors')->value('id');
            $contractorStatus = DB::table('statuses')
                ->where('type_id', $contractorTypeId)
                ->first();

            $this->employeesRepository->insert([
                'id' => $employeeId,
                'user_id' => $user->id,
                'lastname' => $user->lastname,
                'firstname' => $user->firstname,
                'patronymic' => $user->patronymic,
                'email' => $user->email,
                'department_id' => $department->id
            ]);
            $this->cabinetEmployeeRepository->insert([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
            ]);
            $contractor = Contractor::factory()->create([
                'department_id' => $department->id,
                'employee_id' => $employeeId,
                'cabinet_id' => $cabinet->id,
                'status_id' => $contractorStatus->id,
            ]);
            $warehouse = Warehouse::factory()->create([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
                'department_id' => $department->id
            ]);
            $legalEntity = LegalEntity::factory()->create([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
                'department_id' => $department->id
            ]);

            $cabinetCurrencyId = $this->generateUuid();
            $cabinetCurrency = DB::table('cabinet_currencies')
                ->insert([
                    'id' => $cabinetCurrencyId,
                    'cabinet_id' => $cabinet->id,
                    'currency_id' => $currencyId,
                    'is_accouting' => true,
                    'employee_id' => $employeeId,
                    'department_id' => $department->id
                ]);

            $legalEntityDetail = LegalEntityDetail::factory()->create([
                'legal_entity_id' => $legalEntity->id,
                'type' => LegalEntityType::INDIVIDUAL->value,
                'taxation_type' => LegalEntityTaxation::eshn->value
            ]);
            $acceptanceId = Acceptance::factory()->create([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
                'department_id' => $department->id,
                'contractor_id' => $contractor->id,
                'warehouse_id' => $warehouse->id,
                'legal_entity_id' => $legalEntity->id,
                'currency_id' => $cabinetCurrencyId
            ]);
            $product = Product::factory()->create([
                'cabinet_id' => $cabinet->id,
                'measurement_unit_id' => $measurementUnitId,
                'contractor_id' => $contractor->id,
                'country_id' => $countryId,
                'employee_id' => $employeeId,
                'department_id' => $department->id
            ]);

            $contractorGroupsId = $this->generateUuid();
            \DB::table('contractor_groups')
                ->insert([
                    'id' => $contractorGroupsId,
                    'cabinet_id' => $cabinet->id,
                    'name' => 'Название группы'
                ]);

            $category_id = DB::table('product_categories')->inRandomOrder()->first()->id;

            $vat_rate_id = $this->generateUuid();
            $this->vatRatesRepositoryContract->insert([
                'id' => $vat_rate_id,
                'cabinet_id' => $cabinet->id,
                'rate' => 0,
                'description' => 'Без НДС',
                'is_default' => false,
                'employee_id' => $employeeId,
                'department_id' => $department->id
            ]);

            $shipment_status_id = $this->generateUuid();

            $this->statusesRepositoryContract->insert([
                'id' => $shipment_status_id,
                'cabinet_id' => $cabinet->id,
                'name' => 'Status',
                'color' => 'fff',
                'type_id' => $contractorTypeId
            ]);

            $shipment_id = $this->generateUuid();

            $this->shipmentsRepositoryContract->insert([
                'id' => $shipment_id,
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
                'status_id' => $shipment_status_id,
                'currency_id' => $cabinetCurrencyId,
                'number' => 'number111',
                'date_from' => '12.12.2021',
                'held' => 1,
                'legal_entity_id' => $legalEntity->id,
                'contractor_id' => $contractor->id,
                'warehouse_id' => $warehouse->id,
                'cargo_name' => 'cargo_name',
                'shipper_instructions' => 'shipper_instructions',
                'venicle' => 'venicle',
                'venicle_number' => 'venicle_number',
                'total_seats' => 22,
                'goverment_contract_id' => 'goverment_contract_id',
                'comment' => 'comment',
                'price_includes_vat' => 1,
                'overhead_cost' => 123,
                'department_id' => $department->id,
            ]);


            $shipments_items_id = $this->generateUuid();

            $this->shipmentItemsRepository->insert([
                'id' => $shipments_items_id,
                'created_at' => now(),
                'shipment_id' => $shipment_id,
                'product_id' => $product->id,
                'quantity' => 10,
                'price' => 98,
                'discount' => 0,
                'vat_rate_id' => $vat_rate_id,
                'cost' => 300,
                'total_cost' => 500,
                'total_price' => 800,
                'profit' => 300,
            ]);


            $acceptance_status_id = $this->generateUuid();

            $this->statusesRepositoryContract->insert([
                'id' => $acceptance_status_id,
                'cabinet_id' => $cabinet->id,
                'name' => 'Status acceptance',
                'color' => 'fff',
                'type_id' => $contractorTypeId
            ]);

            $acceptance_id = $this->generateUuid();

            $this->acceptanceRepository->insert([
                'id' => $acceptance_id,
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employeeId,
                'department_id' => $department->id,
                'status_id' => $acceptance_status_id,
                'currency_id' => $cabinetCurrencyId,
                'number' => 'number111 acceptance',
                'date_from' => '12.12.2021',
                'held' => 1,
                'legal_entity_id' => $legalEntity->id,
                'contractor_id' => $contractor->id,
                'warehouse_id' => $warehouse->id,
                'incoming_number' => 'incoming_number',
                'incoming_date' => '12.12.2021',
                'comment' => 'comment',
                'price_includes_vat' => 1,
                'overhead_cost' => 123,
            ]);


            $acceptance_items_id = $this->generateUuid();

            $this->acceptanceItemsRepository->insert([
                'id' => $acceptance_items_id,
                'created_at' => now(),
                'acceptance_id' => $acceptance_id,
                'product_id' => $product->id,
                'quantity' => 10,
                'price' => 98,
                'discount' => 0,
                'vat_rate_id' => $vat_rate_id,
                'total_price' => 800,
                'country_id' => $countryId,
                'gtd_number' => 'gtd_number',
            ]);

            $salesChannelType = DB::table('sales_channel_types')
                ->first();

            $salesChannelId = $this->generateUuid();
            $this->salesChannelsRepository->insert(
                [
                    'id' => $salesChannelId,
                    'cabinet_id' => $cabinet->id,
                    'sales_channel_type_id' => $salesChannelType->id,
                    'employee_id' => $employeeId,
                    'department_id' => $department->id,
                    'name' => 'asdasd'
                ]
            );

            DB::table('measurement_unit_groups')
                ->where('is_system', false)
                ->whereNull('cabinet_id')
                ->update(['is_system' => true]);

            DB::commit();

            return response()->json([
                'password' => $password,
                'email' => $email,
                'user_id' => $user->id,
                'token' => $user->createToken('Api-token')->plainTextToken,
                'cabinet_id' => $cabinet->id,
                'prod_price' => $prod_price,
                'prod_price1' => $prod_price1,
                'prod_price2' => $prod_price2,
                'country_id' => $countryId,
                'global_currency_id' => $currencyId,
                'currency_id' => $currencyId,
                'category_id' => $category_id,
                'contractor_group_id' => $contractorGroupsId,
                'measurement_unit_id' => $measurementUnitId,
                'measurement_group_id' => $measurementGroupId,
                'permission_id' => $permissionId,
                'department_id' => $department->id,
                'status_types_id' => $contractorTypeId,
                'contractor_status_id' => $contractorStatus->id,
                'employee_id' => $employeeId,
                'contractor_id' => $contractor->id,
                'warehouse_id' => $warehouse->id,
                'legal_entity_id' => $legalEntity->id,
                'sales_channel_id' => $salesChannelId,
                // 'acceptance_id' => $acceptanceId->id,
                'product_id' => $product->id,
                'vat_rate_id' => $vat_rate_id,
                'shipment_status_id' => $shipment_status_id,
                'shipment_id' => $shipment_id,
                'shipments_items_id' => $shipments_items_id,
                'acceptance_status_id' => $acceptance_status_id,
                'acceptance_id' => $acceptance_id,
                'acceptance_items_id' => $acceptance_items_id,
                'cabinet_currency_id' => $cabinetCurrencyId
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            Log::error('Dev controller fill error', [
                'message' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Произошла ошибка при заполнении данных',
                'details' => $th->getMessage(),
                'code' => $th->getCode(),
                'line' => $th->getLine(),
            ], 500);
        }
    }


}
