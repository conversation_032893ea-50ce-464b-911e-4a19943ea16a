<?php

namespace App\Http\Controllers\Api\Internal\Ozon;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\OzonWarehousesPolicyContract;
use App\Contracts\Services\Internal\OzonWarehousesServiceContract;
use App\Http\Requests\Api\Internal\Ozon\Warehouses\OzonWarehouseStoreRequest;
use App\Http\Requests\Api\Internal\Ozon\Warehouses\OzonWarehousesIndexRequest;

class OzonV1WarehouseController extends Controller
{
  use ApiPolicy;
 
  public function __construct(
      private readonly OzonWarehousesServiceContract $service,
      private readonly OzonWarehousesPolicyContract $policy
  ) {
  }
  
   /**
   * Display a listing of the resource.
   */
  public function index(OzonWarehousesIndexRequest $request): JsonResponse
  {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
  }

  public function store(OzonWarehouseStoreRequest $request): ?JsonResponse
  {  
      return $this->executeAction(function () use ($request) {
          $data = $request->toDTO();
         
          $this->authorizeCreate($request, $data);

          $this->service->create($data);
          return $this->successResponse();
      });
  }

  /**
   * Display the specified resource.
   */
  public function show(Request $request, string $id): JsonResponse
  {
      return $this->executeAction(function () use ($request, $id) {
          $this->authorizeView($request, $id);

          $data = $this->service->show($id);
          return $this->successResponse($data);
      });
  }

   /**
   * Remove the specified resource from storage.
   */
  public function destroy(Request $request, string $id): JsonResponse
  {
      return $this->executeAction(function () use ($request, $id) {
          $this->authorizeDelete($request, $id);

          $this->service->delete($id);
          return $this->noContentResponse();
      });
  }

  protected function getPolicy(): BaseResourcePolicyContract
  {
      return $this->policy;
  }
  
}