<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Cabinet;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Cabinet\CabinetSettingsPolicyContract;
use App\Contracts\Services\Internal\Cabinet\CabinetSettingsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CabinetSettings\CabinetSettingsUpdateRequest;
use App\Http\Resources\CabinetSettingResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CabinetSettingsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CabinetSettingsServiceContract $service,
        private readonly CabinetSettingsPolicyContract $policy
    ) {
    }

    public function show(Request $request, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $cabinetId) {
            $this->authorizeView($request, $cabinetId);

            $data = $this->service->show($cabinetId);
            return $this->successResponse(
                new CabinetSettingResource($data)
            );
        });
    }

    public function update(CabinetSettingsUpdateRequest $request, string $cabinetId): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
