<?php

namespace App\Http\Controllers\Api\Internal\Sales\ComissionerReports\ReceivedComissionReports;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsRealizedItemPolicyContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportRealizedItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\Items\Realized\ReceivedComissionReportRealizedItemsIndexRequest;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\Items\Realized\ReceivedComissionReportRealizedItemStoreRequest;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\Items\Realized\ReceivedComissionReportRealizedItemUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ReceivedComissionReportsRealizedItemsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ReceivedComissionReportRealizedItemsServiceContract $service,
        private readonly ReceivedComissionReportsRealizedItemPolicyContract $policy
    ) {
    }

    public function index(ReceivedComissionReportRealizedItemsIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(ReceivedComissionReportRealizedItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(ReceivedComissionReportRealizedItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
