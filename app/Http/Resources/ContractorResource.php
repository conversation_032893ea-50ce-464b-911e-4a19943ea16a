<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Проверяем, является ли resource массивом (из репозитория) или объектом (модель)
        if (is_array($this->resource)) {
            return $this->transformFromArray($this->resource);
        }

        return $this->transformFromObject();
    }

    private function transformFromArray(array $data): array
    {
        // Если это массив с числовыми ключами (коллекция), берем первый элемент
        if (isset($data[0]) && is_array($data[0])) {
            return $this->transformFromArray($data[0]);
        }

        return [
            'id' => $data['id'] ?? null,
            'title' => $data['title'] ?? null,
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
            'deleted_at' => $data['deleted_at'] ?? null,
            'archived_at' => $data['archived_at'] ?? null,
            'cabinet_id' => $data['cabinet_id'] ?? null,
            'shared_access' => $data['shared_access'] ?? null,
            'employee_id' => $data['employee_id'] ?? null,
            'department_id' => $data['department_id'] ?? null,
            'status_id' => $data['status_id'] ?? null,
            'is_buyer' => $data['is_buyer'] ?? null,
            'is_supplier' => $data['is_supplier'] ?? null,
            'phone' => $data['phone'] ?? null,
            'fax' => $data['fax'] ?? null,
            'email' => $data['email'] ?? null,
            'description' => $data['description'] ?? null,
            'code' => $data['code'] ?? null,
            'external_code' => $data['external_code'] ?? null,
            'discounts_and_prices' => $data['discounts_and_prices'] ?? null,
            'discount_card_number' => $data['discount_card_number'] ?? null,
            'is_default' => $data['is_default'] ?? null,

            // Связанные данные
            'status' => $data['status'] ?? null,
            'detail' => $data['detail'] ?? [],
            'accounts' => $data['accounts'] ?? [],
            'address' => $data['address'] ?? [],
            'contacts' => $data['contacts'] ?? [],
            'contractor_groups' => $data['contractor_groups'] ?? [],
            'files' => $data['files'] ?? [],
        ];
    }

    private function transformFromObject(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            'archived_at' => $this->archived_at,
            'cabinet_id' => $this->cabinet_id,
            'shared_access' => $this->shared_access,
            'employee_id' => $this->employee_id,
            'department_id' => $this->department_id,
            'status_id' => $this->status_id,
            'is_buyer' => $this->is_buyer,
            'is_supplier' => $this->is_supplier,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'description' => $this->description,
            'code' => $this->code,
            'external_code' => $this->external_code,
            'discounts_and_prices' => $this->discounts_and_prices,
            'discount_card_number' => $this->discount_card_number,
            'is_default' => $this->is_default,

            // Связанные данные
            'status' => $this->status ?? null,
            'detail' => $this->detail ?? [],
            'accounts' => $this->accounts ?? [],
            'address' => $this->address ?? [],
            'contacts' => $this->contacts ?? [],
            'contractor_groups' => $this->contractor_groups ?? [],
            'files' => $this->files ?? [],
        ];
    }
}
