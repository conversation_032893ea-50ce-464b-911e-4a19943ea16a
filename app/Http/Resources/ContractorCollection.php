<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class ContractorCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Если это Collection из EntityBuilder с data и meta
        if ($this->resource instanceof \Illuminate\Support\Collection &&
            $this->resource->has('data') &&
            $this->resource->has('meta')) {

            $data = $this->resource->get('data');
            $meta = $this->resource->get('meta');

            return [
                'data' => collect($data)->map(function ($item) use ($request) {
                    return (new ContractorResource($item))->toArray($request);
                }),
                'meta' => $meta
            ];
        }

        // Если коллекция пагинирована (Laravel Paginator)
        if ($this->resource instanceof LengthAwarePaginator) {
            return [
                'data' => $this->collection->map(function ($item) use ($request) {
                    return (new ContractorResource($item))->toArray($request);
                }),
                'meta' => [
                    'current_page' => $this->resource->currentPage(),
                    'per_page' => $this->resource->perPage(),
                    'total' => $this->resource->total(),
                    'last_page' => $this->resource->lastPage(),
                ],
            ];
        }

        // Для обычной коллекции
        return [
            'data' => $this->collection->map(function ($item) use ($request) {
                return (new ContractorResource($item))->toArray($request);
            }),
        ];
    }
}
