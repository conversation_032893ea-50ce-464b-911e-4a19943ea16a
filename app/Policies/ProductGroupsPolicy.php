<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\ProductGroupsPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Goods\Products\ProductGroupsService\DTO\ProductGroupsDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class ProductGroupsPolicy implements ProductGroupsPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductGroupsDTO) {
            return;
        }

        $this->authService->init();

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductGroupsDTO) {
            return;
        }

        $this->authService->init();

        $this->authService->validateRelationAccess(
            'product_groups',
            $dto->resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->init();

        $this->authService->validateRelationAccess(
            'product_groups',
            $resourceId
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->init();

        $this->authService->validateRelationAccess(
            'product_groups',
            $resourceId
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->init();

        $this->authService->validateResourcesAccess(
            'product_groups',
            $data['cabinet_id'],
            $data['ids']
        );
    }

}
