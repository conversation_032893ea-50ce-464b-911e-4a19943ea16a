<?php

namespace App\Policies;

use App\Contracts\Policies\BinPolicyContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Contracts\Services\AuthorizationServiceContract;

readonly class BinPolicy implements BinPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId, [PermissionNameEnum::BIN->value, 'view']);
    }

    public function delete(string $cabinetId, array $ids): void
    {
        $this->authService->hasAccessToCabinet($cabinetId, [PermissionNameEnum::BIN->value, 'delete']);
        //TODO Доделать проверки на ids
    }

    public function recover(string $cabinetId, array $ids): void
    {
        $this->authService->hasAccessToCabinet($cabinetId, [PermissionNameEnum::BIN->value, 'view']);
    }
}
