<?php

namespace App\Policies;

use App\Models\User;
use App\Contracts\DtoContract;
use App\Traits\HasEmployeeAndDepartment;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Contracts\Policies\OzonV3PostingFbsListPolicyContract;

readonly class OzonV3PostingFbsListPolicy implements OzonV3PostingFbsListPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }


    /**
     * @throws \Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DtoContract) {
            return;
        }
        $this->authService->init();

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        if ($dto->ozonCredentialId) {
            $this->authService->validateRelationAccess(
                'ozon_credentials',
                $dto->ozonCredentialId,
                $dto->cabinetId
            );
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);

    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('ozon_orders', $resourceId);
    }


    public function update(User $user, DtoContract $dto): void
    {
       //
    }

    public function delete(User $user, string $resourceId): void
    {
        //
    }

    public function index(string $cabinetId): void
    {
        $this->authService->init();
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
