<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\CabinetCurrenciesPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\References\CabinetCurrenciesService\DTO\CabinetCurrencyDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class CabinetCurrenciesPolicy implements CabinetCurrenciesPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws \Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CabinetCurrencyDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, [PermissionNameEnum::CURRENCIES->value => 'create']);

        if ($dto->currencyId) {
            $this->authService->validateRelationAccess('global_currencies', $dto->currencyId);
        }

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );
    }

    /**
     * @throws \Exception
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CabinetCurrencyDTO) {
            return;
        }

        $currency = $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $dto->resourceId,
            null,
            PermissionNameEnum::CURRENCIES->value,
            'update'
        );

        if ($dto->currencyId && $currency->currency_id != $dto->currencyId) {
            $this->authService->validateRelationAccess(
                'global_currencies',
                $dto->currencyId
            );
        }

        if ($currency->employee_id != $dto->employeeId || $currency->department_id != $dto->departmentId) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $currency->cabinet_id
            );
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $resourceId,
            null,
            PermissionNameEnum::CURRENCIES->value,
            'delete'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $resourceId,
            null,
            PermissionNameEnum::CURRENCIES->value,
            'view'
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'cabinet_currencies',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CURRENCIES->value,
            'delete',
        );
    }

    public function bulkUpdate(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'cabinet_currencies',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CURRENCIES->value,
            'update',
        );
    }

    /**
     * @throws \Exception
     */
    public function setAccounting(array $data): void
    {
        $this->authService->hasAccessToCabinet(
            $data['cabinet_id'],
            [
                PermissionNameEnum::CURRENCIES->value,
                'update',
            ]
        );

        $this->checkEmployeeAndDepartmentIds(
            $data['employee_id'],
            $data['department_id'],
            $data['cabinet_id']
        );
    }
}
