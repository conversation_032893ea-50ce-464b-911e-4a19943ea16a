<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Sales\IssuedComissionReportsPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\NotFoundException;
use App\Models\User;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\DTO\IssuedComissionReportDTO;
use App\Traits\HasEmployeeAndDepartment;
use Exception;
use RuntimeException;

readonly class IssuedComissionReportsPolicy implements IssuedComissionReportsPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof IssuedComissionReportDTO) {
            throw new RuntimeException('Incorrect DTO');
        }

        $requiredPermissions = [
            PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value => 'create'
        ];

        if ($dto->isHeld) {
            $requiredPermissions[PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value] = ['create', 'held'];
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, $requiredPermissions);

        if ($dto->statusId) {
            $this->authService->validateRelationAccess(
                'statuses',
                $dto->statusId,
                $dto->cabinetId
            );
        }

        if($dto->salesChannelId){
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->salesChannelId,
                $dto->cabinetId
            );
        }

        $this->authService->validateRelationAccess(
            'contracts',
            $dto->contractId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'legal_entities',
            $dto->legalEntityId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'contractors',
            $dto->contractorId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $dto->cabinetCurrencyId,
            $dto->cabinetId
        );

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

    }

    /**
     * @throws NotFoundException
     * @throws Exception
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof IssuedComissionReportDTO) {
            throw new RuntimeException('Incorrect DTO');
        }

        $report = $this->authService->validateRelationAccess(
            'issued_comission_reports',
            $dto->id
        );

        $requiredPermissions = [
            PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value => 'update',
        ];

        if ($dto->isHeld !== $report->is_held) {
            $requiredPermissions[PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value] .= 'held';
        }

        if ($dto->cabinetCurrencyId !== $report->currency_id) {
            $requiredPermissions[PermissionNameEnum::EDIT_DOCUMENT_CURRENCY->value] = 'all';
        }

        $this->authService->hasAccessToCabinet(
            $report->cabinet_id,
            $requiredPermissions
        );

        if ($dto->statusId && $report->status_id != $dto->statusId) {
            $this->authService->validateRelationAccess(
                'statuses',
                $dto->statusId,
                $report->cabinet_id
            );
        }

        if ($dto->legalEntityId !== $report->legal_entity_id) {
            $this->authService->validateRelationAccess(
                'legal_entities',
                $dto->legalEntityId,
                $report->cabinet_id
            );
        }

        if ($dto->cabinetCurrencyId !== $report->currency_id) {
            $this->authService->validateRelationAccess(
                'cabinet_currencies',
                $dto->cabinetCurrencyId,
                $report->cabinet_id
            );
        }

        if ($dto->salesChannelId !== $report->sales_channel_id) {
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->salesChannelId,
                $report->cabinet_id
            );
        }

        if ($dto->contractId !== $report->contract_id) {
            $this->authService->validateRelationAccess(
                'contracts',
                $dto->contractId,
                $report->cabinet_id
            );
        }

        if ($dto->contractorId !== $report->contractor_id) {
            $this->authService->validateRelationAccess(
                'contractors',
                $dto->contractorId,
                $report->cabinet_id
            );
        }

        if (
            $report->employee_id != $dto->employeeId
            ||
            $report->department_id != $dto->departmentId
        ) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $report->cabinet_id
            );
        }
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'issued_comission_reports',
            $resourceId,
            null,
            PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'view'
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'issued_comission_reports',
            $resourceId,
            null,
            PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'delete'
        );
    }
}
