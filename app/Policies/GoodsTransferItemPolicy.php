<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\GoodsTransferItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\GoodsTransferItemService\DTO\GoodsTransferItemDto;

readonly class GoodsTransferItemPolicy implements GoodsTransferItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof GoodsTransferItemDto) {
            return;
        }

        $acceptance = $this->authService->validateRelationAccess(
            'goods_transfers',
            $dto->goodsTransferId,
            null,
            PermissionNameEnum::MOVEMENTS->value,
            'update',
        );

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $acceptance->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );

        if ($product->archived_at) {
            throw new AccessDeniedException('Product is archived');
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof GoodsTransferItemDto) {
            return;
        }

        $this->authService->validateRelationAccess(
            'goods_transfer_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::MOVEMENTS->value,
            'update',
            null,
            [
                'table' => 'goods_transfers',
                'field' => 'goods_transfer_id',
                'value' => $dto->resourceId
            ]
        );
    }

    public function index(string $acceptanceId): void
    {
        $this->authService->validateRelationAccess(
            'goods_transfers',
            $acceptanceId,
            null,
            PermissionNameEnum::MOVEMENTS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'goods_transfer_items',
            $resourceId,
            null,
            PermissionNameEnum::MOVEMENTS->value,
            'view',
            null,
            [
                'table' => 'goods_transfers',
                'field' => 'goods_transfer_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'goods_transfer_items',
            $resourceId,
            null,
            PermissionNameEnum::MOVEMENTS->value,
            'update',
            null,
            [
                'table' => 'goods_transfers',
                'field' => 'goods_transfer_id',
                'value' => $resourceId
            ]
        );
    }
}
