<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Sales\ComissionReportsPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\ContractService\DTO\ContractDTO;
use App\Traits\HasEmployeeAndDepartment;
use RuntimeException;

readonly class ComissionReportsPolicy implements ComissionReportsPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws \Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ContractDTO) {
            throw  new RuntimeException('Incorrect DTO');
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, [PermissionNameEnum::CONTRACTORS->value => 'create']);

        $this->authService->validateRelationAccess(
            'legal_entities',
            $dto->legalEntityId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'contractors',
            $dto->contractorId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $dto->currencyId,
            $dto->cabinetId
        );

        if ($dto->statusId) {
            $this->authService->validateRelationAccess(
                'statuses',
                $dto->statusId,
                $dto->cabinetId
            );
        }

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId,
        );

    }

    /**
     * @throws \Exception
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ContractDTO) {
            return;
        }

        $contract = $this->authService->validateRelationAccess(
            'contracts',
            $dto->resourceId,
            null,
            PermissionNameEnum::CONTRACTORS->value,
            'update'
        );

        $this->authService->hasEntityPermission($contract, PermissionNameEnum::CONTRACTORS->value, 'update');

        if ($dto->statusId && $contract->status_id != $dto->statusId) {
            $this->authService->validateRelationAccess('statuses', $dto->statusId, $contract->cabinet_id);
        }

        if ($dto->employeeId != $contract->employee_id || $dto->departmentId != $contract->department_id) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $contract->cabinet_id
            );
        }

        if ($dto->legalEntityId !== $contract->legal_entity_id) {
            $this->authService->validateRelationAccess(
                'legal_entities',
                $dto->legalEntityId,
                $contract->cabinet_id
            );
        }

        if ($dto->contractorId !== $contract->contractor_id) {
            $this->authService->validateRelationAccess(
                'contractors',
                $dto->contractorId,
                $contract->cabinet_id
            );
        }

        if ($dto->currencyId !== $contract->currency_id) {
            $this->authService->validateRelationAccess(
                'cabinet_currencies',
                $dto->currencyId,
                $contract->cabinet_id
            );
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'contracts',
            $resourceId,
            null,
            PermissionNameEnum::CONTRACTORS->value,
            'delete'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'contracts',
            $resourceId,
            null,
            PermissionNameEnum::CONTRACTORS->value,
            'view'
        );
    }

    public function bulkCopy(array $data): void
    {
        $this->authService->hasAccessToCabinet($data['cabinet_id'], [
            PermissionNameEnum::CONTRACTORS->value => 'create',
        ]);

        $this->authService->validateResourcesAccess(
            'contracts',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CONTRACTORS->value,
            'view',
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'contracts',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CONTRACTORS->value,
            'delete',
        );
    }

    public function bulkUpdate(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'contracts',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CONTRACTORS->value,
            'update',
        );
    }
}
