<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\VatRatePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\References\VatRatesService\DTO\VatRateDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class VatRatePolicy implements VatRatePolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof VatRateDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id, [PermissionNameEnum::VAT_RATES->value => 'create']);
        $this->checkEmployeeAndDepartmentIds(
            $dto->employee_id,
            $dto->department_id,
            $dto->cabinet_id
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof VatRateDTO) {
            return;
        }

        $vatRate = $this->authService->validateRelationAccess(
            'vat_rates',
            $dto->id,
            null,
            PermissionNameEnum::VAT_RATES->value,
            'update',
        );

        $this->checkEmployeeAndDepartmentIds(
            $dto->employee_id,
            $dto->department_id,
            $vatRate->cabinet_id
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'vat_rates',
            $resourceId,
            null,
            PermissionNameEnum::VAT_RATES->value,
            'delete',
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'vat_rates',
            $resourceId,
            null,
            PermissionNameEnum::VAT_RATES->value,
            'view',
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'vat_rates',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::VAT_RATES->value,
            'delete',
        );
    }

    public function bulkUpdate(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'vat_rates',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::VAT_RATES->value,
            'update',
        );
    }
}
