<?php

namespace App\Services\Storage;

use Illuminate\Support\Facades\Storage;

class S3StorageService
{
    private const PRIVATE_DISK = 's3-docs';
    private const PUBLIC_DISK = 's3-images';

    public function store(mixed $contents, bool $isPrivate = false): string
    {
        $disk = $isPrivate ? self::PRIVATE_DISK : self::PUBLIC_DISK;
        $path = $isPrivate ? 'docs' : 'images';

        return Storage::disk($disk)->putFile('', $contents);
    }

    public function getUrl(string $path, bool $isPrivate = false): string
    {
        $disk = $isPrivate ? self::PRIVATE_DISK : self::PUBLIC_DISK;

        if ($isPrivate) {
            return Storage::disk($disk)->temporaryUrl(
                $path,
                now()->addMinutes(5)
            );
        }

        return Storage::disk($disk)->url($path);
    }
}
