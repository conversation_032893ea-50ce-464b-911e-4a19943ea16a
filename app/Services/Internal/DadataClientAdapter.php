<?php

namespace App\Services\Internal;

use Dadata\Settings;
use Dadata\DadataClient;
use GuzzleHttp\Exception\GuzzleException;
use App\Contracts\Services\Internal\DadataClientInterface;

class DadataClientAdapter implements DadataClientInterface
{
    private DadataClient $client;

    public function __construct(string $token, string $secret)
    {
        $this->client = new DadataClient($token, $secret);
    }
    /**
     * @throws GuzzleException
     */
    public function suggest(mixed $name, mixed $query, mixed $count = Settings::SUGGESTION_COUNT, mixed $kwargs = []): array
    {
        return $this->client->suggest($name, $query, $count, $kwargs);
    }
}
