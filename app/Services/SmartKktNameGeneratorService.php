<?php

namespace App\Services;

/**
 * Интеллектуальный сервис генерации наименований для кассовых чеков (БСО)
 * Создает информативные сокращения для длинных наименований товаров
 */
class SmartKktNameGeneratorService
{
    /**
     * Максимальная длина тега наименования по ФФД (Тег 1030)
     */
    public const MAX_NAME_LENGTH = 128;

    /**
     * Генерирует наименование товара для кассового чека
     * с учётом ограничения в 128 символов
     *
     * @param string $fullName Полное наименование товара
     * @return string Наименование для кассового чека
     */
    public function generateReceiptName(string $fullName): string
    {
        // Если наименование не превышает лимит, возвращаем как есть
        $cleanName = $this->cleanString($fullName);

        if (mb_strlen($cleanName) <= self::MAX_NAME_LENGTH) {
            return $cleanName;
        }

        // В случае превышения лимита, создаём интеллектуальное сокращение
        return $this->createSmartName($cleanName);
    }

    /**
     * Очищает строку от лишних пробелов и символов
     *
     * @param string $string Исходная строка
     * @return string Очищенная строка
     */
    private function cleanString(string $string): string
    {
        // Удаляем лишние пробелы и спецсимволы
        $string = trim($string);
        // Заменяем множественные пробелы на один
        $string = preg_replace('/\s+/', ' ', $string);
        // Удаляем управляющие символы
        return preg_replace('/[\x00-\x1F\x7F]/', '', $string);
    }

    /**
     * Создаёт интеллектуальное сокращение наименования товара
     *
     * @param string $name Очищенное наименование товара
     * @return string Сокращенное наименование
     */
    private function createSmartName(string $name): string
    {
        // Разбиваем название на части
        $parts = $this->parseNameParts($name);

        // Если после парсинга название уже короткое, возвращаем его
        if (mb_strlen($parts['short_name']) <= self::MAX_NAME_LENGTH) {
            return $parts['short_name'];
        }

        // Сокращаем описательные части, сохраняя основное наименование
        $result = $this->constructOptimalName($parts);

        // Если всё ещё превышает лимит, обрезаем с умом
        if (mb_strlen($result) > self::MAX_NAME_LENGTH) {
            $result = $this->finalTruncate($result);
        }

        return $result;
    }

    /**
     * Разбирает наименование на смысловые части
     *
     * @param string $name Полное название
     * @return array Массив с выделенными частями
     */
    private function parseNameParts(string $name): array
    {
        $result = [
            'brand' => '',
            'main_name' => '',
            'characteristics' => [],
            'details' => [],
            'short_name' => ''
        ];

        // Попытка выделить бренд (обычно в начале или после первого слова)
        $words = explode(' ', $name);

        // Основное наименование (первые 2-4 слова обычно самые важные)
        $mainNameWords = array_slice($words, 0, min(4, count($words)));
        $result['main_name'] = implode(' ', $mainNameWords);

        // Поиск ключевых характеристик (размеры, объемы, проценты)
        preg_match_all('/([\d,.]+\s*(?:кг|г|мл|л|см|мм|м|%|шт))/', $name, $matches);
        if (!empty($matches[0])) {
            $result['characteristics'] = $matches[0];
        }

        // Проверка на наличие артикула или кода
        preg_match('/(?:арт\.|артикул|код)[\s:]*([\w\d-]+)/iu', $name, $artMatch);
        if (!empty($artMatch[1])) {
            $result['details'][] = 'арт. ' . $artMatch[1];
        }

        // Создаем короткое название из основных частей
        $shortName = $result['main_name'];

        // Добавляем ключевые характеристики
        if (!empty($result['characteristics'])) {
            $shortName .= ' ' . implode(' ', array_slice($result['characteristics'], 0, 2));
        }

        // Добавляем артикул если есть
        if (!empty($result['details'])) {
            $shortName .= ' ' . implode(' ', $result['details']);
        }

        $result['short_name'] = $this->cleanString($shortName);

        return $result;
    }

    /**
     * Конструирует оптимальное наименование, сохраняя наиболее важную информацию
     *
     * @param array $parts Разобранные части наименования
     * @return string Сконструированное наименование
     */
    private function constructOptimalName(array $parts): string
    {
        // Начинаем с основного наименования
        $result = $parts['main_name'];
        $remainingLength = self::MAX_NAME_LENGTH - mb_strlen($result);

        // Добавляем ключевые характеристики (размер, вес и т.д.)
        if (!empty($parts['characteristics']) && $remainingLength > 10) {
            $chars = '';
            foreach ($parts['characteristics'] as $char) {
                if (mb_strlen($chars . ' ' . $char) <= $remainingLength) {
                    $chars .= ($chars ? ' ' : '') . $char;
                } else {
                    break;
                }
            }

            if ($chars) {
                $result .= ' ' . $chars;
                $remainingLength = self::MAX_NAME_LENGTH - mb_strlen($result);
            }
        }

        // Добавляем артикул, если остается место
        if (!empty($parts['details']) && $remainingLength > 5) {
            $details = '';
            foreach ($parts['details'] as $detail) {
                if (mb_strlen($details . ' ' . $detail) <= $remainingLength) {
                    $details .= ($details ? ' ' : '') . $detail;
                } else {
                    break;
                }
            }

            if ($details) {
                $result .= ' ' . $details;
            }
        }

        return $this->cleanString($result);
    }

    /**
     * Выполняет финальное обрезание, если все методы сокращения не помогли
     *
     * @param string $name Предварительно сокращенное название
     * @return string Финальное название, не превышающее лимит
     */
    private function finalTruncate(string $name): string
    {
        if (mb_strlen($name) <= self::MAX_NAME_LENGTH) {
            return $name;
        }

        // Обрезаем до последнего целого слова
        $mainPartLength = self::MAX_NAME_LENGTH - 3; // -3 для "..."
        $mainPart = mb_substr($name, 0, $mainPartLength);

        $lastSpacePos = mb_strrpos($mainPart, ' ');
        if ($lastSpacePos !== false) {
            $mainPart = mb_substr($mainPart, 0, $lastSpacePos);
        }

        return $mainPart . '...';
    }
}