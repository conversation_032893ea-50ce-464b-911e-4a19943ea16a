<?php

namespace App\Services\Api\RateLimiter;

use Illuminate\Support\Facades\Log;

class RateLimiterService
{
    /**
     * @var array Массив конфигураций рейт-лимитеров для разных типов запросов
     */
    private array $limiters = [];

    /**
     * @var array Массив счетчиков запросов для разных типов запросов
     */
    private array $requestCounts = [];

    /**
     * @var array Массив времени начала отсчета для разных типов запросов
     */
    private array $startTimes = [];

    /**
     * @var array Массив счетчиков запросов с ошибкой 409 для разных типов запросов
     */
    private array $error409Counts = [];

    /**
     * @var bool Флаг тестового режима
     */
    private bool $isTest;

    /**
     * Конструктор сервиса
     *
     * @param bool $isTest Флаг тестового режима
     */
    public function __construct(bool $isTest = false)
    {
        $this->isTest = $isTest;

        // Инициализируем стандартные конфигурации рейт-лимитеров
        $this->initDefaultLimiters();
    }

    /**
     * Инициализация стандартных конфигураций рейт-лимитеров
     */
    private function initDefaultLimiters(): void
    {
        // Лимитер для методов категории Маркетплейс (300 запросов в минуту)
        $this->registerLimiter('marketplace', 300, 60);

        // Лимитер для методов схемы Самовывоз (300 запросов в минуту)
        $this->registerLimiter('self_delivery', 300, 60);

        // Лимитер для методов цен (10 запросов за 6 секунд)
        $this->registerLimiter('prices', 10, 6);

        // Лимитер для методов контента (100 запросов в минуту)
        $this->registerLimiter('content', 100, 60);

        // Лимитер для методов статистики (4 запроса в секунду)
        $this->registerLimiter('statistics', 4, 1);

        // Лимитер для методов остатков Ozon (80 запросов в минуту)
        $this->registerLimiter('ozon_stocks', 80, 60);

        // Лимитер для методов финансов Ozon (40 запросов в минуту)
        $this->registerLimiter('ozon_finance', 40, 60);
    }

    /**
     * Регистрация нового рейт-лимитера
     *
     * @param string $type Тип запроса
     * @param int $maxRequests Максимальное количество запросов
     * @param int $timeWindow Временное окно в секундах
     * @param int $error409Weight Вес запроса с ошибкой 409 (по умолчанию 5)
     * @return void
     */
    public function registerLimiter(string $type, int $maxRequests, int $timeWindow, int $error409Weight = 5): void
    {
        $this->limiters[$type] = [
            'maxRequests' => $maxRequests,
            'timeWindow' => $timeWindow,
            'error409Weight' => $error409Weight
        ];

        $this->requestCounts[$type] = 0;
        $this->startTimes[$type] = microtime(true);
        $this->error409Counts[$type] = 0;
    }

    /**
     * Обработка запроса с учетом рейт-лимита
     *
     * @param string $type Тип запроса
     * @param bool $isError409 Флаг ошибки 409
     * @return void
     */
    public function throttle(string $type): void
    {
        if (!isset($this->limiters[$type])) {
            // Если лимитер не зарегистрирован, используем лимитер для маркетплейса по умолчанию
            $type = 'marketplace';
        }

        // Увеличиваем счетчик запросов
        $this->requestCounts[$type]++;

        // Получаем конфигурацию лимитера
        $limiter = $this->limiters[$type];

        // Вычисляем эффективное количество запросов с учетом ошибок 409
        $effectiveRequestCount = $this->requestCounts[$type] + ($this->error409Counts[$type] * ($limiter['error409Weight'] - 1));

        // Проверяем, не превысили ли мы лимит запросов
        if ($effectiveRequestCount >= $limiter['maxRequests']) {
            $elapsedTime = microtime(true) - $this->startTimes[$type];

            if ($elapsedTime < $limiter['timeWindow']) {
                // Если мы сделали максимальное количество запросов быстрее, чем за отведенное время, ждем оставшееся время
                $sleepTime = ceil($limiter['timeWindow'] - $elapsedTime);

                Log::info("Rate limit reached for {$type}, sleeping for {$sleepTime} seconds", [
                    'type' => $type,
                    'requestCount' => $this->requestCounts[$type],
                    'error409Count' => $this->error409Counts[$type],
                    'effectiveRequestCount' => $effectiveRequestCount,
                    'maxRequests' => $limiter['maxRequests'],
                    'timeWindow' => $limiter['timeWindow'],
                    'elapsedTime' => $elapsedTime,
                    'sleepTime' => $sleepTime
                ]);

                sleep($sleepTime);
            }

            // Сбрасываем счетчики
            $this->requestCounts[$type] = 0;
            $this->error409Counts[$type] = 0;
            $this->startTimes[$type] = microtime(true);
        }

        // Если включен тестовый режим, добавляем задержку в 1 секунду между запросами
        if ($this->isTest) {
            sleep(1);
        }
    }

    /**
     * Регистрация запроса с ошибкой 409
     *
     * @param string $type Тип запроса
     * @return void
     */
    public function registerError409(string $type): void
    {
        if (!isset($this->limiters[$type])) {
            // Если лимитер не зарегистрирован, используем лимитер для маркетплейса по умолчанию
            $type = 'marketplace';
        }

        $this->error409Counts[$type]++;
    }

    /**
     * Сброс счетчиков для указанного типа запросов
     *
     * @param string $type Тип запроса
     * @return void
     */
    public function reset(string $type): void
    {
        if (isset($this->requestCounts[$type])) {
            $this->requestCounts[$type] = 0;
            $this->error409Counts[$type] = 0;
            $this->startTimes[$type] = microtime(true);
        }
    }

    /**
     * Сброс всех счетчиков
     *
     * @return void
     */
    public function resetAll(): void
    {
        foreach (array_keys($this->requestCounts) as $type) {
            $this->reset($type);
        }
    }
}
