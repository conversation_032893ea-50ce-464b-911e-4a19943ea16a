<?php

namespace App\Services\Api\RateLimiter\Facades;

use Illuminate\Support\Facades\Facade;
use App\Services\Api\RateLimiter\RateLimiterService;

/**
 * @method static void throttle(string $type)
 * @method static void registerError409(string $type)
 * @method static void registerLimiter(string $type, int $maxRequests, int $timeWindow, int $error409Weight = 5)
 * @method static void reset(string $type)
 * @method static void resetAll()
 * 
 * @see \App\Services\Api\RateLimiter\RateLimiterService
 */
class RateLimiter extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return RateLimiterService::class;
    }
}
