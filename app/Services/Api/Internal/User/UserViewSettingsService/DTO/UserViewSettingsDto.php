<?php

namespace App\Services\Api\Internal\User\UserViewSettingsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class UserViewSettingsDto implements DtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public string $employeeId;

    public function __construct(
        public string $cabinetId,
        public array $settings,
        public string $name,
        public int $userId
    ) {
    }

    public function toUpdateArray(): array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => $this->cabinetId,
            'settings' => json_encode($this->settings),
            'name' => $this->name,
            'employee_id' => $this->employeeId
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'],
            settings: $data['settings'],
            name: $data['name'],
            userId: $data['user_id']
        );
    }
}
