<?php

namespace App\Services\Api\Internal\User\UserViewSettingsService\Handlers;

use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\UserViewSettingsRepositoryContract;
use Illuminate\Support\Collection;

readonly class UserViewSettingsGetHandler
{
    public function __construct(
        private UserViewSettingsRepositoryContract $repository,
        private EmployeeRepositoryContract $employeeRepository
    ) {
    }

    public function run(string $cabinetId, int $userId, ?string $name = null): Collection
    {
        $employeeId = $this->employeeRepository->getByUserIdAndCabinet(
            $userId,
            $cabinetId
        )->id;

        return $this->repository->get($employeeId, $name);
    }
}
