<?php

namespace App\Services\Api\Internal\Other\ExcelService;

use App\Services\Api\Internal\ExcelService\TValue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ExportExcelContractorsService
{
    public function exportExcelContractors(string $cabinetId): mixed
    {

        $where = " cntr.cabinet_id = '".$cabinetId."' ";

        $sortPrice = DB::table('contractors as cntr')
        ->leftJoin('contractors_addresses as cntr_adrs', 'cntr_adrs.contractor_id', '=', 'cntr.id')
        // ->leftJoin('contractors_accounts as cntr_acc', 'cntr_acc.contractor_id', '=', 'cntr.id')
        // ->leftJoin('contractors_contacts as cntr_contc', 'cntr_contc.contractor_id', '=', 'cntr.id')
        ->leftJoin('contractors_details as cntr_dtl', 'cntr_dtl.contractor_id', '=', 'cntr.id')
        ->leftJoin('contractors_detail_addresses as cntr_dtl_adrs', 'cntr_dtl_adrs.contractors_detail_id', '=', 'cntr_dtl.id')
        ->leftJoin('statuses as cntr_st', 'cntr_st.id', '=', 'cntr.status_id') // cabinet_id
        ->leftJoin('contractor_group as cntr_gr', 'cntr_gr.contractor_id', '=', 'cntr.id')
        ->leftJoin('groups as gr', 'cntr_gr.group_id', '=', 'gr.id')
        ->select(
            'cntr.id                    as cntr_id',
            'cntr.cabinet_id                    as cntr_cabinet_id',
            'cntr.title                     as cntr_title',
            'cntr.status_id                  as cntr_contractor_status_id',
            'cntr.departament_id                    as cntr_departament_id',
            'cntr.buyer                     as cntr_buyer',
            'cntr.supplier                  as cntr_supplier',
            'cntr.tel                   as cntr_tel',
            'cntr.fax                   as cntr_fax',
            'cntr.email                     as cntr_email',
            'cntr.description                   as cntr_description',
            'cntr.code                  as cntr_code',
            'cntr.external_code                     as cntr_external_code',
            'cntr.discounts_and_prices                  as cntr_discounts_and_prices',
            'cntr.discount_card_number                  as cntr_discount_card_number',
            'cntr.employee                  as cntr_employee',
            'cntr.shared_access                     as cntr_shared_access',
            'cntr.deleted_at                    as cntr_deleted_at',
            'cntr.created_at                    as cntr_created_at',
            'cntr.updated_at                    as cntr_updated_at',
            'cntr_adrs.postcode                     as cntr_adrs_postcode',
            'cntr_adrs.country                  as cntr_adrs_country',
            'cntr_adrs.region                   as cntr_adrs_region',
            'cntr_adrs.city                     as cntr_adrs_city',
            'cntr_adrs.street                   as cntr_adrs_street',
            'cntr_adrs.house                    as cntr_adrs_house',
            'cntr_adrs.office                   as cntr_adrs_office',
            'cntr_adrs.other                    as cntr_adrs_other',
            'cntr_adrs.comment                  as cntr_adrs_comment',
            'cntr_dtl.taxation_type                     as cntr_dtl_taxation_type',
            'cntr_dtl.tax_rate                  as cntr_dtl_tax_rate',
            'cntr_dtl.vat_rate                  as cntr_dtl_vat_rate',
            'cntr_dtl.type                  as cntr_dtl_type',
            'cntr_dtl.inn                   as cntr_dtl_inn',
            'cntr_dtl.kpp                   as cntr_dtl_kpp',
            'cntr_dtl.ogrn                  as cntr_dtl_ogrn',
            'cntr_dtl.okpo                  as cntr_dtl_okpo',
            'cntr_dtl.full_name                     as cntr_dtl_full_name',
            'cntr_dtl.firstname                     as cntr_dtl_firstname',
            'cntr_dtl.patronymic                    as cntr_dtl_patronymic',
            'cntr_dtl.lastname                  as cntr_dtl_lastname',
            'cntr_dtl.ogrnip                    as cntr_dtl_ogrnip',
            'cntr_dtl.certificate_number                    as cntr_dtl_certificate_number',
            'cntr_dtl.certificate_date                  as cntr_dtl_certificate_date',
            'cntr_dtl_adrs.postcode                     as cntr_dtl_adrs_postcode',
            'cntr_dtl_adrs.country                  as cntr_dtl_adrs_country',
            'cntr_dtl_adrs.region                   as cntr_dtl_adrs_region',
            'cntr_dtl_adrs.city                     as cntr_dtl_adrs_city',
            'cntr_dtl_adrs.street                   as cntr_dtl_adrs_street',
            'cntr_dtl_adrs.house                    as cntr_dtl_adrs_house',
            'cntr_dtl_adrs.office                   as cntr_dtl_adrs_office',
            'cntr_dtl_adrs.other                    as cntr_dtl_adrs_other',
            'cntr_dtl_adrs.comment                  as cntr_dtl_adrs_comment',
            'cntr_st.title                  as cntr_st_title',
            DB::raw("STRING_AGG(DISTINCT gr.title, ', ') AS contractor_groups"),

            // 'cntr_dtl_adrs.',
        )
        ->where('cntr.cabinet_id', $cabinetId)
        ->groupBy([
            'cntr.id',
            'cntr.cabinet_id',
            'cntr.title',
            'cntr.status_id',
            'cntr.departament_id',
            'cntr.buyer',
            'cntr.supplier',
            'cntr.tel',
            'cntr.fax',
            'cntr.email',
            'cntr.description',
            'cntr.code',
            'cntr.external_code',
            'cntr.discounts_and_prices',
            'cntr.discount_card_number',
            'cntr.employee',
            'cntr.shared_access',
            'cntr.deleted_at',
            'cntr.created_at',
            'cntr.updated_at',
            'cntr_adrs.postcode',
            'cntr_adrs.country',
            'cntr_adrs.region',
            'cntr_adrs.city',
            'cntr_adrs.street',
            'cntr_adrs.house',
            'cntr_adrs.office',
            'cntr_adrs.other',
            'cntr_adrs.comment',
            'cntr_dtl.taxation_type',
            'cntr_dtl.tax_rate',
            'cntr_dtl.vat_rate',
            'cntr_dtl.type',
            'cntr_dtl.inn',
            'cntr_dtl.kpp',
            'cntr_dtl.ogrn',
            'cntr_dtl.okpo',
            'cntr_dtl.full_name',
            'cntr_dtl.firstname',
            'cntr_dtl.patronymic',
            'cntr_dtl.lastname',
            'cntr_dtl.ogrnip',
            'cntr_dtl.certificate_number',
            'cntr_dtl.certificate_date',
            'cntr_dtl_adrs.postcode',
            'cntr_dtl_adrs.country',
            'cntr_dtl_adrs.region',
            'cntr_dtl_adrs.city',
            'cntr_dtl_adrs.street',
            'cntr_dtl_adrs.house',
            'cntr_dtl_adrs.office',
            'cntr_dtl_adrs.other',
            'cntr_dtl_adrs.comment',
            'cntr_st.title',
        ]);

        $contractors_accounts = DB::table('contractors_accounts as cntr_acc')
        ->select(
            'cntr_acc.contractor_id',
            DB::raw('COALESCE(JSON_AGG(JSON_BUILD_OBJECT(\'cntr_acc_id\', cntr_acc.id, \'cntr_contractor_id\', cntr_acc.contractor_id,  \'is_main\', cntr_acc.is_main, \'cntr_acc_bik\', cntr_acc.bik,
            \'cntr_acc_cor_acc\', cntr_acc.correspondent_account, \'cntr_acc_paym_acc\', cntr_acc.payment_account,
            \'cntr_acc_balance\', cntr_acc.balance,  \'cntr_acc_bank\', cntr_acc.bank, \'cntr_acc_address\', cntr_acc.address) ORDER BY cntr_acc.created_at ), \'[]\') as cntr_accounts')
        )
        ->groupBy('cntr_acc.contractor_id');

        $contractors_contacts = DB::table('contractors_contacts as cntr_contc')
        ->select(
            'cntr_contc.contractor_id',
            DB::raw('COALESCE(JSON_AGG(JSON_BUILD_OBJECT(\'cntr_contc_id\',  cntr_contc.id, \'cntr_contractor_id\', cntr_contc.contractor_id,  \'full_name\', cntr_contc.full_name, \'position\', cntr_contc.position,
            \'phone\', cntr_contc.phone, \'email\', cntr_contc.email,
            \'comment\', cntr_contc.comment) ORDER BY cntr_contc.created_at ), \'[]\') as cntr_contacts')
        )
        ->groupBy('cntr_contc.contractor_id');

        $contractors_temp = DB::table(DB::raw("({$sortPrice->toSql()}) as cntrs"))
            ->mergeBindings($sortPrice)
            ->leftJoin(DB::raw("({$contractors_accounts->toSql()}) as cntr_acc"), 'cntrs.cntr_id', '=', 'cntr_acc.contractor_id')
            ->mergeBindings($contractors_accounts)
            ->leftJoin(DB::raw("({$contractors_contacts->toSql()}) as cntr_cont"), 'cntrs.cntr_id', '=', 'cntr_cont.contractor_id')
            ->mergeBindings($contractors_contacts)
            ->orderByDesc('cntrs.cntr_created_at')
        ->get();

        // dd($contractors_temp);

        $contractors['sheet'][0]['data']['contractors'] = new Collection($contractors_temp);

        /**
        * @param TValue $item
        */
        $contractors['sheet'][0]['data']['contractors'] = $contractors['sheet'][0]['data']['contractors']->map(function ($item) {
            $item->cntr_accounts = json_decode($item->cntr_accounts, true);
            $item->cntr_contacts = json_decode($item->cntr_contacts, true);
            return $item;
        });

        foreach ($contractors['sheet'][0]['data']['contractors'] as $contractor) {

            // foreach ($contractor->cntr_accounts as $k => $account) {
            //     $contractor->{'is_main.'.$account['cntr_acc_id']} = $account['is_main'];
            //     $contractor->{'cntr_acc_bik.'.$account['cntr_acc_id']} = $account['cntr_acc_bik'];
            //     $contractor->{'cntr_acc_cor_acc.'.$account['cntr_acc_id']} = $account['cntr_acc_cor_acc'];
            //     $contractor->{'cntr_acc_paym_acc.'.$account['cntr_acc_id']} = $account['cntr_acc_paym_acc'];
            //     $contractor->{'cntr_acc_balance.'.$account['cntr_acc_id']} = $account['cntr_acc_balance'];
            //     $contractor->{'cntr_acc_bank.'.$account['cntr_acc_id']} = $account['cntr_acc_bank'];
            //     $contractor->{'cntr_acc_address.'.$account['cntr_acc_id']} = $account['cntr_acc_address'];
            // }

            foreach ($contractor->cntr_accounts as $k => $account) {

                $contractors['sheet'][1]['data']['cntr_accounts'][$k] = [
                    'cntr_acc_id' => $account['cntr_acc_id'],
                    'cntr_contractor_id' => $account['cntr_contractor_id'],
                    'cntr_title' => $contractor->cntr_title,
                    'is_main' => $account['is_main'],
                    'cntr_acc_bik' => $account['cntr_acc_bik'],
                    'cntr_acc_cor_acc' => $account['cntr_acc_cor_acc'],
                    'cntr_acc_paym_acc' => $account['cntr_acc_paym_acc'],
                    'cntr_acc_balance' => $account['cntr_acc_balance'],
                    'cntr_acc_bank' => $account['cntr_acc_bank'],
                    'cntr_acc_address' => $account['cntr_acc_address'],
                ];

            }



            unset($contractor->cntr_accounts);

            // foreach ($contractor->cntr_contacts as $k => $contact) {
            //     $contractor->{'full_name.'.$contact['cntr_contc_id']} = $contact['full_name'];
            //     $contractor->{'position.'.$contact['cntr_contc_id']} = $contact['position'];
            //     $contractor->{'phone.'.$contact['cntr_contc_id']} = $contact['phone'];
            //     $contractor->{'email.'.$contact['cntr_contc_id']} = $contact['email'];
            //     $contractor->{'comment.'.$contact['cntr_contc_id']} = $contact['comment'];
            // }

            foreach ($contractor->cntr_contacts as $k => $account) {

                $contractors['sheet'][2]['data']['cntr_contacts'][$k] = [
                    'cntr_contc_id' => $account['cntr_contc_id'],
                    'cntr_contractor_id' => $account['cntr_contractor_id'],
                    'cntr_title' => $contractor->cntr_title,
                    'full_name' => $account['full_name'],
                    'position' => $account['position'],
                    'phone' => $account['phone'],
                    'email' => $account['email'],
                    'comment' => $account['comment'],
                ];

            }


            unset($contractor->cntr_contacts);


            // LegalEntityTaxation LegalEntityType
            //     $product->paf_packing = PackTypeInProductAccountingEnum::from($product->paf_packing)->getStatus();
            //     $product->paf_type_accounting = TypeAccountingEnum::from($product->paf_type_accounting)->getTypeAccounting();
            //     $product->type = TypeProductEnum::from($product->type)->getTypeProduct();
            //     $product->paf_target_gender = TargetGenderEnum::from($product->paf_target_gender)->getTargetGender();
            //     $product->paf_type_production = TypeProductionEnum::from($product->paf_type_production)->getTypeProduction();
            //     $product->paf_age_category = AgeCategoryEnum::from($product->paf_age_category)->getAgeCategory();

        }

        // dd($contractors);
        return $contractors;

    }

}
