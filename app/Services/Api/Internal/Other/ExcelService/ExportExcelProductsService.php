<?php

namespace App\Services\Api\Internal\Other\ExcelService;

use App\Enums\Api\Internal\AgeCategoryEnum;
use App\Enums\Api\Internal\PackTypeInProductAccountingEnum;
use App\Enums\Api\Internal\TargetGenderEnum;
use App\Enums\Api\Internal\TypeAccountingEnum;
use App\Enums\Api\Internal\TypeProductEnum;
use App\Enums\Api\Internal\TypeProductionEnum;
use App\Services\Api\Internal\ExcelService\TValue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ExportExcelProductsService
{
    public function exportExcelProducts(string $cabinetId): mixed
    {

        $where = " p.cabinet_id = '" . $cabinetId . "' ";

        $sortPrice = DB::table('products as p')
            ->leftJoin('product_categories as ca', 'p.category_id', '=', 'ca.id')
            ->leftJoin('measurement_units as mu', 'p.measurement_unit_id', '=', 'mu.id')
            ->leftJoin('product_egais_codes as peg', 'p.id', '=', 'peg.product_id')
            ->leftJoin('product_accounting_features as paf', 'p.id', '=', 'paf.product_id')
            ->leftJoin('packings as pk', 'p.id', '=', 'pk.product_id')
            ->leftJoin('brands as brands', 'p.brand_id', '=', 'brands.id')
            ->leftJoin('contractors as co', 'co.id', '=', 'p.contractor_id')
            ->leftJoin('countries as cntr', 'cntr.id', '=', 'p.country_id')
            ->leftJoin('product_images as pi', 'pi.product_id', '=', 'p.id')
            ->leftJoin(
                DB::raw('(SELECT barcodes.id, barcodes.barcodable_id, barcodes.barcode, ROW_NUMBER() OVER (PARTITION BY barcodes.barcodable_id ORDER BY barcodes.sort ASC) as row_num_barcod
                  FROM barcodes) as barcod'),
                function ($join) {
                    $join->on('pk.id', '=', 'barcod.barcodable_id')
                        ->orOn('p.id', '=', 'barcod.barcodable_id');
                }
            )
            ->select(
                'p.id',
                'pi.url as image',
                'p.title',
                'p.description',
                'p.type',
                DB::raw("STRING_AGG(DISTINCT p.article, ', ') AS product_articles"),
                DB::raw("STRING_AGG(DISTINCT pk.article, ', ') AS packing_articles"),
                'p.code',
                'p.inner_code',
                'p.external_code',
                'p.discounts_retail_sales',
                'p.short_description',
                'cntr.name as country',
                'p.length',
                'p.width',
                'p.height',
                'p.weight',
                'p.volume',
                'p.tax',
                'p.tax_system',
                'p.indication_subject_calculation',
                'mu.name as measurement_units_name',
                DB::raw("STRING_AGG(DISTINCT barcod.barcode, ', ')  AS barcode"),
                'paf.packing as paf_packing',
                'paf.type_accounting as paf_type_accounting',
                'paf.accounting_series as paf_accounting_series',
                'paf.product_siz_name_id as paf_product_siz_name_id',
                'paf.product_siz_type_id as paf_product_siz_type_id',
                'paf.product_type_code as paf_product_type_code',
                'paf.container_capacity as paf_container_capacity',
                'paf.fortress as paf_fortress',
                'paf.excise as paf_excise',
                'paf.tnwed as paf_tnwed',
                'paf.target_gender as paf_target_gender',
                'paf.type_production as paf_type_production',
                'paf.age_category as paf_age_category',
                'paf.set as paf_set',
                'paf.partial_sale as paf_partial_sale',
                'paf.model as paf_model',
                'paf.traceable as paf_traceable',
                DB::raw("STRING_AGG(DISTINCT peg.code , ', ') AS peg_code"),
                'brands.name as brands',
                'p.created_at',
                'p.updated_at',
                'p.group',
                'co.title as co_title',
                'ca.name as category_name'
            )
            ->whereRaw($where)
            ->groupBy([
                'p.id',
                'pi.url',
                'p.title',
                'p.description',
                'p.type',
                'p.code',
                'p.inner_code',
                'p.external_code',
                'p.discounts_retail_sales',
                'p.short_description',
                'p.country_id',
                'cntr.name',
                'p.length',
                'p.width',
                'p.height',
                'p.weight',
                'p.volume',
                'p.tax',
                'p.tax_system',
                'p.indication_subject_calculation',
                'mu.name',
                'paf.packing',
                'paf.type_accounting',
                'paf.accounting_series',
                'paf.product_siz_name_id',
                'paf.product_siz_type_id',
                'paf.product_type_code',
                'paf.container_capacity',
                'paf.fortress',
                'paf.excise',
                'paf.tnwed',
                'paf.target_gender',
                'paf.type_production',
                'paf.age_category',
                'paf.set',
                'paf.partial_sale',
                'paf.model',
                'paf.traceable',
                'brands.name',
                'p.created_at',
                'p.updated_at',
                'p.group',
                'ca.name',
                'co.title'
            ]);

        $productPricesData = DB::table('product_prices as pp')
            ->leftJoin('cabinet_prices as cp', function ($join) use ($cabinetId) {
                $join->on('cp.id', '=', 'pp.cabinet_price_id')
                    ->where('cp.cabinet_id', $cabinetId);
            })
            ->leftJoin('global_currencies as cc', 'pp.global_currency_id', '=', 'cc.id')
            ->select(
                'pp.product_id',
                'cc.char_code as char_code',
                DB::raw('COALESCE(JSON_AGG(JSON_BUILD_OBJECT( \'cp_id\', cp.id, \'cp_title\', cp.name, \'amount\', pp.amount, \'char_code\', cc.char_code) ORDER BY cp.sort asc ), \'[]\') as product_prices')
            )
            ->groupBy('pp.product_id', 'cc.char_code');

        $productAttributesData = DB::table('product_attributes as p_atr')
            ->leftJoin('attributes as atr', 'atr.id', '=', 'p_atr.attribute_id')
            ->leftJoin('attribute_groups as atr_gr', 'atr_gr.id', '=', 'atr.attribute_groups_id')
            ->leftJoin('attribute_values as atr_val', 'atr_val.attribute_id', '=', 'atr.id')
            ->select(
                'p_atr.product_id',
                DB::raw('COALESCE(JSON_AGG(JSON_BUILD_OBJECT(\'atr_id\', atr_val.id, \'atr_name\', atr.name, \'atr_val\', atr_val.value, \'atr_gr\', atr_gr.name) ORDER BY atr.name ), \'[]\') as product_attribute')
            )
            ->groupBy('p_atr.product_id');

        $products_temp = DB::table(DB::raw("({$sortPrice->toSql()}) as pd"))
            ->mergeBindings($sortPrice)
            ->leftJoin(DB::raw("({$productPricesData->toSql()}) as ppr"), 'pd.id', '=', 'ppr.product_id')
            ->mergeBindings($productPricesData)
            ->leftJoin(DB::raw("({$productAttributesData->toSql()}) as pat"), 'pd.id', '=', 'pat.product_id')
            ->mergeBindings($productAttributesData)
            ->orderByDesc('pd.created_at')
            // ->limit(100)
            // ->offset(0)
            ->get();


        $products['sheet'][0]['data']['products'] = new Collection($products_temp);

        /**
         * @param TValue $item
         */
        $products['sheet'][0]['data']['products'] = $products['sheet'][0]['data']['products']->map(function ($item) {
            $item->product_prices = json_decode($item->product_prices, true);
            $item->product_attribute = json_decode($item->product_attribute, true);
            return $item;
        });


        foreach ($products['sheet'][0]['data']['products'] as $product) {

            if ($product->product_prices !== null) {
                foreach ($product->product_prices as $k => $price) {
                    $product->{'price_amount.' . $price['cp_id']} = $price['amount'];
                    $product->{'price_char_code.' . $price['cp_id']} = $price['char_code'];
                }
            }

            unset($product->product_prices);

            if ($product->product_attribute !== null) {
                foreach ($product->product_attribute as $k => $attribute) {
                    $product->{'attribute.' . $attribute['atr_id']} = $attribute['atr_val'];
                }
            }

            unset($product->product_attribute);

            $product->paf_packing = ($product->paf_packing !== null) ? PackTypeInProductAccountingEnum::from($product->paf_packing)->getStatus() : null;
            $product->paf_type_accounting = ($product->paf_type_accounting !== null) ? TypeAccountingEnum::from($product->paf_type_accounting)->getTypeAccounting() : null;
            $product->type = ($product->type !== null) ? TypeProductEnum::from($product->type)->getTypeProduct() : null;
            $product->paf_target_gender = ($product->paf_target_gender !== null) ? TargetGenderEnum::from($product->paf_target_gender)->getTargetGender() : null;
            $product->paf_type_production = ($product->paf_type_production !== null) ? TypeProductionEnum::from($product->paf_type_production)->getTypeProduction() : null;
            $product->paf_age_category = ($product->paf_age_category !== null) ? AgeCategoryEnum::from($product->paf_age_category)->getAgeCategory() : null;
        }

        $products['sheet'][0]['data']['priceNames'] = DB::table('cabinet_prices')
            ->where('cabinet_id', $cabinetId)
            ->select('id', 'name') // Нужно приводить к единому выводу атрибутов id и name
            ->distinct()
            ->get();

        $products['sheet'][0]['data']['attributes'] = DB::table('attributes as atr')
            ->leftJoin('attribute_values as atr_val', 'atr_val.attribute_id', '=', 'atr.id')
            ->where('cabinet_id', $cabinetId)
            ->select('atr_val.id', 'atr.name as name') // Нужно приводить к единому выводу атрибутов id и name
            ->distinct()
            ->get();

        return $products;
    }
}
