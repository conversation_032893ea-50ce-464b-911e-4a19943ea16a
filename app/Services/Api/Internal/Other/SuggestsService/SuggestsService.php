<?php

namespace App\Services\Api\Internal\Other\SuggestsService;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\SuggestsServiceContract;
use App\Services\Api\Internal\Other\SuggestsService\Handlers\SuggestAddressHandler;
use App\Services\Api\Internal\Other\SuggestsService\Handlers\SuggestBankHandler;
use App\Services\Api\Internal\Other\SuggestsService\Handlers\SuggestPartyHandler;

readonly class SuggestsService implements SuggestsServiceContract
{
    public function __construct(
        private SuggestAddressHandler $addressHandler,
        private SuggestBankHandler $bankHandler,
        private SuggestPartyHandler $partyHandler,
    ) {
    }

    public function address(DtoContract $dto): array|null|string
    {
        return $this->addressHandler->run($dto);
    }
    public function bank(DtoContract $dto): array|null|string
    {
        return $this->bankHandler->run($dto);
    }

    public function party(DtoContract $dto): array|string|null
    {
        return $this->partyHandler->run($dto);
    }
}
