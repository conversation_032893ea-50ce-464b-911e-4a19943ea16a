<?php

namespace App\Services\Api\Internal\Other\SuggestsService\DTO;

use App\Contracts\DtoContract;

class SuggestAddressDTO implements DtoContract
{
    public function __construct(
        public string $query,
        public ?string $language,
        public ?string $division,
        public ?array $locations,
        public ?array $locationsGeo,
        public ?array $locationsBoost,
        public ?array $fromBound,
        public ?array $toBound
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            query: $data['query'],
            language: $data['language'] ?? null,
            division: $data['division'] ?? null,
            locations: $data['locations'] ?? null,
            locationsGeo: $data['locations_geo'] ?? null,
            locationsBoost: $data['locations_boost'] ?? null,
            fromBound: $data['from_bound'] ?? null,
            toBound: $data['to_bound'] ?? null
        );
    }
}
