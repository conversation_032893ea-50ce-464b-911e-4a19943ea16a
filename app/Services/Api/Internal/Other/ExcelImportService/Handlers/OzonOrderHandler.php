<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Contracts\Repositories\OzonOrdersRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Illuminate\Http\UploadedFile;

class OzonOrderHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        protected OzonGetDataFromFileHandler $ozonGetDataFromFileHandler,
        protected OzonOrderNumberChunksHandler $ozonOrderNumberChunksHandler,
        protected OzonOrderItemsChunksHandler $ozonOrderItemsChunksHandler,
        private OzonOrdersRepositoryContract $ozonOrdersRepositoryContract,
    ) {
    }

    public function run(UploadedFile $file, string $cabinetId, string $departmentId, string $employeeId): void
    {

        $soldAmount = $this->ozonGetDataFromFileHandler->run($file);

        $this->ozonOrderNumberChunksHandler->run($soldAmount, $cabinetId, $departmentId, $employeeId);

        $orderIds = $this->ozonOrdersRepositoryContract->getAllOrderIds($cabinetId); // Метод для получения всех записей вида [inner_posting_number => id]

        $this->ozonOrderItemsChunksHandler->run($soldAmount, $orderIds);

    }

}
