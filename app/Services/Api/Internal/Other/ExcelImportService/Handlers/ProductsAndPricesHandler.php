<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Contracts\Repositories\OzonProductsRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductsAndPricesHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    private string $resourceId;

    public function __construct(
        private OzonProductsRepositoryContract $ozonProductsRepositoryContract,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(Worksheet $sheet, string $cabinetId, string $departmentId, string $employeeId): void
    {
        // Список ожидаемых колонок
        $expectedColumns = [
            'Артикул' => 'offer_id', //+
            'Ozon SKU ID' => 'sku',//+
            'Название' => 'name',//+
            'Статус' => 'state_name',//+
            'Видимость на OZON' => 'visible',//+
            'На складе Ozon' => 'stocks_fbo_coming',//+
            'На моих складах' => 'stocks_fbs_present',//+
            'Объем, л' => 'volume_in_litres',//+
            'Объемный вес, кг' => 'volume_weight',//+
            'Штрихкод' => 'barcode',//+
            'Цена до скидки, руб.' => 'old_price',//+
            'Текущая цена (со скидкой), руб.' => 'price',//+
            'Скидка, %' => 'discount_percent',// ??------------
            'Скидка, руб.' => 'discount_rub',// ??--------------
            'Цена с учетом акции или стратегии, руб.' => 'marketing_seller_price',//+
            'Скидка с учетом акции, %' => 'discount_with_action_percent',// ??----------
            'Скидка с учетом акции, руб.' => 'discount_with_action_rub',// ??---------------
            'НДС, %' => 'vat',//+
            'Минимальная цена, руб.' => 'min_price',//+
            'Настройка стратегии ценообразования «Удерживайте выгодную цену»' => 'strategy_price_setting',// ??---------
            'Подключение подходящих акций' => 'promotion_connection',// ??---------
            'Рыночная цена на Ozon, руб.' => 'market_price_ozon',// ??----------
            'Рыночная цена конкурентов, руб.' => 'ozon_index_data__price_index_value', // ??-------------
            'Моя цена на других площадках, руб.' => 'self_marketplaces_index_data__price_index_value',//+
            'Ссылка на рыночную цену конкурентов' => 'competitors_price_link',// ??-------------
            'Ссылка на рыночную цену на мои товары' => 'my_price_link',// ??----------
            'Индекс цен' => 'price_indexes__price_index',//+
            'Ценовой индекс товара на Ozon' => 'price_indexes__ozon_index_data__price_index_value',//+ ??----------
            'Ценовой индекс товара на рынке' => 'price_indexes__external_index_data__price_index_value',//+ ??----------
            'Ценовой индекс товара на рынке на мои товары' => 'price_indexes__self_marketplaces_index_data__price_index_value',//+ ??--------
            'Эквайринг' => 'acquiring',//+
            'Вознаграждение Ozon, FBO, %' => 'sales_percent_fbo',
            'Логистика Ozon, минимум, FBO' => 'fbo_direct_flow_trans_min_amount',
            'Логистика Ozon, максимум, FBO' => 'fbo_direct_flow_trans_max_amount',
            'Последняя миля, FBO' => 'fbo_deliv_to_customer_amount',
            'Вознаграждение Ozon, FBS, %' => 'sales_percent_fbs',
            'Обработка отправления, минимум FBS' => 'fbs_first_mile_min_amount',
            'Обработка отправления, максимум FBS' => 'fbs_first_mile_max_amount',
            'Логистика Ozon, минимум, FBS' => 'fbs_direct_flow_trans_min_amount',
            'Логистика Ozon, максимум, FBS' => 'fbs_direct_flow_trans_max_amount',
            'Последняя миля, FBS' => 'fbs_deliv_to_customer_amount',
        ];

        // Получение заголовков колонок из 3 строки
        $headers = [];
        $headerRow = $sheet->getRowIterator(3, 3)->current();

        foreach ($headerRow->getCellIterator() as $cell) {
            $headers[] = trim($cell->getValue());
        }

        // Сопоставление заголовков с ожидаемыми колонками
        $columnMapping = [];
        foreach ($expectedColumns as $expectedHeader => $columnKey) {
            $columnIndex = array_search($expectedHeader, $headers);
            if ($columnIndex !== false) {
                $columnMapping[$columnKey] = Coordinate::stringFromColumnIndex($columnIndex + 1);
            }
        }

        // Чтение данных и присвоение значений
        $data = [];
        $highestRow = $sheet->getHighestRow();
        for ($row = 5; $row <= $highestRow; $row++) {
            $rowData = [];
            foreach ($columnMapping as $columnKey => $columnLetter) {
                $rowData[$columnKey] = $sheet->getCell($columnLetter . $row)->getValue();
            }
            if (!empty(array_filter($rowData))) {
                $data[] = $rowData;
            }
        }


        $data = new Collection($data);

        // Разбиваем коллекцию на чанки по 1000 элементов
        $chunks = $data->chunk(1000);

        $chunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId) {
            $records = $chunk->map(function ($item) use ($cabinetId, $departmentId, $employeeId) {

                if (!isset($item['id'])) {
                    $item['id'] = $this->generateUuid();
                }

                $item['old_price'] = $this->rublesInKopeck($item['old_price']);
                $item['price'] = $this->rublesInKopeck($item['price']);
                $item['marketing_seller_price'] = $this->rublesInKopeck($item['marketing_seller_price']);
                $item['acquiring'] = $this->rublesInKopeck($item['acquiring']);
                $item['fbo_direct_flow_trans_min_amount'] = $this->rublesInKopeck($item['fbo_direct_flow_trans_min_amount']);
                $item['fbo_direct_flow_trans_max_amount'] = $this->rublesInKopeck($item['fbo_direct_flow_trans_max_amount']);
                $item['fbo_deliv_to_customer_amount'] = $this->rublesInKopeck($item['fbo_deliv_to_customer_amount']);
                $item['fbs_first_mile_min_amount'] = $this->rublesInKopeck($item['fbs_first_mile_min_amount']);
                $item['fbs_first_mile_max_amount'] = $this->rublesInKopeck($item['fbs_first_mile_max_amount']);
                $item['fbs_direct_flow_trans_min_amount'] = $this->rublesInKopeck($item['fbs_direct_flow_trans_min_amount']);
                $item['fbs_direct_flow_trans_max_amount'] = $this->rublesInKopeck($item['fbs_direct_flow_trans_max_amount']);
                $item['fbs_deliv_to_customer_amount'] = $this->rublesInKopeck($item['fbs_deliv_to_customer_amount']);

                unset(
                    $item['volume_in_litres'],
                    $item['stocks_fbo_coming'],
                    $item['stocks_fbs_present'],
                    $item['discount_percent'],
                    $item['min_price'],
                    $item['discount_rub'],
                    $item['discount_with_action_percent'],
                    $item['discount_with_action_rub'],
                    $item['vat'],
                    $item['strategy_price_setting'],
                    $item['promotion_connection'],
                    $item['market_price_ozon'],
                    $item['ozon_index_data__price_index_value'],
                    $item['self_marketplaces_index_data__price_index_value'],
                    $item['competitors_price_link'],
                    $item['my_price_link'],
                    $item['price_indexes__price_index'],
                    $item['price_indexes__ozon_index_data__price_index_value'],
                    $item['price_indexes__external_index_data__price_index_value'],
                    $item['price_indexes__self_marketplaces_index_data__price_index_value']
                );

                $item['cabinet_id'] = $cabinetId;
                $item['department_id'] = $departmentId;
                $item['employee_id'] = $employeeId;
                $item['created_at'] = now();
                $item['updated_at'] = now();

                return $item;

            })->toArray();

            //TODO ещё в разработке
            // SaveOzonProductsAndPricesJob::dispatch($records);
            $this->ozonProductsRepositoryContract->upsert($records);
        });
    }

}
