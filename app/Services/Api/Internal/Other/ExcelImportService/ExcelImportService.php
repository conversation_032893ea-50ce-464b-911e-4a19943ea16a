<?php

namespace App\Services\Api\Internal\Other\ExcelImportService;

use App\Contracts\Services\Internal\ExcelImportServiceContract;
use App\Services\Api\Internal\Other\ExcelImportService\DTO\ExcelImportDTO;
use App\Services\Api\Internal\Other\ExcelImportService\Handlers\ExcelImportHandler;
use Exception;

readonly class ExcelImportService implements ExcelImportServiceContract
{
    public function __construct(
        private ExcelImportHandler $importHandler,
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(ExcelImportDTO $dto): void
    {
        $this->importHandler->run($dto);
    }

}
