<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemCalculateDTO;
use Illuminate\Support\Collection;

readonly class AcceptanceItemGetHandler
{
    public function __construct(
        private AcceptanceItemsRepositoryContract $shipmentItemsRepository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->shipmentItemsRepository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции приемки
     *
     * @param AcceptanceItemCalculateDTO $dto
     * @return object
     */
    public function calculateMetrics(AcceptanceItemCalculateDTO $dto): object
    {
        return $this->shipmentItemsRepository->calculateMetricsForNewItem(
            $dto->productId,
            $dto->warehouseId,
            $dto->dateFrom,
            $dto->cabinetId
        );
    }
}
