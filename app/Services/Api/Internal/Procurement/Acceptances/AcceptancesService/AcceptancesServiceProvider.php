<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService;

use Illuminate\Support\ServiceProvider;

class AcceptancesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $migrationsPath = __DIR__ . '/migrations';
        $this->loadMigrationsFrom($migrationsPath);
    }
}
