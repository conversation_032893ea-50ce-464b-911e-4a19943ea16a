<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Jobs\BulkHandleFifoJob;

use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use RuntimeException;

class AcceptanceBulkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly DocumentsRepositoryContract $documentsRepository,
        private readonly CabinetSettingRepositoryContract $cabinetSettingRepository
    ) {
    }

    /**
     * Массовое удаление приемок
     *
     * @param array $resourceIds Массив ID приемок для удаления
     * @throws BindingResolutionException
     */
    public function run(array $resourceIds): void
    {
        // Получаем все приемки одним запросом
        $acceptances = $this->acceptanceRepository->whereIn('id', $resourceIds);

        if ($acceptances->isEmpty()) {
            throw new RuntimeException('Acceptances not found in handler');
        }

        // Получаем все настройки кабинетов одним запросом
        $cabinetId = $acceptances->take(1)->value('cabinet_id');
        $cabinetSettings = $this->cabinetSettingRepository->show($cabinetId);

        if (!$cabinetSettings) {
            throw new RuntimeException('Cabinet settings not found in handler');
        }
        if (!$cabinetSettings->use_bin) {
            $this->documentsRepository->deleteWhereDocumentableIdIn($resourceIds);
        }

        // Получаем все элементы приемок одним запросом
        $acceptanceItems = $this->acceptanceItemsRepository->whereIn('acceptance_id', $resourceIds);

        // Получаем все связанные элементы отгрузки одним запросом
        $shipmentItems = $this->acceptanceRepository->getFirstShipmentItemForAllProductsBulk($resourceIds);

        DB::transaction(function () use ($resourceIds, $acceptances, $acceptanceItems, $cabinetSettings, $shipmentItems) {
            // Массовое удаление приемок
            $this->acceptanceRepository->bulkDelete($resourceIds);

            // Группируем элементы приемки по cabinet_id
            $itemsByCabinet = $acceptanceItems->groupBy(function ($item) use ($acceptances) {
                return $acceptances->firstWhere('id', $item->acceptance_id)->cabinet_id;
            });

            // Обрабатываем элементы приемки
            foreach ($itemsByCabinet as $cabinetId => $items) {
                if (!$cabinetSettings->use_bin) {
                    // Массовое удаление элементов приемки
                    $this->acceptanceItemsRepository->bulkDelete($items->pluck('id')->toArray());
                }
            }

            // Обработка FIFO для всех затронутых отгрузок
            if ($shipmentItems->isNotEmpty()) {
                Queue::push(new BulkHandleFifoJob($shipmentItems));
            }
        });
    }
}
