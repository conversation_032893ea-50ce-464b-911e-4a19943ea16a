<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers;

use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Jobs\BulkRecalculationAfterUpdateAcceptanceJob;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

class AcceptanceBulkHeldHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(array $resourceIds): void
    {
        $oldResources = $this->acceptanceRepository->whereIn('id', $resourceIds);

        DB::table('acceptances')
            ->whereIn('id', $resourceIds)
            ->update([
                'held' => true,
                'updated_at' => now()
            ]);

        $newResources = $this->acceptanceRepository->whereIn('id', $resourceIds);

        Queue::push(new BulkRecalculationAfterUpdateAcceptanceJob($oldResources, $newResources));
    }
}
