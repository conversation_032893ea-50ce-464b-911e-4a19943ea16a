<?php

namespace App\Services\Api\Internal\Workspace\BookmarksService\Handlers;

use App\Contracts\Repositories\BookmarkRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use RuntimeException;

readonly class BookmarkGetHandler
{
    public function __construct(
        private BookmarkRepositoryContract $acceptanceRepository,
        private EmployeeRepositoryContract $employeeRepository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $employee = $this->employeeRepository->getByUserIdAndCabinet(Auth::id(), $dto->id);
        if (!$employee) {
            throw new RuntimeException('Employee not found');
        }
        $dto->filters['employee_id'] = $employee->id;

        return $this->acceptanceRepository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
