<?php

namespace App\Services\Api\Internal\Workspace\BookmarksService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\BookmarksServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Workspace\BookmarksService\Handlers\BookmarkCreateHandler;
use App\Services\Api\Internal\Workspace\BookmarksService\Handlers\BookmarkDeleteHandler;
use App\Services\Api\Internal\Workspace\BookmarksService\Handlers\BookmarkGetHandler;
use App\Services\Api\Internal\Workspace\BookmarksService\Handlers\BookmarkShowHandler;
use App\Services\Api\Internal\Workspace\BookmarksService\Handlers\BookmarkUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class BookmarksService implements BookmarksServiceContract
{
    public function __construct(
        private BookmarkCreateHandler $createHandler,
        private BookmarkGetHandler $getHandler,
        private BookmarkShowHandler $showHandler,
        private BookmarkUpdateHandler $updateHandler,
        private BookmarkDeleteHandler $deleteHandler,
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
