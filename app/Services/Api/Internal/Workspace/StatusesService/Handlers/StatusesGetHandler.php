<?php

namespace App\Services\Api\Internal\Workspace\StatusesService\Handlers;

use App\Contracts\Repositories\StatusesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class StatusesGetHandler
{
    public function __construct(
        private StatusesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
