<?php

namespace App\Services\Api\Internal\Workspace\Permissions\PermissionsService;

use App\Contracts\Services\Internal\PermissionsServiceContract;
use App\Services\Api\Internal\Workspace\Permissions\PermissionsService\Handlers\PermissionsGetHandler;
use Illuminate\Support\Collection;

readonly class PermissionsService implements PermissionsServiceContract
{
    public function __construct(
        private PermissionsGetHandler $getHandler,
    ) {
    }

    public function index(): Collection
    {
        return $this->getHandler->run();
    }
}
