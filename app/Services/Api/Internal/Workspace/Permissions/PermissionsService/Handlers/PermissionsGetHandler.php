<?php

namespace App\Services\Api\Internal\Workspace\Permissions\PermissionsService\Handlers;

use App\Contracts\Repositories\PermissionsRepositoryContract;
use Illuminate\Support\Collection;

readonly class PermissionsGetHandler
{
    public function __construct(
        private PermissionsRepositoryContract $repository
    ) {
    }

    public function run(): Collection
    {
        $result = $this->repository->get();

        if ($result->isNotEmpty()) {
            foreach ($result as $item) {
                $item->groups = json_decode($item->groups, true);
            }
        }

        return $result;
    }
}
