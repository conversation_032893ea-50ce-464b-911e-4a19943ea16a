<?php

namespace App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DepartmentPermissionsRepositoryContract;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO\DepartmentPermissionDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;

readonly class DepartmentPermissionsCreateHandler
{
    use HasOrderedUuid;

    public string $resourceId;

    public function __construct(
        private DepartmentPermissionsRepositoryContract $departmentPermissionsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if ($dto instanceof DepartmentPermissionDTO) {

            $this->departmentPermissionsRepository->insert(
                $dto->toInsertArray($this->resourceId)
            );

            return $this->resourceId;

        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
