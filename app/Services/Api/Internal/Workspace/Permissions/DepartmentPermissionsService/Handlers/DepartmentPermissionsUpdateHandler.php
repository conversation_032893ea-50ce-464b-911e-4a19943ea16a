<?php

namespace App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\DepartmentPermissionsRepositoryContract;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO\DepartmentPermissionDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;

readonly class DepartmentPermissionsUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private DepartmentPermissionsRepositoryContract $repository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof DepartmentPermissionDTO) {

            $ids = collect($dto->permissions);

            $this->repository
                ->deleteWhereNotInIds(
                    $dto->departmentId,
                    $ids->pluck('id')->toArray()
                );


            $this->repository
                ->upsert($dto->toUpdateArray());

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
