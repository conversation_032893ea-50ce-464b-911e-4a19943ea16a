<?php

namespace App\Services\Api\Internal\Workspace\CabinetSettingsService;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Cabinet\CabinetSettingsServiceContract;
use App\Services\Api\Internal\Workspace\CabinetSettingsService\Handlers\CabinetSettingsShowHandler;
use App\Services\Api\Internal\Workspace\CabinetSettingsService\Handlers\CabinetSettingsUpdateHandler;

readonly class CabinetSettingsService implements CabinetSettingsServiceContract
{
    public function __construct(
        private CabinetSettingsUpdateHandler $updateHandler,
        private CabinetSettingsShowHandler $showHandler
    ) {
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }
}
