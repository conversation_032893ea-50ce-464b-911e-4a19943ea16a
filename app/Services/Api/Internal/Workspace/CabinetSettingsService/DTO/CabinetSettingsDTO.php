<?php

namespace App\Services\Api\Internal\Workspace\CabinetSettingsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class CabinetSettingsDTO implements DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $resourceId,
        public string $numberingType,
        public ?string $email,
        public bool $globalNumbering,
        public bool $useCabinetEmail,
        public bool $checkStock,
        public bool $checkMinPrice,
        public bool $useBin,
        public bool $useProductSeries,
        public bool $autoUpdatePurchasePrice,
        public ?string $logoImageId = null
    ) {
    }

    public function toUpdateArray(): array
    {
        return [
            'numbering_type' => $this->numberingType,
            'email' => $this->email,
            'global_numbering' => $this->globalNumbering,
            'use_cabinet_email' => $this->useCabinetEmail,
            'check_stock' => $this->checkStock,
            'check_min_price' => $this->checkMinPrice,
            'use_bin' => $this->useBin,
            'use_product_series' => $this->useProductSeries,
            'auto_update_purchase_price' => $this->autoUpdatePurchasePrice,
            'logo_image_id' => $this->logoImageId
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            resourceId: $data['resource_id'] ?? null,
            numberingType: $data['numbering_type'],
            email: $data['email'] ?? null,
            globalNumbering: $data['global_numbering'] ?? false,
            useCabinetEmail: $data['use_cabinet_email'] ?? false,
            checkStock: $data['check_stock'] ?? false,
            checkMinPrice: $data['check_min_price'] ?? false,
            useBin: $data['use_bin'] ?? false,
            useProductSeries: $data['use_product_series'] ?? false,
            autoUpdatePurchasePrice: $data['auto_update_purchase_price'] ?? false,
            logoImageId: $data['logo_image_id'] ?? null
        );
    }
}
