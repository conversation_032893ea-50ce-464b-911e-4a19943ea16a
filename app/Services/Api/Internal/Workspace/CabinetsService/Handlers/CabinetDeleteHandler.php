<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService\Handlers;

use App\Contracts\Repositories\CabinetsRepositoryContract;
use Illuminate\Support\Facades\Cache;
use Psr\SimpleCache\InvalidArgumentException;

readonly class CabinetDeleteHandler
{
    public function __construct(
        private CabinetsRepositoryContract $repository,
    ) {
    }

    /**
     * @throws InvalidArgumentException
     */
    public function run(string $resourceId): void
    {
        $employees = $this->repository->getEmployees($resourceId);
        $permissionKeys = [];
        $employeeKeys = [];

        foreach ($employees as $employee) {
            $permissionKeys[] = "permissions_{$employee->user_id}";
            $employeeKeys[] = "employees_{$employee->user_id}";
        }

        Cache::deleteMultiple($permissionKeys);
        Cache::deleteMultiple($employeeKeys);

        $this->repository->delete($resourceId);
    }
}
