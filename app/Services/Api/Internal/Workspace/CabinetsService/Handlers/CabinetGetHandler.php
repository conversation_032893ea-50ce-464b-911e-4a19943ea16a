<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService\Handlers;

use App\Contracts\Repositories\CabinetsRepositoryContract;
use Illuminate\Support\Collection;

readonly class CabinetGetHandler
{
    public function __construct(
        private CabinetsRepositoryContract $repository
    ) {
    }

    public function run(string $userId, string $filters): Collection
    {
        return $this->repository->tempGet($userId, $filters);
    }
}
