<?php

namespace App\Services\Api\Internal\Workspace\EmployeesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Services\Api\Internal\Workspace\EmployeesService\DTO\EmployeeDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class EmployeesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private EmployeeRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof EmployeeDTO) {

            $this->repository->update(
                $dto->id,
                $dto->toUpdateArray()
            );

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
