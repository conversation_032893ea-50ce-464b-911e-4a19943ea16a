<?php

namespace App\Services\Api\Internal\Workspace\EmployeesService\Handlers;

use App\Contracts\Repositories\EmployeeRepositoryContract;

readonly class EmployeesShowHandler
{
    public function __construct(
        private EmployeeRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);
        if ($result && $result->work_schedule) {
            $result->work_schedule = json_decode($result->work_schedule);
        }

        return $result;
    }
}
