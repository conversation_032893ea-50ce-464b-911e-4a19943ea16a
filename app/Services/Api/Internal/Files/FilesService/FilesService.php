<?php

namespace App\Services\Api\Internal\Files\FilesService;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\FilesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Files\FilesService\Handlers\FilesCreateHandler;
use App\Services\Api\Internal\Files\FilesService\Handlers\FilesDeleteHandler;
use App\Services\Api\Internal\Files\FilesService\Handlers\FilesDownloadHandler;
use App\Services\Api\Internal\Files\FilesService\Handlers\FilesGetHandler;
use App\Services\Api\Internal\Files\FilesService\Handlers\FilesShowHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\StreamedResponse;

readonly class FilesService implements FilesServiceContract
{
    public function __construct(
        private FilesDeleteHandler $deleteHandler,
        private FilesGetHandler $getHandler,
        private FilesDownloadHandler $downloadHandler,
        private FilesShowHandler $showHandler,
        private FilesCreateHandler $createHandler
    ) {
    }

    public function index(IndexRequestDTO $dto): Collection
    {
        return $this->getHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    /**
     * @throws NotFoundException
     */
    public function download(string $id): StreamedResponse
    {
        return $this->downloadHandler->run($id);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(DtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }
}
