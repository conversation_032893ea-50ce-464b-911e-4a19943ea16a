<?php

namespace App\Services\Api\Internal\Files\FilesService\Handlers;

use App\Jobs\DeleteFileJob;
use Illuminate\Contracts\Container\BindingResolutionException;

readonly class FilesDeleteHandler
{
    public function __construct(
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(string $resourceId): void
    {
        dispatch(new DeleteFileJob($resourceId));
    }
}
