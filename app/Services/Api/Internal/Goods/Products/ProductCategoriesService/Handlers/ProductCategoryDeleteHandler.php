<?php

namespace App\Services\Api\Internal\Goods\Products\ProductCategoriesService\Handlers;

use App\Contracts\Repositories\ProductCategoriesRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;

class ProductCategoryDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly ProductCategoriesRepositoryContract $repository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->repository->delete($resourceId);
    }
}
