<?php

namespace App\Services\Api\Internal\Goods\Products\ProductCategoriesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ProductCategoriesRepositoryContract;
use App\Services\Api\Internal\Goods\Products\ProductCategoriesService\DTO\ProductCategoryDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class ProductCategoryUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductCategoriesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ProductCategoryDTO) {
            throw new InvalidArgumentException();
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
