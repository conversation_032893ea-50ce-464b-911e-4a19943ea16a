<?php

namespace App\Services\Api\Internal\Goods\Products\ProductCategoriesService\Handlers;

use App\Contracts\Repositories\ProductCategoriesRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductCategoryGetHandler
{
    public function __construct(
        private ProductCategoriesRepositoryContract $repository
    ) {
    }

    public function run(string $cabinetId): ?Collection
    {
        return $this->repository->get($cabinetId);
    }
}
