<?php

namespace App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\Handlers;

use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductEgaisCodeShowHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): ?object
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $result = $this->productEgaisCodeRepository->show($resourceId);

        if (!$result) {
            throw new ResourceNotFoundException();
        }

        return $result;
    }
}
