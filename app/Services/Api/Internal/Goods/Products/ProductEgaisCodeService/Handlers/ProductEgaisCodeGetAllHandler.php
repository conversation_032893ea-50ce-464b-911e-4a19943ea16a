<?php

namespace App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\Handlers;

use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductEgaisCodeGetAllHandler
{
    public function __construct(
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepository,
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productEgaisCodeRepository->getAll($resourceId);
    }
}
