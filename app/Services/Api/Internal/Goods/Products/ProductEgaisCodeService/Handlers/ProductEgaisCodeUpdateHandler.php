<?php

namespace App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductEgaisCodeUpdateHandler
{
    use HasOrderedUuid;

    private HasUpdateArrayDtoContract $dto;
    private string $resourceId;

    public function __construct(
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof ProductDto) {
            $this->dto = $dto;
            $this->resourceId = $this->dto->resourceId;

            if (!$this->validateUuid($this->dto->resourceId)) {
                throw new InvalidUuidException();
            }

            $resource = $this->productEgaisCodeRepository->getFirst($this->resourceId);

            if (!$resource) {
                throw new ResourceNotFoundException();
            }

            $this->productEgaisCodeRepository->update(
                $this->dto->resourceId,
                $this->dto->toUpdateArray(),
            );

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');
    }
}
