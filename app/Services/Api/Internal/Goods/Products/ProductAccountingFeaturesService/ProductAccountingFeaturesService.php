<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Products\ProductAccountingFeaturesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureCreateHandler;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureDeleteHandler;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureGetAllHandler;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureGetHandler;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureShowHandler;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers\ProductAccountingFeatureUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class ProductAccountingFeaturesService implements ProductAccountingFeaturesServiceContract
{
    public function __construct(
        private ProductAccountingFeatureCreateHandler $createHandler,
        private ProductAccountingFeatureGetHandler $getHandler,
        private ProductAccountingFeatureGetAllHandler $getAllHandler,
        private ProductAccountingFeatureShowHandler $showHandler,
        private ProductAccountingFeatureUpdateHandler $updateHandler,
        private ProductAccountingFeatureDeleteHandler $deleteHandler
    ) {
    }


    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data['id']);
    }

    public function getFirstForProductId(string $resourceId): Collection
    {
        return $this->getAllHandler->run($resourceId);
    }

    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws InvalidUuidException
     */
    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
