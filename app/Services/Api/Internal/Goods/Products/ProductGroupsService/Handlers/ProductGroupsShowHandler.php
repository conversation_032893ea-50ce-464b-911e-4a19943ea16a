<?php

namespace App\Services\Api\Internal\Goods\Products\ProductGroupsService\Handlers;

use App\Contracts\Repositories\ProductGroupsRepositoryContract;

readonly class ProductGroupsShowHandler
{
    public function __construct(
        private ProductGroupsRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        return $this->repository->show($id);
    }
}
