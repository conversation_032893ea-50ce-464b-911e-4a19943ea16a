<?php

namespace App\Services\Api\Internal\Goods\Products\ProductGroupsService\Handlers;

use App\Contracts\Repositories\ProductGroupsRepositoryContract;
use App\Traits\HasOrderedUuid;

class ProductGroupsBulkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductGroupsRepositoryContract $repository,
    ) {
    }

    public function run(array $ids): void
    {
        $this->repository->deleteWhereIn($ids);
    }
}
