<?php

namespace App\Services\Api\Internal\Goods\Products\ProductsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Contracts\Repositories\ProductsRepositoryContract;
use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Enums\Api\Internal\ProductThresholdsEnum;
use App\Helpers\SetIdHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;
use RuntimeException;

// use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;

class ProductCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProductsRepositoryContract $productRepository,
        private readonly ProductPricesRepositoryContract $productPriceRepository,
        private readonly ProductAccountingFeaturesRepositoryContract $productAccountingFeaturesRepositoryContract,
        private readonly ProductThresholdsRepositoryContract $productThresholdsRepositoryContract,
        // private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepositoryContract,
        private readonly ProductAttributeRepositoryContract $productAttributeRepositoryContract,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ProductDto) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $employeeId = $dto->employeeId;

        if (!$employeeId) {
            throw new RuntimeException('Employee not found');
        }


        $setIdProductPrice = SetIdHandler::setIdData(
            $dto->toInsertArrayProductPrice(),
            [
                'product_id' => $this->resourceId,
                // 'cabinet_id' => $dto->cabinetId,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            'sale_price'
        );

        $this->productRepository->insert(
            $dto->toInsertArray($this->resourceId, $employeeId)
        );


        // if (!empty($dto->images)) {
        //     $this->saveFiles(
        //         $employeeId,
        //         $dto->images,
        //         'images', // disc s3 images или docs
        //         'image' // type image или file
        //     );
        // }

        // if (!empty($dto->files)) {
        //     $this->saveFiles(
        //         $employeeId,
        //         $dto->images
        //     );
        // }


        if (!empty($dto->toInsertArrayProductAttribute()['attributes'])) {

            $attributes = SetIdHandler::setIdData(
                $dto->toInsertArrayProductAttribute(),
                [
                    'product_id' => $this->resourceId,
                ],
                'attributes'
            );

            $this->productAttributeRepositoryContract->upsert($attributes);

        }


        if (!empty($setIdProductPrice->toArray())) {
            $this->productPriceRepository->insert($setIdProductPrice->toArray());
        }

        if (!empty($dto->toInsertArrayProductAccountingFeatures($this->resourceId))) {

            $productAccountingFeature = $dto->toInsertArrayProductAccountingFeatures($this->resourceId);

            $productAccountingFeature['id'] = $this->generateUuid();

            $this->productAccountingFeaturesRepositoryContract->insert($productAccountingFeature);
        }

        // TODO временно отключены
        // if (!empty($dto->toInsertArrayProductEgaisCode()['egais_code'])) {

        //     $setIdProductEgaisCode = SetIdHandler::setIdData(
        //         $dto->toInsertArrayProductEgaisCode(),
        //         [
        //             'product_id' => $this->resourceId,
        //             'created_at' => now(),
        //             'updated_at' => now(),
        //         ],
        //         'egais_code');

        //     $this->productEgaisCodeRepositoryContract->upsert($setIdProductEgaisCode);

        // }
        // dd($dto->toInsertArrayProductThresholds()['thresholds']);

        if (!empty($dto->toInsertArrayProductThresholds()['thresholds'])) {

            $thresholds = $dto->toInsertArrayProductThresholds()['thresholds'];

            if ($thresholds['type'] == ProductThresholdsEnum::SET_FOR_EACH_WAREHOUSE->value) {

                $setIdArray = SetIdHandler::setIdData(
                    $thresholds,
                    [
                        'product_id' => $this->resourceId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ],
                    'warehouses'
                );

                $this->productThresholdsRepositoryContract->upsert($setIdArray);

            }

        }

        return $this->resourceId;
    }
}
