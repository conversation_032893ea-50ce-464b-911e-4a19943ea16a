<?php

namespace App\Services\Api\Internal\Goods\Products\ProductsService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Contracts\Repositories\FileRelationsRepositoryContract;
use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Contracts\Repositories\ProductsRepositoryContract;
use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductsRepositoryContract $productRepository,
        private readonly ProductPricesRepositoryContract $productPriceRepository,
        private readonly BarcodesRepositoryContract $barcodesRepository,
        private readonly ProductAccountingFeaturesRepositoryContract $productAccountingFeaturesRepositoryContract,
        private readonly ProductThresholdsRepositoryContract $productThresholdsRepositoryContract,
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepositoryContract,
        private readonly ProductAttributeRepositoryContract $productAttributeRepositoryContract,
        private readonly FileRelationsRepositoryContract $FileRelationsRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->FileRelationsRepository->deleteRelationsByRelatedId($resourceId);

        $this->barcodesRepository->deleteOldBarcodesWhereBarcoddableId($resourceId);

        if (!$this->productRepository->delete($resourceId)) {
            throw new ResourceNotFoundException();
        }

    }
}
