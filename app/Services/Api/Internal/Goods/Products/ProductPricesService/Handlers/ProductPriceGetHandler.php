<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPricesService\Handlers;

use App\Contracts\Repositories\ProductPricesRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductPriceGetHandler
{
    public function __construct(
        private ProductPricesRepositoryContract $productPricesRepository
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productPricesRepository->get($resourceId);
    }
}
