<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPricesService\Handlers;

use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductPriceShowHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductPricesRepositoryContract $productPricesRepository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): ?object
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $result = $this->productPricesRepository->show($resourceId);

        if (!$result) {
            throw new ResourceNotFoundException();
        }

        return $result;
    }
}
