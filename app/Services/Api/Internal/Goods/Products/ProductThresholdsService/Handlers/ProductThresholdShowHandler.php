<?php

namespace App\Services\Api\Internal\Goods\Products\ProductThresholdsService\Handlers;

use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductThresholdShowHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductThresholdsRepositoryContract $ProductThresholdsRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): ?object
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $result = $this->ProductThresholdsRepository->show($resourceId);

        if (!$result) {
            throw new ResourceNotFoundException();
        }

        return $result;
    }
}
