<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeGroupsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class AttributeGroupDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $name,
        public ?string $description,
        public ?int $sort_order,
        public bool $status,
        public ?string $resourceId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'status' => $this->status
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'status' => $this->status
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            description: $data['description'] ?? null,
            sort_order: $data['sort_order'] ?? null,
            status: $data['status'] ?? true,
            resourceId: $data['resource_id'] ?? null
        );
    }
}
