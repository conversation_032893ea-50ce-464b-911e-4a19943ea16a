<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeGroupsService\Handlers;

use App\Contracts\Repositories\AttributeGroupsRepositoryContract;
use App\Traits\HasOrderedUuid;

class AttributeGroupShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly AttributeGroupsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $data = $this->repository->show($resourceId);

        $data->attributes = json_decode($data->attributes, true);

        return $data;
    }
}
