<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeValuesService\Handlers;

use App\Contracts\Repositories\AttributeValuesRepositoryContract;

class AttributeValueShowHandler
{
    public function __construct(
        private readonly AttributeValuesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
