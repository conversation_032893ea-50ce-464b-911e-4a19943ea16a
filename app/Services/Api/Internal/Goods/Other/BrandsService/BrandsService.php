<?php

namespace App\Services\Api\Internal\Goods\Other\BrandsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\BrandsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Goods\Other\BrandsService\Handlers\BrandCreateHandler;
use App\Services\Api\Internal\Goods\Other\BrandsService\Handlers\BrandDeleteHandler;
use App\Services\Api\Internal\Goods\Other\BrandsService\Handlers\BrandGetHandler;
use App\Services\Api\Internal\Goods\Other\BrandsService\Handlers\BrandShowHandler;
use App\Services\Api\Internal\Goods\Other\BrandsService\Handlers\BrandUpdateHandler;
use Illuminate\Support\Collection;

readonly class BrandsService implements BrandsServiceContract
{
    public function __construct(
        private BrandGetHandler $getHandler,
        private BrandCreateHandler $createHandler,
        private BrandUpdateHandler $updateHandler,
        private BrandDeleteHandler $deleteHandler,
        private BrandShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws InvalidUuidException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }
}
