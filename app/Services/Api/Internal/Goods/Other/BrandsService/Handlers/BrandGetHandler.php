<?php

namespace App\Services\Api\Internal\Goods\Other\BrandsService\Handlers;

use App\Contracts\Repositories\BrandsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class BrandGetHandler
{
    public function __construct(
        private BrandsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
