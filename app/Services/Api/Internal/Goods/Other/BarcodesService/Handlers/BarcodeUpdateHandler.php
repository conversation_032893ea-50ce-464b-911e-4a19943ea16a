<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class BarcodeUpdateHandler
{
    use HasOrderedUuid;
    use HasBarcodes;

    public function __construct(
        private readonly BarcodesRepositoryContract $repository,
        private BarcodeForCreateOrUpdateHandler $handler
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof BarcodeDTO) {
            throw new InvalidArgumentException();
        }

        $setIdBarcodes = $this->handler->run($dto);

        $this->repository->update($dto->resourceId, $setIdBarcodes);

    }

}
