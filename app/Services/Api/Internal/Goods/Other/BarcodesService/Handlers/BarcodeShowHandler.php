<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Traits\HasOrderedUuid;

class BarcodeShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly BarcodesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
