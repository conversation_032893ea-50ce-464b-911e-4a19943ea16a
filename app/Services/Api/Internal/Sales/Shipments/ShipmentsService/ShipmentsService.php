<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Sales\ShipmentsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsBulkCopyHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsBulkDeleteHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsBulkHeldHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsBulkUnheldHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsCreateHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsDeleteHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsGetHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsShowHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers\ShipmentsUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Throwable;

readonly class ShipmentsService implements ShipmentsServiceContract
{
    public function __construct(
        private ShipmentsCreateHandler $createHandler,
        private ShipmentsGetHandler $getHandler,
        private ShipmentsShowHandler $showHandler,
        private ShipmentsUpdateHandler $updateHandler,
        private ShipmentsDeleteHandler $deleteHandler,
        private ShipmentsBulkHeldHandler $bulkHeldHandler,
        private ShipmentsBulkUnheldHandler $bulkUnheldHandler,
        private ShipmentsBulkDeleteHandler $bulkDeleteHandler,
        private ShipmentsBulkCopyHandler $bulkCopyHandler
    ) {
    }


    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkHeld(array $ids): void
    {
        $this->bulkHeldHandler->run($ids);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkUnheld(array $ids): void
    {
        $this->bulkUnheldHandler->run($ids);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkCopy(array $ids): void
    {
        $this->bulkCopyHandler->run($ids);
    }
}
