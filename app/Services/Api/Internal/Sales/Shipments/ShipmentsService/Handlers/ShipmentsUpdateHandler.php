<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ShipmentDeliveryRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\DTO\ShipmentDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\Queue;
use RuntimeException;

class ShipmentsUpdateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public function __construct(
        private readonly ShipmentsRepositoryContract $shipmentsRepository,
        private readonly ShipmentDeliveryRepositoryContract $shipmentDeliveryRepository,
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ShipmentDTO) {
            throw new RuntimeException('Invalid dto');
        }
        $dto1 = $dto;

        $resource = $this->shipmentsRepository->findFirst($dto1->resourceId);

        if (!$resource) {
            throw new RuntimeException('Shipment not found');
        }
        $update = $dto->toUpdateArray();
        if ($resource->legal_entity_id !== $dto->legalEntityId) {
            $documentNumberGenerator = new DocumentNumberGenerator(
                'shipments',
                $dto->cabinetId,
                $dto->number,
                $dto->legalEntityId
            );
            $update['number'] = $documentNumberGenerator->generateNumber();
        }

        $this->shipmentsRepository->update(
            $dto1->resourceId,
            $update,
        );

        if (!empty($dto->deliveryInfo)) {
            $dataArray = $dto->toDeliveryArray($dto->resourceId);
            $this->shipmentDeliveryRepository->upsert($dataArray);
        }

        $this->setRelationToFiles($dto->resourceId, $dto->files, 'shipments', true);

        // Проверяем, изменилась ли дата отгрузки или склад
        if ($resource->date_from != $dto1->dateFrom || $resource->warehouse_id != $dto1->warehouseId) {
            $items = $this->shipmentItemsRepository->getByShipmentId($dto1->resourceId);
            Queue::push(new HandleFifoJob($items));
        }
    }
}
