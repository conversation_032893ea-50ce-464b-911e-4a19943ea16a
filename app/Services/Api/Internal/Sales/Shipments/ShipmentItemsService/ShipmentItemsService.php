<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Sales\ShipmentItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemDeleteHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemGetHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemsCreateHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemShowHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemsUpdateHandler;
use App\Services\Api\Internal\Sales\ShipmentItemsService\DTO\ShipmentItemCalculateDTO;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class ShipmentItemsService implements ShipmentItemsServiceContract
{
    public function __construct(
        private ShipmentItemsCreateHandler $createHandler,
        private ShipmentItemsUpdateHandler $updateHandler,
        private ShipmentItemDeleteHandler $deleteHandler,
        private ShipmentItemGetHandler $getHandler,
        private ShipmentItemShowHandler $showHandler,
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws Exception
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции отгрузки
     *
     * @param ShipmentItemCalculateDTO $dto
     * @return object
     */
    public function calculateMetrics(ShipmentItemCalculateDTO $dto): object
    {
        return $this->getHandler->calculateMetrics($dto);
    }
}
