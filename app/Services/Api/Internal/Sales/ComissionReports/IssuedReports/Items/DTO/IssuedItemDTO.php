<?php

namespace App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class IssuedItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public ?string $id,
        public ?string $reportId,
        public ?string $productId,
        public int $quantity,
        public string $price,
        public string $vatRateId,
        public string $summaryPrice,
        public string $comissionReturnValue,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'report_id' => $this->reportId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'vat_rate_id' => $this->vatRateId,
            'summary_price' => $this->summaryPrice,
            'comission_value' => $this->comissionReturnValue,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'vat_rate_id' => $this->vatRateId,
            'summary_price' => $this->summaryPrice,
            'comission_value' => $this->comissionReturnValue,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            reportId: $data['report_id'] ?? null,
            productId: $data['product_id'] ?? null,
            quantity: $data['quantity'] ?? 0,
            price: $data['price'] ?? 0,
            vatRateId: $data['vat_rate_id'] ?? null,
            summaryPrice: $data['summary_price'] ?? 0,
            comissionReturnValue: $data['comission_return_value'] ?? 0,
        );
    }
}
