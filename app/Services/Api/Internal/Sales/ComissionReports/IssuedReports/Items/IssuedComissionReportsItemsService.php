<?php

namespace App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\IssuedComissionReportsItemsRepositoryContract;
use App\Contracts\Services\Internal\Sales\IssuedComissionReportItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items\DTO\IssuedItemDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class IssuedComissionReportsItemsService implements IssuedComissionReportItemsServiceContract
{
    use HasOrderedUuid;
    use HasFiles;
    use PrecisionCalculator;

    public function __construct(
        private readonly IssuedComissionReportsItemsRepositoryContract $repository
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->repository->get(
            id: $data->id,
            filters: $data->filters,
            fields: $data->fields,
            sortField: $data->sortField,
            sortDirection: $data->sortDirection,
            page: $data->page,
            perPage: $data->perPage
        );
    }

    public function show(string $id): ?object
    {
        return $this->repository->show($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof IssuedItemDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $dto->summaryPrice = $this->multiply(
            $dto->price,
            $dto->quantity
        );

        $id = $this->generateUuid();
        $this->repository->insert($dto->toInsertArray($id));

        $this->recalculateSumOnReport($dto->reportId);

        return $id;
    }

    /**
     * @throws NotFoundException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof IssuedItemDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $dto->summaryPrice = $this->multiply(
            $dto->price,
            $dto->quantity
        );

        $item = $this->repository->show($dto->id);

        if (!$item) {
            throw new NotFoundException('Item not found');
        }

        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );

        $this->recalculateSumOnReport($item->report_id);
    }


    /**
     * @throws NotFoundException
     */
    public function delete(string $id): void
    {
        $item = $this->repository->show($id);

        if (!$item) {
            throw new NotFoundException('Item not found');
        }

        $this->repository->delete($id);

        $this->recalculateSumOnReport($item->report_id);
    }

    /**
     * @param string $reportId
     * @return void
     */
    public function recalculateSumOnReport(string $reportId): void
    {
        DB::update(
            '
                UPDATE issued_comission_reports
                SET sum = (
                    COALESCE((
                        SELECT SUM(CAST(price AS DECIMAL(65, 30)) * quantity)
                        FROM issued_comission_report_items
                        WHERE report_id = ?
                    ), 0)
                )
                WHERE id = ?
            ',
            [$reportId, $reportId]
        );

        $report = DB::table('issued_comission_reports')
            ->where('id', $reportId)
            ->first();

        DB::update(
            '
                UPDATE issued_comission_reports
                SET sum = ?
                WHERE id = ?
            ',
            [$this->normalize($report->sum), $reportId]
        );
    }
}
