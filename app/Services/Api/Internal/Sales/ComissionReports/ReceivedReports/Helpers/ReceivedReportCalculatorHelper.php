<?php

namespace App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Helpers;

use Illuminate\Support\Facades\DB;

class ReceivedReportCalculatorHelper
{
    public static function recalculateSumInReport(string $reportId): void
    {
        DB::update(
            '
                UPDATE received_comission_reports
                SET sum = (
                    COALESCE((
                        SELECT SUM(CAST(price AS DECIMAL(65, 30)) * quantity)
                        FROM received_comission_report_realized_items
                        WHERE report_id = ?
                    ), 0) - COALESCE((
                        SELECT SUM(CAST(price AS DECIMAL(65, 30)) * quantity)
                        FROM received_comission_report_return_items
                        WHERE report_id = ?
                    ), 0)
                )
                WHERE id = ?
            ',
            [$reportId, $reportId, $reportId]
        );

        $report = DB::table('received_comission_reports')
            ->where('id', $reportId)
            ->first();

        DB::update(
            '
                UPDATE received_comission_reports
                SET sum = ?
                WHERE id = ?
            ',
            [self::normalize($report->sum), $reportId]
        );
    }

    private static function normalize(string $number): string
    {
        $normalized = rtrim($number, '0');
        $normalized = rtrim($normalized, '.');
        return $normalized === '' ? '0' : $normalized;
    }
}
