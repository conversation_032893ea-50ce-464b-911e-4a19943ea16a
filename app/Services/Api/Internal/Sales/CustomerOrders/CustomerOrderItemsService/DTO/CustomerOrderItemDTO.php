<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Services\Api\Internal\Sales\CustomerOrders\Traits\CustomerOrderTotalCalculator;
use App\Traits\HasOrderedUuid;
use App\Traits\SummaryPriceCalculator;

class CustomerOrderItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;
    use SummaryPriceCalculator;
    use CustomerOrderTotalCalculator;

    public string $totalPrice;
    public function __construct(
        public ?string $orderId,
        public ?string $productId,
        public int $quantity,
        public string $priceInCurrency,
        public string $currencyRateToBase,
        public ?string $currencyId,
        public string $discount = '0',
        public ?string $vatRateId = null,
        public ?string $resourceId = null,
        public ?string $priceInBase = null,
        public ?string $amountInBase = null,
    ) {
        $this->totalPrice = $this->calculateTotalPrice();
    }

    /**
     * Рассчитывает total_price с учетом настроек НДС в заказе покупателя
     */
    private function calculateTotalPrice(): string
    {
        if (!$this->orderId) {
            // Если нет ID заказа, используем простой расчет
            return $this->multiply($this->priceInCurrency, (string)$this->quantity);
        }

        $vatInfo = $this->getVatInfo($this->orderId, $this->vatRateId);
        
        return $this->calculateCustomerOrderItemTotal(
            $this->priceInCurrency,
            (string)$this->quantity,
            $this->discount,
            $vatInfo['vat_rate'],
            $vatInfo['price_includes_vat'],
            $vatInfo['has_vat']
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            orderId: $data['order_id'] ?? null,
            productId: $data['product_id']  ?? null,
            quantity: $data['quantity'],
            priceInCurrency: $data['price_in_currency'] ?? '0',
            currencyRateToBase: $data['currency_rate_to_base'] ?? '1',
            currencyId: $data['currency_id'] ?? null,
            discount: $data['discount'] ?? '0',
            vatRateId: $data['vat_rate_id'] ?? null,
            resourceId: $data['id'] ?? null,
            priceInBase: $data['price_in_base'] ?? '0',
            amountInBase: $data['amount_in_base'] ?? '0',
        );
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'order_id' => $this->orderId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vatRateId ?? null,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'vat_rate_id' => $this->vatRateId,
            'discount' => $this->discount,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }
}
