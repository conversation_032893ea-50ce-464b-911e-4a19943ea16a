<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO\CustomerOrderItemDTO;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\CustomerOrderItemVatCalculatorService;
use App\Services\Api\Internal\Sales\CustomerOrders\Traits\CustomerOrderTotalCalculator;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use InvalidArgumentException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class CustomerOrderItemUpdateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;
    use CustomerOrderTotalCalculator;

    public function __construct(
        private readonly CustomerOrderItemsRepositoryContract $customerOrderItemsRepository,
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository,
        private readonly CustomerOrderItemVatCalculatorService $vatCalculatorService
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof CustomerOrderItemDTO) {
            throw new InvalidArgumentException();
        }

        $item = $this->customerOrderItemsRepository->show($dto->resourceId);
        if (!$item) {
            throw new ResourceNotFoundException();
        }

        // Получаем заказ для проверки настроек НДС
        $order = $this->customerOrdersRepository->show($item->order_id);
        if (!$order) {
            throw new InvalidArgumentException('Customer order not found');
        }

        // Устанавливаем значения по умолчанию
        if (!$dto->currencyId) {
            // Получаем учетную валюту кабинета
            $baseCurrency = \DB::table('cabinet_currencies')
                ->where('cabinet_id', $order->cabinet_id)
                ->where('is_accouting', true)
                ->first();
            $dto->currencyId = $baseCurrency?->id;
            
            if (!$dto->currencyId) {
                throw new InvalidArgumentException('Default currency not found for cabinet');
            }
        }
        
        if (!$dto->priceInCurrency) {
            $dto->priceInCurrency = '0';
        }
        
        if (!$dto->currencyRateToBase) {
            $dto->currencyRateToBase = '1';
        }
        
        if (!$dto->discount) {
            $dto->discount = '0';
        }

        // Автоматически назначаем ставку НДС "Без НДС" если has_vat = false и ставка не указана
        if (!$order->has_vat && !$dto->vatRateId) {
            $dto->vatRateId = $this->vatCalculatorService->getAutoVatRateId($order->cabinet_id);
        }

        // Рассчитываем цену в базовой валюте
        $dto->priceInBase = $this->multiply($dto->priceInCurrency, $dto->currencyRateToBase);

        // Рассчитываем сумму позиции (цена * количество)
        $dto->amountInBase = $this->multiply($dto->priceInBase, $dto->quantity);

        // Используем новый расчет с НДС
        $dto->totalPrice = $this->vatCalculatorService->calculateItemTotal(
            $item->order_id,
            $dto->priceInCurrency,
            (string)$dto->quantity,
            $dto->discount,
            $dto->vatRateId,
            $order->cabinet_id
        );

        $result = $this->customerOrderItemsRepository->update(
            $dto->resourceId,
            $dto->toUpdateArray(),
        );

        if (!$result) {
            throw new ResourceNotFoundException();
        }

        // Пересчитываем общую сумму заказа с учетом НДС
        $this->updateCustomerOrderTotal($item->order_id);
    }
}
