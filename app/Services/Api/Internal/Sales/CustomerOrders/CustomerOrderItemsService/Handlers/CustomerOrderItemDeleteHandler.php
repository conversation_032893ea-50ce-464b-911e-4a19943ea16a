<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers;

use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Services\Api\Internal\Sales\CustomerOrders\Traits\CustomerOrderTotalCalculator;
use App\Traits\HasOrderedUuid;

class CustomerOrderItemDeleteHandler
{
    use HasOrderedUuid;
    use CustomerOrderTotalCalculator;

    public function __construct(
        private readonly CustomerOrderItemsRepositoryContract $customerOrderItemsrepository
    ) {
    }

    public function run(string $resourceId): void
    {
        $item = $this->customerOrderItemsrepository->show($resourceId);

        // Сохраняем ID заказа для пересчета после удаления
        $orderId = $item->order_id;

        $this->customerOrderItemsrepository->delete($resourceId);

        // Пересчитываем общую сумму заказа после удаления позиции с учетом НДС
        $this->updateCustomerOrderTotal($orderId);
    }
}
