<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO\CustomerOrderItemDTO;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\CustomerOrderItemVatCalculatorService;
use App\Services\Api\Internal\Sales\CustomerOrders\Traits\CustomerOrderTotalCalculator;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use InvalidArgumentException;

class CustomerOrderItemCreateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;
    use CustomerOrderTotalCalculator;

    protected string $resourceId;
    public function __construct(
        private readonly CustomerOrderItemsRepositoryContract $customerOrderItemsRepository,
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository,
        private readonly CustomerOrderItemVatCalculatorService $vatCalculatorService
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof CustomerOrderItemDTO) {
            throw new InvalidArgumentException('Invalid DTO');
        }

        // Получаем заказ для проверки настроек НДС
        $order = $this->customerOrdersRepository->show($dto->orderId);
        if (!$order) {
            throw new InvalidArgumentException('Customer order not found');
        }

        // Устанавливаем значения по умолчанию
        if (!$dto->currencyId) {
            // Получаем учетную валюту кабинета
            $baseCurrency = \DB::table('cabinet_currencies')
                ->where('cabinet_id', $order->cabinet_id)
                ->where('is_accouting', true)
                ->first();
            $dto->currencyId = $baseCurrency?->id;
            
            if (!$dto->currencyId) {
                throw new InvalidArgumentException('Default currency not found for cabinet');
            }
        }
        
        if (!$dto->priceInCurrency) {
            $dto->priceInCurrency = '0';
        }
        
        if (!$dto->currencyRateToBase) {
            $dto->currencyRateToBase = '1';
        }
        
        if (!$dto->discount) {
            $dto->discount = '0';
        }

        // Автоматически назначаем ставку НДС "Без НДС" если has_vat = false и ставка не указана
        if (!$order->has_vat && !$dto->vatRateId) {
            $dto->vatRateId = $this->vatCalculatorService->getAutoVatRateId($order->cabinet_id);
        }

        $dto->priceInBase = $this->multiply($dto->priceInCurrency, $dto->currencyRateToBase);
        $dto->amountInBase = $this->multiply($dto->priceInBase, $dto->quantity);

        // Используем новый расчет с НДС
        $dto->totalPrice = $this->vatCalculatorService->calculateItemTotal(
            $dto->orderId,
            $dto->priceInCurrency,
            (string)$dto->quantity,
            $dto->discount,
            $dto->vatRateId,
            $order->cabinet_id
        );

        $this->customerOrderItemsRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        // Пересчитываем общую сумму заказа с учетом НДС
        $this->updateCustomerOrderTotal($dto->orderId);

        return $this->resourceId;
    }
}
