<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;

readonly class CustomerOrderBulkHeldHandler
{
    public function __construct(
        private CustomerOrdersRepositoryContract $repository
    ) {
    }

    public function run(array $ids): void
    {
        $this->repository->updateWhereIn(
            $ids,
            ['held' => true]
        );
    }
}
