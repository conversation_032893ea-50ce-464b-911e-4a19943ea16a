<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;

readonly class CustomerOrdersDeleteHandler
{
    public function __construct(
        private CustomerOrdersRepositoryContract $customerOrdersRepository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->customerOrdersRepository->delete($resourceId);
    }
}
