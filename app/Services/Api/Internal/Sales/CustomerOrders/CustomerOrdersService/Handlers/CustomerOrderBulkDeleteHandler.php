<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class CustomerOrderBulkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CustomerOrdersRepositoryContract $repository,
        private readonly DocumentsRepositoryContract $documentsRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     * @throws \Throwable
     */
    public function run(array $ids): void
    {
        $this->repository->bulkDelete($ids);
        $this->documentsRepository->deleteWhereDocumentableIdIn($ids);
    }
}
