<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers;

use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;

readonly class OutgoingPaymentsDeleteHandler
{
    public function __construct(
        private OutgoingPaymentsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
