<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\DTO\OutgoingPaymentDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class OutgoingPaymentsCreateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public string $resourceId;

    public function __construct(
        private OutgoingPaymentsRepositoryContract $repository,
        private DocumentsRepositoryContract $documentsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if ($dto instanceof OutgoingPaymentDTO) {

            $documentNumberGenerator = new DocumentNumberGenerator(
                'outgoing_payments',
                $dto->cabinet_id,
                $dto->number,
                $dto->legal_entity_id
            );
            $dto->number = $documentNumberGenerator->generateNumber();

            $this->repository->insert(
                $dto->toInsertArray($this->resourceId, $dto->employee_id)
            );

            $this->documentsRepository->insert(
                [
                    'documentable_id' => $this->resourceId,
                    'documentable_type' => 'outgoing_payments',
                    'lft' => 1,
                    'rgt' => 2,
                    'parent_id' => null,
                    'tree_id' => $this->generateUuid(),
                    'cabinet_id' => $dto->cabinet_id
                ]
            );

            $this->setRelationToFiles($this->resourceId, $dto->files, 'outgoing_payments');

            return $this->resourceId;

        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
