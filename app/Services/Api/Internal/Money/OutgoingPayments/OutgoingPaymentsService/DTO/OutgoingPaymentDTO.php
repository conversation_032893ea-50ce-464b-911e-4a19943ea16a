<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use Illuminate\Support\Carbon;

class OutgoingPaymentDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $legal_entity_id,
        public string $contractor_id,
        public string $department_id,
        public string $employee_id,
        public string $currency_id,
        public float $currencyValue,
        public ?string $status_id = null,
        public ?string $number = null,
        public ?string $date_from = null,
        public ?bool $held = true,
        public ?string $sales_channel_id = null,
        public float $sum = 0,
        public float $included_vat = 0,
        public ?string $comment = null,
        public float $bounded_sum = 0,
        public float $not_bounded_sum = 0,
        public ?string $id = null,
        public ?bool $without_closing_documents = false,
        public ?bool $is_imported = false,
        public array $files = [],
        public bool $is_common = false
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinet_id,
            'department_id' => $this->department_id,

            'employee_id' => $this->employee_id,
            'number' => $this->number,
            'date_from' => $this->date_from,
            'held' => $this->held,
            'without_closing_documents' => $this->without_closing_documents,

            'legal_entity_id' => $this->legal_entity_id,
            'contractor_id' => $this->contractor_id,
            'status_id' => $this->status_id,
            'sales_channel_id' => $this->sales_channel_id,
            'sum' => $this->sum,
            'included_vat' => $this->included_vat,
            'comment' => $this->comment,
            'currency_id' => $this->currency_id,
            'currency_value' => $this->currencyValue,
            'bounded_sum' => $this->bounded_sum,
            'not_bounded_sum' => $this->not_bounded_sum,
            'is_imported' => $this->is_imported,
            'is_common' => $this->is_common,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'date_from' => $this->date_from,
            'held' => $this->held,

            'legal_entity_id' => $this->legal_entity_id,
            'contractor_id' => $this->contractor_id,
            'sales_channel_id' => $this->sales_channel_id,
            'sum' => $this->sum,
            'included_vat' => $this->included_vat,
            'comment' => $this->comment,
            'currency_id' => $this->currency_id,
            'currency_value' => $this->currencyValue,
            'bounded_sum' => $this->bounded_sum,
            'not_bounded_sum' => $this->not_bounded_sum,
            'is_common' => $this->is_common,
            'employee_id' => $this->employee_id,
            'department_id' => $this->department_id,
            'without_closing_documents' => $this->without_closing_documents
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            legal_entity_id: $data['legal_entity_id'],
            contractor_id: $data['contractor_id'],
            department_id: $data['department_id'],
            employee_id: $data['employee_id'],
            currency_id: $data['currency_id'],
            currencyValue: $data['currency_value'] ?? 1,
            status_id: $data['status_id'] ?? null,
            number: $data['number'] ?? null,
            date_from: isset($data['date_from']) ? Carbon::parse($data['date_from']) : null,
            held: $data['held'] ?? true,
            sales_channel_id: $data['sales_channel_id'] ?? null,
            sum: $data['sum'] ?? 0,
            included_vat: $data['included_vat'] ?? 0,
            comment: $data['comment'] ?? null,
            bounded_sum: $data['bounded_sum'] ?? 0,
            not_bounded_sum: $data['not_bounded_sum'] ?? 0,
            id: $data['id'] ?? null,
            without_closing_documents: $data['without_closing_documents'] ?? false,
            is_imported: $data['is_imported'] ?? false,
            files: $data['files'] ?? [],
            is_common: $data['is_common'] ?? false
        );
    }
}
