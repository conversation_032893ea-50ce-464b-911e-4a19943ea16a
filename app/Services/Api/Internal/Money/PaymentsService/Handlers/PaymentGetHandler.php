<?php

namespace App\Services\Api\Internal\Money\PaymentsService\Handlers;

use App\Contracts\Repositories\FinanceRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class PaymentGetHandler
{
    public function __construct(
        private FinanceRepositoryContract $repository,
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
