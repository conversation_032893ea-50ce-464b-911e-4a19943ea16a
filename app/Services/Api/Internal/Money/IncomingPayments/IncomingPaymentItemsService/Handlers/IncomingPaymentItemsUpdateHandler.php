<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\IncomingPaymentItemsRepositoryContract;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\DTO\IncomingPaymentItemDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class IncomingPaymentItemsUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private IncomingPaymentItemsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof IncomingPaymentItemDTO) {

            $this->repository->update(
                $dto->resourceId,
                $dto->toUpdateArray()
            );

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
