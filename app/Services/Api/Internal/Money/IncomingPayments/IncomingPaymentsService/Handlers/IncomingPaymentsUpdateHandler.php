<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\FileRelationsRepositoryContract;
use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\DTO\IncomingPaymentDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class IncomingPaymentsUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private IncomingPaymentsRepositoryContract $repository,
        private FileRelationsRepositoryContract $fileRelationsRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof IncomingPaymentDTO) {

            $update = $dto->toUpdateArray();

            $resource = $this->repository->show($dto->resourceId);

            if ($resource->legal_entity_id !== $dto->legal_entity_id) {
                $documentNumberGenerator = new DocumentNumberGenerator(
                    'incoming_payments',
                    $dto->cabinet_id,
                    $dto->number,
                    $dto->legal_entity_id
                );
                $update['number'] = $documentNumberGenerator->generateNumber();
            }

            $this->repository->update(
                $dto->resourceId,
                $update
            );

            if ($dto->fileIds) {
                $fileids = collect($dto->fileIds);

                $fileids->map(function ($fileId) use ($dto) {
                    $fileId['related_id'] = $dto->resourceId;
                    $fileId['related_type'] = 'incoming_payments';
                    $fielId['file_id'] = $fileId;
                });

                $this->fileRelationsRepository->insert($fileids->toArray());
            }

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
