<?php

namespace App\Services\Api\Internal\GoodsTransferService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\FilesRepositoryContract;
use App\Contracts\Repositories\GoodsTransferRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\GoodsTransferService\DTO\GoodsTransferDto;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class GoodsTransferCreateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    private string $resourceId;

    public function __construct(
        private readonly GoodsTransferRepositoryContract $transfersRepository,
        private readonly DocumentsRepositoryContract $documentsRepository,
        protected readonly FilesRepositoryContract $filesRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof GoodsTransferDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }

        $documentNumberGenerator = new DocumentNumberGenerator(
            'goods_transfers',
            $dto->cabinetId,
            $dto->number,
            $dto->legalEntityId
        );
        $dto->number = $documentNumberGenerator->generateNumber();

        $this->transfersRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        $this->documentsRepository->insert(
            [
                'documentable_id' => $this->resourceId,
                'documentable_type' => 'goods_transfers',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $dto->cabinetId
            ]
        );

        $this->setRelationToFiles($this->resourceId, $dto->files, 'goods_transfers');

        return $this->resourceId;
    }
}
