<?php

namespace App\Services\Api\Internal\GoodsTransferService\Handlers;

use App\Contracts\Repositories\GoodsTransferRepositoryContract;

readonly class GoodsTransferShowHandler
{
    public function __construct(
        private GoodsTransferRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);
        if ($result) {
            $result->status = json_decode($result->status, true, 512, JSON_THROW_ON_ERROR);
            $result->to_warehouse = json_decode($result->to_warehouse, true, 512, JSON_THROW_ON_ERROR);
            $result->from_warehouse = json_decode($result->from_warehouse, true, 512, JSON_THROW_ON_ERROR);
        }
        return $result;
    }
}
