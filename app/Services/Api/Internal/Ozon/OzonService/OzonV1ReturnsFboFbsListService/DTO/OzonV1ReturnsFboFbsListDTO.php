<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV1ReturnsFboFbsListService\DTO;

use App\Contracts\DtoContract;

class OzonV1ReturnsFboFbsListDTO implements DtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $departmentId,
        public string $employeeId,
        public string $ozonCredentialId,
        public array $filter,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            departmentId: $data['department_id'],
            employeeId: $data['employee_id'],
            ozonCredentialId: $data['ozon_credential_id'],
            filter: $data['filter'],
        );
    }

}
