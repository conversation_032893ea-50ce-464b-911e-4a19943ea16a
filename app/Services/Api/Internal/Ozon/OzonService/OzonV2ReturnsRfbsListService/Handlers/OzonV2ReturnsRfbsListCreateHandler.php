<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV2ReturnsRfbsListService\Handlers;

use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsShowHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonV2ReturnsRfbsListService\DTO\OzonV2ReturnsRfbsListDTO;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class OzonV2ReturnsRfbsListCreateHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        protected OzonApiRequestHandler $ozonApiRequestHandler,
        protected OzonApiReturnsChunksHandler $ozonApiReturnsChunksHandler,
        protected OzonCredentialsShowHandler $ozonCredentialsShowHandler,
    ) {
    }

    public function run(OzonV2ReturnsRfbsListDTO $dto): void
    {

        $limit = 500; // max 500

        $ozonCredential = $this->ozonCredentialsShowHandler->run($dto->ozonCredentialId);

        $lastId = 0;
        $hasNext = true;

        while ($hasNext) {

            $response = $this->ozonApiRequestHandler->run($dto, $ozonCredential->client_id, $ozonCredential->api_key, $lastId, $limit);

            $lastId = $response['returns'] ? end($response['returns'])['return_id'] : 0;

            $hasNext = $response['has_next'] ?? false;

            $returns = new Collection($response['returns']);

            if ($returns->isNotEmpty()) {

                $this->ozonApiReturnsChunksHandler->run($returns, $limit, $dto->cabinetId, $dto->departmentId, $dto->employeeId, $ozonCredential->client_id);

            }

        }

    }
}
