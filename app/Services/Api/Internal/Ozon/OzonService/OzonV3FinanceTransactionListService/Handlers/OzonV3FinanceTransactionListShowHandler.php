<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\Handlers;

use App\Contracts\Repositories\OzonOrdersRepositoryContract;

class OzonV3FinanceTransactionListShowHandler
{
    public function __construct(
        private readonly OzonOrdersRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
