<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\Handlers;

use App\Contracts\Repositories\OzonV3FinanceTransactionItemsRepositoryContract;
use App\Contracts\Repositories\OzonV3FinanceTransactionListRepositoryContract;
use App\Contracts\Repositories\OzonV3FinanceTransactionServicesRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class OzonApiTransactionNumberChunksHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        private readonly OzonV3FinanceTransactionListRepositoryContract $repository,
        private readonly OzonV3FinanceTransactionItemsRepositoryContract $repositoryItems,
        private readonly OzonV3FinanceTransactionServicesRepositoryContract $repositoryServices,
    ) {
    }

    public function run(Collection $soldAmount, int $pageSize, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): void
    {
        $orderNumberChunks = $soldAmount->chunk($pageSize);

        $orderNumberChunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId) {
            $orders = [];
            $services = [];
            $transactionItems = [];

            $existingOrders = $this->repository->getOrdersByOperationIds($chunk->pluck('operation_id')->toArray());

            $chunk->each(function ($item) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId, &$orders, &$services, &$transactionItems, &$existingOrders) {
                $orderId = $existingOrders[$item['operation_id']] ?? $this->generateUuid();

                $orders[] = $this->prepareOrderData($item, $orderId, $cabinetId, $departmentId, $employeeId, $ozonCompanyId);
                $this->processTransactionItems($item, $orderId, $transactionItems);
                $this->processServices($item, $orderId, $services);
            });

            $this->saveProcessedData($orders, $transactionItems, $services);
        });
    }


    private function prepareOrderData($item, string $orderId, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): array
    {
        return [
            'id' => $orderId,
            'cabinet_id' => $cabinetId,
            'department_id' => $departmentId,
            'employee_id' => $employeeId,
            'ozon_company_id' => $ozonCompanyId,
            'operation_id' => $item['operation_id'],
            'operation_type' => $item['operation_type'],
            'operation_date' => $this->formatDate($item['operation_date'] ?? null),
            'operation_type_name' => $item['operation_type_name'],
            'delivery_charge' => $this->convertToKopecks($item['delivery_charge'] ?? null),
            'return_delivery_charge' => $this->convertToKopecks($item['return_delivery_charge'] ?? null),
            'accruals_for_sale' => $this->convertToKopecks($item['accruals_for_sale'] ?? null),
            'sale_commission' => $this->convertToKopecks($item['sale_commission'] ?? null),
            'amount' => $this->convertToKopecks($item['amount'] ?? null),
            'type' => $item['type'],
            'delivery_schema' => $item['posting']['delivery_schema'],
            'order_date' => $this->formatDate($item['posting']['order_date'] ?? null),
            'posting_number' => $item['posting']['posting_number'],
            'warehouse_id' => $item['posting']['warehouse_id'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    private function processTransactionItems($item, string $orderId, array &$transactionItems): void
    {
        $existingItems = $this->repositoryItems->getTransactionItemNamesAndSkus($orderId, $item);

        if (!empty($item['items'])) {
            foreach ($item['items'] as $items) {
                $key = $items['name'] . '|' . $items['sku'];
                if (!in_array($key, $existingItems)) {
                    $transactionItems[] = [
                        'id' => $this->generateUuid(),
                        'ozon_transaction_id' => $orderId,
                        'name' => $items['name'],
                        'sku' => $items['sku'],
                    ];
                }
            }
        }
    }

    private function processServices($item, string $orderId, array &$services): void
    {
        if (!empty($item['services'])) {
            foreach ($item['services'] as $service) {
                $services[] = [
                    'id' => $this->generateUuid(),
                    'ozon_transaction_id' => $orderId,
                    'name' => $service['name'],
                    'price' => $this->convertToKopecks($service['price'] ?? null),
                ];
            }
        }
    }

    private function saveProcessedData(array $orders, array $transactionItems, array $services): void
    {
        if (!empty($orders)) {
            $this->repository->upsert($orders);
        }
        if (!empty($transactionItems)) {
            $this->repositoryItems->insert($transactionItems);
        }
        if (!empty($services)) {
            $this->repositoryServices->upsert($services);
        }
    }


    private function formatDate(?string $date): ?string
    {
        return $date ? Carbon::parse($date)->format('Y-m-d H:i:s') : null;
    }

    private function convertToKopecks(?int $price): ?int
    {
        return isset($price) ? $this->rublesInKopeck($price) : null;
    }

}
