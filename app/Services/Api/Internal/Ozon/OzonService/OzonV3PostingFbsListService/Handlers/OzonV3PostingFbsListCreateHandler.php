<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\Handlers;

use App\Contracts\Repositories\OzonOrdersRepositoryContract;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsShowHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\DTO\OzonV3PostingFbsListDTO;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class OzonV3PostingFbsListCreateHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    protected array   $statuses = [
        'awaiting_registration',         //  ожидает регистрации,
        'acceptance_in_progress',        //  идёт приёмка,
        'awaiting_approve',              //  ожидает подтверждения,
        'awaiting_packaging',            //  ожидает упаковки,
        'awaiting_deliver',              //  ожидает отгрузки,
        'arbitration',                   //  арбитраж,
        'client_arbitration',            //  клиентский арбитраж доставки,
        'delivering',                    //  доставляется,
        'driver_pickup',                 //  у водителя,
        'delivered',                     //  доставлено,
        'cancelled',                     //  отменено,
        'not_accepted',                  //  не принят на сортировочном центре,
    ];

    public function __construct(
        protected OzonApiRequestHandler $ozonApiRequestHandler,
        protected OzonApiOrderNumberChunksHandler $ozonApiOrderNumberChunksHandler,
        protected OzonApiOrderItemsChunksHandler $ozonApiOrderItemsChunksHandler,
        protected OzonCredentialsShowHandler $ozonCredentialsShowHandler,
        private readonly OzonOrdersRepositoryContract $ozonOrdersRepositoryContract,
    ) {
    }

    public function run(OzonV3PostingFbsListDTO $dto): void
    {

        $limit = 1000; // от 0 до 1000

        $ozonCredential = $this->ozonCredentialsShowHandler->run($dto->ozonCredentialId);

        foreach ($this->statuses as $status) {

            $offset = 0;
            $hasNext = true;

            while ($hasNext) {

                $response = $this->ozonApiRequestHandler->run($status, $dto, $ozonCredential->client_id, $ozonCredential->api_key, $limit, $offset);

                $hasNext = $response['result']['has_next'] ?? false;

                $soldAmount = new Collection($response['result']['postings']);

                if ($soldAmount->isNotEmpty()) {

                    $this->ozonApiOrderNumberChunksHandler->run($soldAmount, $limit, $dto->cabinetId, $dto->departmentId, $dto->employeeId, $ozonCredential->client_id);

                    $orderIds = $this->ozonOrdersRepositoryContract->getAllOrderIds($dto->cabinetId); // Метод для получения всех записей вида [order_number => id]

                    $this->ozonApiOrderItemsChunksHandler->run($soldAmount, $limit, $orderIds);

                }

                $offset += $limit;

            }
        }

    }
}
