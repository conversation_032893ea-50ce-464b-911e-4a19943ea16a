<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService;

use App\Contracts\Services\Internal\OzonV3PostingFbsListServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\DTO\OzonV3PostingFbsListDTO;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\Handlers\OzonV3PostingFbsListCreateHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\Handlers\OzonV3PostingFbsListGetHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\Handlers\OzonV3PostingFbsListShowHandler;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

readonly class OzonV3PostingFbsListService implements OzonV3PostingFbsListServiceContract
{
    public function __construct(
        private OzonV3PostingFbsListGetHandler $getHandler,
        private OzonV3PostingFbsListCreateHandler $createHandler,
        private OzonV3PostingFbsListShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection|LengthAwarePaginator
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(OzonV3PostingFbsListDTO $dto): void
    {
        $this->createHandler->run($dto);
    }

}
