<?php

namespace App\Services\Api\Internal\ExcelImportService\Handlers;

use App\Traits\HasOrderedUuid;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EconomyProductsHandler
{
    use HasOrderedUuid;

    public function run(Worksheet $sheet): void
    {
        // Список ожидаемых колонок
        $expectedColumns = [
            'Артикул' => 'article',
            'Ozon SKU ID' => 'ozon_sku_id',
            'Название' => 'name',
            'Статус' => 'status',
            'Видимость на OZON' => 'visibility_on_ozon',
            'На складе Ozon' => 'stock_on_ozon',
            'Схема' => 'scheme',
            'Количество товаров в кванте' => 'quantity_of_goods_in_receipt',
            'Цена до скидки, руб.' => 'price_before_discount',
            'Текущая цена (со скидкой), руб.' => 'current_price_with_discount',
            'Минимальная цена, руб.' => 'minimal_price',
        ];

        $dynamicColumns = [
            'Цена до скидки, руб.' => 'price_before_discount_rub_dynamic',
            'Текущая цена (со скидкой), руб.' => 'current_price_with_discount_dynamic',
            'Минимальная цена, руб.' => 'minimal_price_dynamic',
        ];

        // Получение заголовков колонок из 3 строки
        $headers = [];
        $headerRow = $sheet->getRowIterator(3, 3)->current();

        foreach ($headerRow->getCellIterator() as $cell) {
            $headers[] = trim($cell->getValue());
        }

        // Получение динамических колонок из первой строки
        $dynamicHeaders = [];
        $dynamicHeaderRow = $sheet->getRowIterator(1, 1)->current();
        $dynamicHeaderCells = $dynamicHeaderRow->getCellIterator();
        $dynamicHeaderCells->setIterateOnlyExistingCells(false);
        $startColumn = null;

        foreach ($dynamicHeaderCells as $cell) {
            $value = $cell->getValue();
            if ($value == 'Новые цены (редактируйте только поля в зеленых столбцах)') {
                if ($startColumn === null) {
                    $startColumn = $cell->getColumn();
                }
            }
        }

        $endColumn = $sheet->getHighestColumn();

        for ($col = $startColumn; $col <= $endColumn; $col++) {
            $dynamicHeaders[] = $sheet->getCell($col . '3')->getValue();
        }

        // Сопоставление динамических заголовков с динамическими колонками
        $dynamicColumnMapping = [];
        foreach ($dynamicColumns as $dynamicHeader => $columnKey) {
            $columnIndex = array_search($dynamicHeader, $dynamicHeaders);
            if ($columnIndex !== false) {
                $dynamicColumnMapping[$columnKey] = Coordinate::stringFromColumnIndex(
                    Coordinate::columnIndexFromString($startColumn) + $columnIndex
                );
            }
        }

        // Сопоставление заголовков с ожидаемыми колонками
        $columnMapping = [];
        foreach ($expectedColumns as $expectedHeader => $columnKey) {
            $columnIndex = array_search($expectedHeader, $headers);
            if ($columnIndex !== false) {
                $columnMapping[$columnKey] = Coordinate::stringFromColumnIndex($columnIndex + 1);
            }
        }

        // Чтение данных и присвоение значений
        $data = [];
        $highestRow = $sheet->getHighestRow();
        for ($row = 5; $row <= $highestRow; $row++) {
            $rowData = [];
            foreach ($columnMapping as $columnKey => $columnLetter) {
                $rowData[$columnKey] = $sheet->getCell($columnLetter . $row)->getValue();
            }
            foreach ($dynamicColumnMapping as $columnKey => $columnLetter) {
                $rowData[$columnKey] = $sheet->getCell($columnLetter . $row)->getValue();
            }
            if (!empty(array_filter($rowData))) {
                $data[] = $rowData;
            }
        }

        //TODO ещё в разработке
        dd($data);

        // $this->repository->insert($dto->toInsertArray($this->resourceId));

        // return $data;
        // return $this->resourceId;

    }

}
