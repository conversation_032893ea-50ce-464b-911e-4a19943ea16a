<?php

namespace App\Services\Api\Internal\ExcelImportService\Handlers;

use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CompensationReportHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(

    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(Worksheet $sheet): void
    {

        $dataTitle = [
            'report_compensation_number' => 'B2',   // Отчет о компенсациях № 813460 от 30.11.2024
            'agreement_offer_number' => 'B3',       // по Договору оферты для Продавцов на Платформе OZON № ИР-33937/21 от 27.04.2021
            'payer_name' => 'B6',                   // ООО "Интернет Решения"  Плательщик
            'payer_inn' => 'C7',                    // ИНН	7704217370
            'payer_kpp' => 'C8',                    // КПП	997750001
            'recipient_name' => 'G6',               // Пикин Андрей Сергеевич, ИП  Получатель
            'recipient_inn' => 'H7',                // ИНН	************
            'recipient_kpp' => 'H8',                // КПП
        ];

        $columnsColumnMapping = [
            'number_row' => 'B',            // № п/п
            'compensation_type' => 'C',     // Тип компенсации
            'product' => 'D',               // Товар
            'article' => 'E',               // Артикул
            'sku' => 'F',                   // SKU
            'barcode' => 'G',               // Штрих-код
            'quantity' => 'H',              // Кол-во
            'actual_product_cost' => 'I',   // Действительная стоимость товара
            'actual_total_cost' => 'J',     // "Действительная стоимость - всего
            'reduced_by_amount' => 'K',     // "Уменьшено на сумму
            'total_to_be_credited' => 'L',  // "Итого к начислению
        ];

        $parsedData = [];
        foreach ($dataTitle as $key => $cell) {
            $parsedData[$key] = $sheet->getCell($cell)->getValue();
        }

        // Чтение данных и присвоение значений
        $data = [];

        $data['dataTitle'] = $parsedData;
        $highestRow = $sheet->getHighestRow();
        // dd( $highestRow);
        for ($row = 13; $row <= $highestRow; $row++) {
            $rowData = [];
            foreach ($columnsColumnMapping as $columnKey => $columnLetter) {
                if (empty($sheet->getCell($columnLetter . $row)->getValue())) {
                    break;
                }
                $rowData[$columnKey] = (string)$sheet->getCell($columnLetter . $row)->getValue();
            }
            if (!empty(array_filter($rowData))) {
                $data['item'][] = $rowData;
            }
        }
        $data['item'] = array_slice($data['item'], 0, -1);

        $sold_amount = new Collection($data['item']);

        $data['find'] = $sold_amount->where('number_row', 2)->first();

        //TODO ещё в разработке
        dd($data);

        // $this->repository->insert($dto->toInsertArray($this->resourceId));

        // return $data;
        // return $this->resourceId;

    }

}
