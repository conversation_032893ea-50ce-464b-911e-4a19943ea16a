<?php

namespace App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers;

use App\Contracts\Repositories\ContractorGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class ContractorGroupsGetHandler
{
    public function __construct(
        private ContractorGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
