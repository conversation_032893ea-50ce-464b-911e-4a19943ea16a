<?php

namespace App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers;

use App\Contracts\Repositories\ContractorGroupsRepositoryContract;

readonly class ContractorGroupsDeleteHandler
{
    public function __construct(
        private ContractorGroupsRepositoryContract $repository
    ) {
    }

    public function run(string $id): void
    {
        $this->repository->delete($id);
    }
}
