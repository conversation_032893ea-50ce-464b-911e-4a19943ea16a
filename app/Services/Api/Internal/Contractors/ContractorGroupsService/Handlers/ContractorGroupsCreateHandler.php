<?php

namespace App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ContractorGroupsRepositoryContract;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\DTO\ContractorGroupsDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class ContractorGroupsCreateHandler
{
    use HasOrderedUuid;

    public string $resourceId;

    public function __construct(
        private ContractorGroupsRepositoryContract $repository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ContractorGroupsDTO) {
            throw new InvalidArgumentException();
        }

        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
