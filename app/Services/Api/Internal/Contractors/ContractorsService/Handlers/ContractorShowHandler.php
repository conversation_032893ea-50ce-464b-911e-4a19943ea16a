<?php

namespace App\Services\Api\Internal\Contractors\ContractorsService\Handlers;

use App\Contracts\Repositories\ContractorsRepositoryContract;

readonly class ContractorShowHandler
{
    public function __construct(
        private ContractorsRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        $result = $this->repository->show($id);
        $result->detail = json_decode($result->detail, true);
        $result->accounts = json_decode($result->accounts, true);
        $result->address = json_decode($result->address, true);
        $result->contacts = json_decode($result->contacts, true);
        $result->contractor_groups = json_decode($result->contractor_groups, true);
        $result->files = json_decode($result->files, true);

        return $result;
    }
}
