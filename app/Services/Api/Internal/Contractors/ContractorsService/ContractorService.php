<?php

namespace App\Services\Api\Internal\Contractors\ContractorsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Contractors\ContractorsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorBulkCopyHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorBulkDeleteHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorCreateHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorDeleteHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorGetHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorShowHandler;
use App\Services\Api\Internal\Contractors\ContractorsService\Handlers\ContractorUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class ContractorService implements ContractorsServiceContract
{
    public function __construct(
        private ContractorCreateHandler $createHandler,
        private ContractorGetHandler $getHandler,
        private ContractorDeleteHandler $deleteHandler,
        private ContractorShowHandler $showHandler,
        private ContractorUpdateHandler $updateHandler,
        private ContractorBulkCopyHandler $bulkCopyHandler,
        private ContractorBulkDeleteHandler $bulkDeleteHandler
    ) {
    }
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function bulkCopy(array $ids): void
    {
        $this->bulkCopyHandler->run($ids);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }
}
