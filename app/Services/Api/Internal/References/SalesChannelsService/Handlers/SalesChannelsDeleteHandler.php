<?php

namespace App\Services\Api\Internal\References\SalesChannelsService\Handlers;

use App\Contracts\Repositories\SalesChannelsRepositoryContract;

readonly class SalesChannelsDeleteHandler
{
    public function __construct(
        private SalesChannelsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
