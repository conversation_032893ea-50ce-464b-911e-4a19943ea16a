<?php

namespace App\Services\Api\Internal\References\SalesChannelsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Services\Api\Internal\References\SalesChannelsService\DTO\SalesChannelDTO;
use App\Traits\HasOrderedUuid;

class SalesChannelsUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly SalesChannelsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof SalesChannelDTO) {
            throw new \InvalidArgumentException();
        }
        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );
    }
}
