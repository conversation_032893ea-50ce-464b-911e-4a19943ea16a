<?php

namespace App\Services\Api\Internal\References\CountriesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\CountriesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\CountriesService\Handlers\CountiresDeleteHandler;
use App\Services\Api\Internal\References\CountriesService\Handlers\CountriesCreateHandler;
use App\Services\Api\Internal\References\CountriesService\Handlers\CountriesGetHandler;
use App\Services\Api\Internal\References\CountriesService\Handlers\CountriesShowHandler;
use App\Services\Api\Internal\References\CountriesService\Handlers\CountriesUpdateHandler;
use App\Services\Api\Internal\References\CountriesService\Handlers\FillSystemCountriesHandler;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class CountryService implements CountriesServiceContract
{
    use HasOrderedUuid;
    public function __construct(
        private readonly CountriesGetHandler $getHandler,
        private readonly CountriesCreateHandler $createHandler,
        private readonly CountriesShowHandler $showHandler,
        private readonly CountriesUpdateHandler $updateHandler,
        private readonly CountiresDeleteHandler $deleteHandler,
        private readonly FillSystemCountriesHandler $fillSystemHandler
    ) {
    }

    /**
     * @throws \JsonException
     */
    public function fillSystem(): void
    {
        $this->fillSystemHandler->run();
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
