<?php

namespace App\Services\Api\Internal\References\CountriesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\CountriesRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Services\Api\Internal\References\CountriesService\DTO\CountryDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class CountriesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly CountriesRepositoryContract $repository,
        private readonly EmployeeRepositoryContract $employeeRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof CountryDTO) {
            throw new InvalidArgumentException();
        }
        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
