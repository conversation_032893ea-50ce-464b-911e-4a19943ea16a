<?php

namespace App\Services\Api\Internal\References\CountriesService\Handlers;

use App\Contracts\Repositories\CountriesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class CountriesGetHandler
{
    public function __construct(
        private CountriesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
