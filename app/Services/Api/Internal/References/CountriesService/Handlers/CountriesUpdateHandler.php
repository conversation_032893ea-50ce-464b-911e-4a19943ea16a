<?php

namespace App\Services\Api\Internal\References\CountriesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\CountriesRepositoryContract;
use App\Services\Api\Internal\References\CountriesService\DTO\CountryDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class CountriesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CountriesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof CountryDTO) {
            throw new InvalidArgumentException('Expected CountryDTO');
        }

        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );
    }
}
