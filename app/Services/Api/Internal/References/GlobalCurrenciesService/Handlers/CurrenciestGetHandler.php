<?php

namespace App\Services\Api\Internal\References\GlobalCurrenciesService\Handlers;

use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class CurrenciestGetHandler
{
    public function __construct(
        private GlobalCurrenciesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->getAll(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
