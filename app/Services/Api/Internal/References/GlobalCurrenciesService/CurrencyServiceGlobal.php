<?php

namespace App\Services\Api\Internal\References\GlobalCurrenciesService;

use App\Contracts\Services\Internal\Directories\GlobalCurrenciesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\References\GlobalCurrenciesService\Handlers\CurrenciesShowHandler;
use App\Services\Api\Internal\References\GlobalCurrenciesService\Handlers\CurrenciestGetHandler;
use Illuminate\Support\Collection;

readonly class CurrencyServiceGlobal implements GlobalCurrenciesServiceContract
{
    public function __construct(
        private CurrenciestGetHandler $getHandler,
        private CurrenciesShowHandler $showHandler,
    ) {
    }

    public function index(IndexRequestDTO $dto): Collection
    {
        return $this->getHandler->run($dto);
    }

    /**
     * @throws NotFoundException
     */
    public function show(string $resourceId): ?object
    {
        return $this->showHandler->run($resourceId);
    }
}
