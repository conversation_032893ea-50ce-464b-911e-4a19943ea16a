<?php

namespace App\Services\Api\Internal\References\CabinetPricesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class CabinetPriceDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $name,
        public ?string $resourceId,
        public int $sort = 0
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'sort' => $this->sort
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'sort' => $this->sort
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['resource_id'] ?? null,
            sort: $data['sort'] ?? 0
        );
    }
}
