<?php

namespace App\Services\Api\Internal\References\CabinetPricesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Finances\CabinetPricesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\CabinetPricesService\Handlers\CabinetPriceCreateHandler;
use App\Services\Api\Internal\References\CabinetPricesService\Handlers\CabinetPriceDeleteHandler;
use App\Services\Api\Internal\References\CabinetPricesService\Handlers\CabinetPriceGetHandler;
use App\Services\Api\Internal\References\CabinetPricesService\Handlers\CabinetPriceShowHandler;
use App\Services\Api\Internal\References\CabinetPricesService\Handlers\CabinetPriceUpdateHandler;
use Illuminate\Support\Collection;

readonly class CabinetPricesService implements CabinetPricesServiceContract
{
    public function __construct(
        private CabinetPriceGetHandler $getHandler,
        private CabinetPriceCreateHandler $createHandler,
        private CabinetPriceUpdateHandler $updateHandler,
        private CabinetPriceDeleteHandler $deleteHandler,
        private CabinetPriceShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }
}
