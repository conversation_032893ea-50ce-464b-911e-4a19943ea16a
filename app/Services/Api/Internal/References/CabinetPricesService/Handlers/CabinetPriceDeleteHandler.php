<?php

namespace App\Services\Api\Internal\References\CabinetPricesService\Handlers;

use App\Contracts\Repositories\CabinetPricesRepositoryContract;

readonly class CabinetPriceDeleteHandler
{
    public function __construct(
        private CabinetPricesRepositoryContract $repository
    ) {
    }
    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
