<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\CabinetCurrenciesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyBulkDeleteHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyCreateHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyDeleteHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyGetHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencySetAccoutingHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyShowHandler;
use App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers\CabinetCurrencyUpdateHandler;
use Illuminate\Support\Collection;

readonly class CabinetCurrencyService implements CabinetCurrenciesServiceContract
{
    public function __construct(
        private CabinetCurrencyGetHandler $getHandler,
        private CabinetCurrencyCreateHandler $createHandler,
        private CabinetCurrencyUpdateHandler $updateHandler,
        private CabinetCurrencyDeleteHandler $deleteHandler,
        private CabinetCurrencyShowHandler $showHandler,
        private CabinetCurrencyBulkDeleteHandler $bulkDeleteHandler,
        private CabinetCurrencySetAccoutingHandler $setAccoutingHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }

    public function setAccounting(array $data): void
    {
        $this->setAccoutingHandler->run($data);
    }
}
