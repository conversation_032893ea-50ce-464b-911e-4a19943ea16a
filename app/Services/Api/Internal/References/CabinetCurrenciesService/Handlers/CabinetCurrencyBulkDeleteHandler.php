<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;

readonly class CabinetCurrencyBulkDeleteHandler
{
    public function __construct(
        private CabinetCurrenciesRepositoryContract $repository,
    ) {
    }

    public function run(array $ids): void
    {
        $this->repository->deleteWhereIn($ids);
    }
}
