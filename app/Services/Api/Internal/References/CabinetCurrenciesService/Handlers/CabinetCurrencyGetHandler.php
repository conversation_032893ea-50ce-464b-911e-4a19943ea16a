<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class CabinetCurrencyGetHandler
{
    public function __construct(
        private CabinetCurrenciesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
