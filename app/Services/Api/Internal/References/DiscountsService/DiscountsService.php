<?php

namespace App\Services\Api\Internal\References\DiscountsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\DiscountsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsCreateHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsDeleteHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsGetGroupsHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsGetHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsGetProductsHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsGetSavingsHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsShowHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsStatusHandler;
use App\Services\Api\Internal\References\DiscountsService\Handlers\DiscountsUpdateHandler;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

readonly class DiscountsService implements DiscountsServiceContract
{
    public function __construct(
        private DiscountsGetHandler $getHandler,
        private DiscountsCreateHandler $createHandler,
        private DiscountsUpdateHandler $updateHandler,
        private DiscountsDeleteHandler $deleteHandler,
        private DiscountsShowHandler $showHandler,
        private DiscountsStatusHandler $setStatusHandler,
        private DiscountsGetProductsHandler $getProducts,
        private DiscountsGetGroupsHandler $getGroups,
        private DiscountsGetSavingsHandler $getSavings
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function setStatus(string $id, Request $request): int
    {
        return $this->setStatusHandler->run($id, $request);
    }

    public function getProducts(string $id): ?object
    {
        return $this->getProducts->run($id);
    }

    public function getGroups(string $id): ?object
    {
        return $this->getGroups->run($id);
    }

    public function getSavings(string $id): ?object
    {
        return $this->getSavings->run($id);
    }
}
