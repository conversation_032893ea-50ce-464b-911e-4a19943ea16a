<?php

namespace App\Services\Api\Internal\References\ProfitTaxRatesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Traits\HasOrderedUuid;

class ProfitTaxRatesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProfitTaxRatesRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
