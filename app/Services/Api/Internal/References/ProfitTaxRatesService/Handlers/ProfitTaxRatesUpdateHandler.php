<?php

namespace App\Services\Api\Internal\References\ProfitTaxRatesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Services\Api\Internal\References\ProfitTaxRatesService\DTO\ProfitTaxRateDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class ProfitTaxRatesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProfitTaxRatesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ProfitTaxRateDTO) {
            throw new InvalidArgumentException();
        }
        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );
    }
}
