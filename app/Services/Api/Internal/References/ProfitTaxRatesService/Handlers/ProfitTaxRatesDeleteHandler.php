<?php

namespace App\Services\Api\Internal\References\ProfitTaxRatesService\Handlers;

use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;

readonly class ProfitTaxRatesDeleteHandler
{
    public function __construct(
        private ProfitTaxRatesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
