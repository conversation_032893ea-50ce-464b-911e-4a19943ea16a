<?php

namespace App\Services\Api\Internal\References\ProfitTaxRatesService\Handlers;

use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;

readonly class ProfitTaxRatesShowHandler
{
    public function __construct(
        private ProfitTaxRatesRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
