<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\Handlers;

use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Traits\HasOrderedUuid;

class LegalEntityDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly LegalEntitiesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
