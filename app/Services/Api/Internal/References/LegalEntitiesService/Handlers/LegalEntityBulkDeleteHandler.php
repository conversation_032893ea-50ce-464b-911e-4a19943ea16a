<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\Handlers;

use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Traits\HasOrderedUuid;

class LegalEntityBulkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly LegalEntitiesRepositoryContract $repository
    ) {
    }

    public function run(array $ids): void
    {
        $this->repository->deleteWhereIn($ids);
    }
}
