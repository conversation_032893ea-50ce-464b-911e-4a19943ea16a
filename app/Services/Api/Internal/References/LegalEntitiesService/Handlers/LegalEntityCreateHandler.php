<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Services\Api\Internal\References\LegalEntitiesService\DTO\LegalEntityDTO;
use App\Services\Api\Internal\References\LegalEntitiesService\Traits\LegalEntitiesManages;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class LegalEntityCreateHandler
{
    use HasOrderedUuid;
    use LegalEntitiesManages;

    private string $resourceId;

    public function __construct(
        private readonly LegalEntitiesRepositoryContract $repository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof LegalEntityDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $this->repository->insert($dto->toInsertArray($this->resourceId));

        $this->addressHandle($dto);

        $this->headHandle($dto);

        $this->detailHandle($dto);

        if ($dto->accounts) {
            $this->manageAccounts($dto->accounts);
        }

        return $this->resourceId;
    }

    /**
     * @param LegalEntityDTO $dto
     * @return void
     */
    public function addressHandle(LegalEntityDTO $dto): void
    {
        if ($dto->address) {
            DB::table('legal_addresses') // Создание адреса
            ->insert(
                array_merge(
                    $dto->address,
                    [
                        'id' => $this->generateUuid(),
                        'legal_entity_id' => $this->resourceId,
                        'created_at' => Carbon::now()
                    ],
                )
            );
        }
    }

    /**
     * @param LegalEntityDTO $dto
     * @return void
     */
    public function headHandle(LegalEntityDTO $dto): void
    {
        if ($dto->head) {
            DB::table('legal_heads') // Создание информации о руководителе, бухгалтере, подписей и т.п.
            ->insert(
                array_merge(
                    $dto->head,
                    [
                        'id' => $this->generateUuid(),
                        'legal_entity_id' => $this->resourceId,
                        'created_at' => Carbon::now()
                    ],
                )
            );
        }
    }

    /**
     * @param LegalEntityDTO $dto
     * @return void
     */
    public function detailHandle(LegalEntityDTO $dto): void
    {
        if ($dto->detail) {
            $detailId = $this->generateUuid();

            DB::table('legal_details')  // Юридические реквизиты
            ->insert([
                'id' => $detailId,
                'legal_entity_id' => $this->resourceId,
                'type' => $dto->detail['type'],
                'prefix' => $dto->detail['prefix'] ?? null,
                'inn' => $dto->detail['inn'] ?? null,
                'kpp' => $dto->detail['kpp'] ?? null,
                'ogrn' => $dto->detail['ogrn'] ?? null,
                'okpo' => $dto->detail['okpo'] ?? null,
                'ogrnip' => $dto->detail['ogrnip'] ?? null,
                'full_name' => $dto->detail['full_name'] ?? null,
                'firstname' => $dto->detail['firstname'] ?? null,
                'lastname' => $dto->detail['lastname'] ?? null,
                'patronymic' => $dto->detail['patronymic'] ?? null,
                'certificate_number' => $dto->detail['certificate_number'] ?? null,
                'certificate_date' => $dto->detail['certificate_date'] ?? null,
                'taxation_type' => $dto->detail['taxation_type'],
                'tax_rate' => $dto->detail['tax_rate'] ?? null,
                'vat_rate' => $dto->detail['vat_rate'] ?? null,
                'created_at' => Carbon::now()
            ]);

            if (isset($dto->detail['address'])) {
                DB::table('legal_detail_addresses') // Адрес юр. лица
                ->insert(
                    array_merge(
                        $dto->detail['address'],
                        [
                            'id' => $this->generateUuid(),
                            'legal_detail_id' => $detailId,
                            'created_at' => Carbon::now()
                        ],
                    )
                );
            }
        }
    }
}
