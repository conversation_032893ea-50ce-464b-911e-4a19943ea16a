<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Services\Api\Internal\References\LegalEntitiesService\DTO\LegalEntityDTO;
use App\Services\Api\Internal\References\LegalEntitiesService\Traits\LegalEntitiesManages;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class LegalEntityUpdateHandler
{
    use HasOrderedUuid;
    use LegalEntitiesManages;

    public string $resourceId;

    public function __construct(
        private readonly LegalEntitiesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof LegalEntityDTO) {
            throw new InvalidArgumentException();
        }
        $this->resourceId = $dto->resourceId;

        $updatedAt = ['updated_at' => Carbon::now()];

        $this->repository->update($dto->resourceId, $dto->toUpdateArray());

        $this->addressHandle($dto, $updatedAt);

        $this->headHandle($dto);

        $this->detailHandle($dto, $updatedAt);

        if ($dto->accounts) {
            $this->manageAccounts($dto->accounts);
        }
    }

    /**
     * @param LegalEntityDTO $dto
     * @return void
     */
    public function headHandle(LegalEntityDTO $dto): void
    {
        DB::table('legal_heads') // Создание информации о руководителе, бухгалтере, подписей и т.п.
            ->upsert(
                [
                    'id' => $this->generateUuid(),
                    'legal_entity_id' => $this->resourceId,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'head_name' => $dto->head['head_name'] ?? null,
                    'head_position' => $dto->head['head_position'] ?? null,
                    'accountant_name' => $dto->head['accountant_name'] ?? null,
                    'head_signature_image_id' => $dto->head['head_signature_image_id'] ?? null,
                    'accountant_signature_image_id' => $dto->head['accountant_signature_image_id'] ?? null,
                    'stamp_image_id' => $dto->head['stamp_image_id'] ?? null,
                ],
                ['legal_entity_id'],
                [
                'updated_at',
                'head_name', 'head_position',
                'accountant_name',
                'head_signature_image_id', 'accountant_signature_image_id', 'stamp_image_id'
            ]
            );
    }

    /**
     * @param LegalEntityDTO $dto
     * @param array $updatedAt
     * @return void
     */
    public function detailHandle(LegalEntityDTO $dto, array $updatedAt): void
    {
        $legalDetailId = DB::table('legal_details')
            ->where('legal_entity_id', $dto->resourceId)
            ->value('id');

        DB::table('legal_details')  // Юридические реквизиты
        ->where('id', $legalDetailId)
            ->update([
                'type' => $dto->detail['type'],
                'prefix' => $dto->detail['prefix'] ?? null,
                'inn' => $dto->detail['inn'] ?? null,
                'kpp' => $dto->detail['kpp'] ?? null,
                'ogrn' => $dto->detail['ogrn'] ?? null,
                'okpo' => $dto->detail['okpo'] ?? null,
                'ogrnip' => $dto->detail['ogrnip'] ?? null,
                'full_name' => $dto->detail['full_name'] ?? null,
                'firstname' => $dto->detail['firstname'] ?? null,
                'lastname' => $dto->detail['lastname'] ?? null,
                'patronymic' => $dto->detail['patronymic'] ?? null,
                'certificate_number' => $dto->detail['certificate_number'] ?? null,
                'certificate_date' => $dto->detail['certificate_date'] ?? null,
                'taxation_type' => $dto->detail['taxation_type'] ?? null,
                'tax_rate' => $dto->detail['tax_rate'] ?? null,
                'vat_rate' => $dto->detail['vat_rate'] ?? null,
                'updated_at' => Carbon::now()
            ]);


        DB::table('legal_detail_addresses')
            ->upsert(
                [
                    'id' => $this->generateUuid(),
                    'legal_detail_id' => $legalDetailId,
                    'postcode' => $dto->detail['address']['postcode'] ?? null,
                    'country' => $dto->detail['address']['country'] ?? null,
                    'region' => $dto->detail['address']['region'] ?? null,
                    'city' => $dto->detail['address']['city'] ?? null,
                    'street' => $dto->detail['address']['street'] ?? null,
                    'house' => $dto->detail['address']['house'] ?? null,
                    'office' => $dto->detail['address']['office'] ?? null,
                    'other' => $dto->detail['address']['other'] ?? null,
                    'comment' => $dto->detail['address']['comment'] ?? null,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ],
                ['legal_detail_id'],
                [
                    'postcode','country','region','city','street','house','office',
                    'other','comment',
                    'updated_at'
                ]
            );
    }

    /**
     * @param LegalEntityDTO $dto
     * @param array $updatedAt
     * @return void
     */
    public function addressHandle(LegalEntityDTO $dto, array $updatedAt): void
    {
        DB::table('legal_addresses') // Создание адреса
        ->upsert(
            [
                'id' => $this->generateUuid(),
                'legal_entity_id' => $this->resourceId,
                'postcode' => $dto->address['postcode'] ?? null,
                'country' => $dto->address['country'] ?? null,
                'region' => $dto->address['region'] ?? null,
                'city' => $dto->address['city'] ?? null,
                'street' => $dto->address['street'] ?? null,
                'house' => $dto->address['house'] ?? null,
                'office' => $dto->address['office'] ?? null,
                'other' => $dto->address['other'] ?? null,
                'comment' => $dto->address['comment'] ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            ['legal_entity_id'],
            [
                'postcode','country','region','city','street','house','office',
                'other','comment',
                'updated_at'
            ]
        );
    }
}
