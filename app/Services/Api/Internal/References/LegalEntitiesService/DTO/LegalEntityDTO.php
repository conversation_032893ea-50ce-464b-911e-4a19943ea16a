<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class LegalEntityDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $departmentId,
        public ?string $resourceId,
        public ?string $logo,
        public string $shortName,
        public ?string $code,
        public ?string $phone,
        public ?string $fax,
        public ?string $email,
        public ?string $discountCard,
        public array $address,
        public array $head,
        public array $detail,
        public array $accounts,
        public string $employeeId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'short_name' => $this->shortName,
            'code' => $this->code,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'logo_image_id' => $this->logo,
            'discount_card' => $this->discountCard
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'short_name' => $this->shortName,
            'code' => $this->code,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'logo_image_id' => $this->logo,
            'discount_card' => $this->discountCard
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            departmentId: $data['department_id'],
            resourceId: $data['resource_id'] ?? null,
            logo: $data['logo_image_id'] ?? null,
            shortName: $data['short_name'],
            code: $data['code'] ?? null,
            phone: $data['phone'] ?? null,
            fax: $data['fax'] ?? null,
            email: $data['email'] ?? null,
            discountCard: $data['discount_card'] ?? null,
            address: $data['address'] ?? [],
            head: $data['head'] ?? [],
            detail: $data['detail'] ?? [],
            accounts: $data['accounts'] ?? [],
            employeeId: $data['employee_id'],
        );
    }
}
