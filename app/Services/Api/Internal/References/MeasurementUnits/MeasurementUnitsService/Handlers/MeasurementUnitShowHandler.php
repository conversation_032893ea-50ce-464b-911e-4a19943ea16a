<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\Handlers;

use App\Contracts\Repositories\MeasurementUnitsRepositoryContract;

readonly class MeasurementUnitShowHandler
{
    public function __construct(
        private MeasurementUnitsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
