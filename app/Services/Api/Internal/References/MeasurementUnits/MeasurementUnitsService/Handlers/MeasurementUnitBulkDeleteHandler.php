<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\Handlers;

use App\Contracts\Repositories\MeasurementUnitsRepositoryContract;

readonly class MeasurementUnitBulkDeleteHandler
{
    public function __construct(
        private MeasurementUnitsRepositoryContract $repository,
    ) {
    }

    public function run(array $ids): void
    {
        $unitsByGroup = collect($this->repository->getUnitsByIds($ids))->groupBy('group_id');

        foreach ($unitsByGroup as $groupId => $units) {
            $baseUnit = $this->repository->findBaseUnitInGroup($groupId);

            if ($baseUnit && $units->contains('id', $baseUnit->id)) {
                $otherUnits = collect($this->repository->getOtherUnitsInGroup($groupId, $baseUnit->id));

                if ($otherUnits->isNotEmpty()) {
                    $newBaseUnit = $otherUnits->first();
                    $newBaseFactor = $newBaseUnit->conversion_factor;

                    foreach ($otherUnits->skip(1) as $unit) {
                        $this->repository->update($unit->id, [
                            'conversion_factor' => $unit->conversion_factor / $newBaseFactor
                        ]);
                    }

                    $this->repository->update($newBaseUnit->id, [
                        'conversion_factor' => 1
                    ]);
                }
            }
        }

        $this->repository->deleteWhereIn($ids);
    }
}
