<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers;

use App\Contracts\Repositories\MeasurementUnitGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class MeasurementUnitGroupGetHandler
{
    public function __construct(
        private MeasurementUnitGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );

    }
}
