<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\MeasurementUnitGroupsRepositoryContract;
use App\Services\Api\Internal\References\LegalEntitiesService\Traits\LegalEntitiesManages;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\DTO\MeasurementUnitGroupDTO;
use App\Traits\HasOrderedUuid;
use Exception;
use InvalidArgumentException;

class MeasurementUnitGroupUpdateHandler
{
    use HasOrderedUuid;
    use LegalEntitiesManages;

    public string $resourceId;

    public function __construct(
        private readonly MeasurementUnitGroupsRepositoryContract $repository
    ) {
    }

    /**
     * @throws Exception
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof MeasurementUnitGroupDTO) {
            throw new InvalidArgumentException('DTO must be instance of MeasurementUnitDTO');
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
