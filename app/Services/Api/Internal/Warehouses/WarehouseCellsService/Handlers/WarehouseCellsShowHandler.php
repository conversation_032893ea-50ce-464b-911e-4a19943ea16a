<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellsService\Handlers;

use App\Contracts\Repositories\WarehouseCellsRepositoryContract;

readonly class WarehouseCellsShowHandler
{
    public function __construct(
        private WarehouseCellsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
