<?php

namespace App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers;

use App\Contracts\Repositories\WarehousePhonesRepositoryContract;
use App\Traits\HasOrderedUuid;

class WarehousePhonesShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly WarehousePhonesRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
