<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseGroups;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseGroupsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers\WarehouseGroupsCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers\WarehouseGroupsDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers\WarehouseGroupsGetHandler;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers\WarehouseGroupsShowHandler;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers\WarehouseGroupsUpdateHandler;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class WarehouseGroupService implements WarehouseGroupsServiceContract
{
    use HasOrderedUuid;

    public function __construct(
        private readonly WarehouseGroupsGetHandler $getHandler,
        private readonly WarehouseGroupsCreateHandler $createHandler,
        private readonly WarehouseGroupsUpdateHandler $updateHandler,
        private readonly WarehouseGroupsDeleteHandler $deleteHandler,
        private readonly WarehouseGroupsShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
