<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseGroupsRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\DTO\WarehouseGroupDTO;

readonly class WarehouseGroupsUpdateHandler
{
    public function __construct(
        private WarehouseGroupsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseGroupDTO) {
            return;
        }

        $updateData = $dto->toUpdateArray();

        if (isset($dto->parent_id)) {
            $hasChild = $this->repository->getByParentId($dto->resourceId);

            if ($hasChild->isNotEmpty()) {
                foreach ($hasChild as $child) {
                    if ($child->id === $dto->parent_id) {
                        throw new \Exception('Нельзя сделать группу дочерней к своей дочерней группе');
                    }
                }
            }
        }

        $this->repository->update($dto->resourceId, $updateData);
    }
} 