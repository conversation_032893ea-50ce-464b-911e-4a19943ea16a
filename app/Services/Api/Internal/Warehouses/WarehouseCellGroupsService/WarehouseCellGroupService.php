<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseCellGroupsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers\WarehouseCellGroupsCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers\WarehouseCellGroupsDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers\WarehouseCellGroupsGetHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers\WarehouseCellGroupsShowHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers\WarehouseCellGroupsUpdateHandler;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class WarehouseCellGroupService implements WarehouseCellGroupsServiceContract
{
    use HasOrderedUuid;

    public function __construct(
        private readonly WarehouseCellGroupsGetHandler $getHandler,
        private readonly WarehouseCellGroupsCreateHandler $createHandler,
        private readonly WarehouseCellGroupsUpdateHandler $updateHandler,
        private readonly WarehouseCellGroupsDeleteHandler $deleteHandler,
        private readonly WarehouseCellGroupsShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
