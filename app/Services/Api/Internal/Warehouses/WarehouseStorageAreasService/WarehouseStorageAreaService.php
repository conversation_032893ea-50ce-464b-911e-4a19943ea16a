<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseStorageAreasServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers\WarehouseStorageAreasCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers\WarehouseStorageAreasDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers\WarehouseStorageAreasGetHandler;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers\WarehouseStorageAreasShowHandler;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers\WarehouseStorageAreasUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehouseStorageAreaService implements WarehouseStorageAreasServiceContract
{
    public function __construct(
        private WarehouseStorageAreasCreateHandler $createHandler,
        private WarehouseStorageAreasUpdateHandler $updateHandler,
        private WarehouseStorageAreasDeleteHandler $deleteHandler,
        private WarehouseStorageAreasGetHandler $getHandler,
        private WarehouseStorageAreasShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
