<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellSizesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseCellSizesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers\WarehouseCellSizesCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers\WarehouseCellSizesDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers\WarehouseCellSizesGetHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers\WarehouseCellSizesShowHandler;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers\WarehouseCellSizesUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehouseCellSizeService implements WarehouseCellSizesServiceContract
{
    public function __construct(
        private WarehouseCellSizesCreateHandler $createHandler,
        private WarehouseCellSizesUpdateHandler $updateHandler,
        private WarehouseCellSizesDeleteHandler $deleteHandler,
        private WarehouseCellSizesShowHandler $showHandler,
        private WarehouseCellSizesGetHandler $getHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
