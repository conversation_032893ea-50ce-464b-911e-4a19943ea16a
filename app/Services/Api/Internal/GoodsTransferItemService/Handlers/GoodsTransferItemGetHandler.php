<?php

namespace App\Services\Api\Internal\GoodsTransferItemService\Handlers;

use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class GoodsTransferItemGetHandler
{
    public function __construct(
        private GoodsTransferItemsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
