<?php

namespace App\Services\Api\Internal\GoodsTransferItemService\Handlers;

use App\Jobs\FIFOJobs\HandleGoodsTransferFifoJob;
use Illuminate\Support\Facades\Queue;

readonly class GoodsTransferItemDeleteHandler
{
    public function run(string $resourceId): void
    {
        // Добавляем вызов FIFO-обработчика с флагом delete=true
        Queue::push(new HandleGoodsTransferFifoJob($resourceId, true));
    }
}
