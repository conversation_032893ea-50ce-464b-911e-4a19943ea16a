<?php

namespace App\Services\Api\Internal\GoodsTransferItemService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\Jobs\FIFOJobs\HandleGoodsTransferFifoJob;
use App\Services\Api\Internal\GoodsTransferItemService\DTO\GoodsTransferItemDto;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Support\Facades\Queue;
use RuntimeException;

class GoodsTransferItemsCreateHandler
{
    use HasOrderedUuid;
    private string $resourceId;

    public function __construct(
        private readonly GoodsTransferItemsRepositoryContract $itemRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws Exception
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof GoodsTransferItemDto) {
            throw new RuntimeException('Invalid DTO type');
        }
        $this->itemRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        // Добавляем вызов FIFO-обработчика
        Queue::push(new HandleGoodsTransferFifoJob($this->resourceId));

        return $this->resourceId;
    }
}
