<?php

namespace App\Services\Api\Internal\ContractService\Handlers;

use Carbon\Carbon;
use App\Traits\HasOrderedUuid;
use App\Contracts\Repositories\ContractRepositoryContract;

class ContractBulkCopyHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ContractRepositoryContract $repository
    ) {
    }

    public function run(array $ids): string
    {
        $insertContracts = [];

        foreach ($this->repository->getWhereInIds($ids)->toArray() as $contract) {
            $contract->id = $this->generateUuid();

            $this->generateContract($contract, $insertContracts);
        }

        $this->repository->insert($insertContracts);

        return 'OK!';
    }

    private function generateContract(object $contract, array &$insertContracts): void
    {
        $insertContracts[] = [
            'id' => $contract->id,
            'archived_at' => $contract->archived_at,
            'cabinet_id' => $contract->cabinet_id,
            'employee_id' => $contract->employee_id,
            'department_id' => $contract->department_id,
            'legal_entity_id' => $contract->legal_entity_id,
            'contractor_id' => $contract->contractor_id,
            'currency_id' => $contract->currency_id,
            'number' => $contract->number,
            'code' => $contract->code,
            'amount' => $contract->amount,
            'comment' => $contract->comment,
            'date_from' => $contract->date_from,
            'status_id' => $contract->status_id,
            'type' => $contract->type,
            'created_at' => $contract->created_at,
            'updated_at' => Carbon::now(),
        ];
    }
}
