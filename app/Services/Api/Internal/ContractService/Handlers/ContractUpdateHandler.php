<?php

namespace App\Services\Api\Internal\ContractService\Handlers;

use InvalidArgumentException;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ContractRepositoryContract;
use App\Services\Api\Internal\ContractService\DTO\ContractDTO;

readonly class ContractUpdateHandler
{
    public function __construct(
        private ContractRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ContractDTO) {
            throw new InvalidArgumentException('Invalid DTO');
        }

        $this->repository->update($dto->resourceId, $dto->toUpdateArray());
    }
}
