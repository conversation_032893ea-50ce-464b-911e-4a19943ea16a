<?php

use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\PermissionScopeEnum;

return [
    'Администратор' => [

        ],
    'Пользователь' => [
            [
                'guard_name' => PermissionNameEnum::METRICS->value,
                'operations' => ['all']
            ],
            [
                'guard_name' => PermissionNameEnum::BIN->value,
                'operations' => ['view', 'delete']
            ],
        [
            'guard_name' => PermissionNameEnum::RECOVER_DOCUMENTS->value,
            'operations' => ['all']
        ],
        [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PROCUREMENT_MANAGEMENT->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PROFITABILITY->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::GOODS_ON_SALE->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::SALES_FUNNEL->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'operations' => ['view', 'update', 'delete', 'held','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::RESIDUES->value,
            'operations' => ['all'],
        ],
        [
            'guard_name' => PermissionNameEnum::TURNOVERS->value,
            'operations' => ['all'],
        ],
        [
            'guard_name' => PermissionNameEnum::SERIAL_NUMBERS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::CRM_INDICATORS_FOR_CONTRACTORS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::CALL_LISTENING->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::CASH_FLOW->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::OFFSETS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::PROFIT_LOSS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'operations' => ['view', 'update', 'delete','print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::SALES->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::SALES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PREPAYMENTS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PREPAYMENTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'operations' => ['view', 'update', 'delete','print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'operations' => ['view', 'update', 'delete', 'print','held'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'operations' => ['view', 'update', 'delete', 'print'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::REMUNERATION->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::SEND_MAILS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::SEE_PRICES->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::EDIT_DOCUMENT_TEMPLATES->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::EDIT_DOCUMENT_CURRENCY->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::IMPORT->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::EXPORT->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::ONLINE_SHOPS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::API_ACCESS->value,
            'operations' => ['all'],
        ],

        [
            'guard_name' => PermissionNameEnum::LEGAL_ENTITIES->value,
            'operations' => ['view'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],

        [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::CURRENCIES->value,
            'operations' => ['update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::CURRENCIES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'operations' => ['view', 'update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'operations' => ['update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::MEASUREMENT_UNITS->value,
            'operations' => ['update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::MEASUREMENT_UNITS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'operations' => ['view','update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::VAT_RATES->value,
            'operations' => ['update', 'delete'],
            'scope' => PermissionScopeEnum::SCOPE_ALL->value,
        ],
        [
            'guard_name' => PermissionNameEnum::VAT_RATES->value,
            'operations' => ['create'],
        ],

        [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'operations' => ['view'],
            'scope' => PermissionScopeEnum::SCOPE_CREATED_AND_ASSIGNED->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'operations' => ['perform'],
            'scope' => PermissionScopeEnum::SCOPE_CREATED_AND_ASSIGNED->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'operations' => ['update'],
            'scope' => PermissionScopeEnum::SCOPE_CREATED->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'operations' => ['delete'],
            'scope' => PermissionScopeEnum::SCOPE_CREATED->value,
        ],
        [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'operations' => ['create'],
        ],

        ],
    'Доступ только к точкам продаж' => [
    ],
    'Доступ только к ТСД' => [
        ],
    'Доступ только к производству' => [

        ],

];
