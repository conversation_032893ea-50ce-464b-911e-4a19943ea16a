<?php

namespace App\Actions\Ozon;

use App\Modules\Marketplaces\Services\Ozon\Jobs\Stocks\SyncStocksJob;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class SyncStocksAction
{
    public function handle(string $integrationId): void
    {
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->first();

        if (!$integration) {
            throw new RuntimeException('Integration not found');
        }

        SyncStocksJob::dispatch(
            $integration->cabinet_id,
            $integration->api_key,
            $integration->client_id,
            $integrationId
        );
    }
}
