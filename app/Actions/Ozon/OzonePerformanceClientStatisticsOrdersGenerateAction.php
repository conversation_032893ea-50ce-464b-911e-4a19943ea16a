<?php

namespace App\Actions\Ozon;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OzonePerformanceClientStatisticsOrdersGenerateAction
{
    use HasTokenPerformanceCredential;
    use OzonAnalyticsOrdersChunksTraits;

    // protected $authService;
    // protected $entity;

    // public function __construct(AuthorizationServiceContract $authService, OzonAnalyticsOrdersEntity $entity)
    // {
    //     $this->authService = $authService;
    //     $this->entity = $entity;
    // }

    /**
     * Отчёт с аналитикой внешнего трафика
     * https://docs.ozon.ru/api/performance/#operation/VendorStatisticsSubmitRequest
     */
    public function handle(): void
    {

        $credentials = DB::table('ozon_performance_credentials')->get();

        $credentials->each(function ($item){

            $cabinetId = $item->cabinet_id;
            $departmentId = $item->department_id;
            $employeeId = $item->employee_id;
            $ozonCredentialId = $item->id;

            $accessToken = $this->getToken($ozonCredentialId);

            $latestDate = DB::table('ozon_performance_client_statistics_orders_generate as og')
                    ->leftJoin('ozon_performance_client_statistics_orders_generate_items as ogi', 'og.id', '=', 'ogi.ozon_performance_client_statistics_orders_generate_id')
                    ->where('og.ozon_performance_credential_id', $ozonCredentialId)
                    ->orderBy('ogi.date', 'desc')
                    ->first('ogi.date');

            $dateFrom = $latestDate !== null ? $latestDate->date : Carbon::now()->subMonths(2)->format('Y-m-d\TH:i:m\Z');

            $dateTo = Carbon::now()->format('Y-m-d\TH:i:m\Z');

            $data = [
                'from' => $dateFrom,
                'to' => $dateTo,
            ];

            $response = OzonApiPerformanceRequestHelper::sendRequest(
                'POST',
                '/api/client/statistic/orders/generate',
                $accessToken,
                $data
            );

            if(isset($response['error'])){
                Log::error("Ошибка в OzonePerformanceClientStatisticsOrdersGenerateAction, запрос /api/client/vendors/statistics/orders/generate:", [$response['error']]);
            }

            OzonePerformanceClientAnalyticsJob::dispatch($response['UUID'], $cabinetId, $departmentId, $employeeId, $ozonCredentialId, $this->fields)
            ->delay(now()->addSeconds(59));


        });

    }

}
