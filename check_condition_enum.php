<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Checking condition enum values:' . PHP_EOL;

// Check contractors filters
$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        if ($param['name'] === 'filters' && $param['schema']['type'] === 'object') {
            if (isset($param['schema']['properties'])) {
                // Check first few filters with condition
                $count = 0;
                foreach ($param['schema']['properties'] as $filterName => $filterSchema) {
                    if (isset($filterSchema['properties']['condition']) && $count < 3) {
                        $conditionSchema = $filterSchema['properties']['condition'];
                        echo "  $filterName.condition: " . $conditionSchema['type'];
                        
                        if (isset($conditionSchema['enum'])) {
                            echo " enum[" . implode(',', $conditionSchema['enum']) . "]";
                        } else {
                            echo " NO ENUM";
                        }
                        echo PHP_EOL;
                        $count++;
                    }
                }
            }
            break;
        }
    }
}

echo PHP_EOL . 'Checking acceptances filters:' . PHP_EOL;

// Check acceptances filters
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        if ($param['name'] === 'filters' && $param['schema']['type'] === 'object') {
            if (isset($param['schema']['properties'])) {
                // Check first filter with condition
                foreach ($param['schema']['properties'] as $filterName => $filterSchema) {
                    if (isset($filterSchema['properties']['condition'])) {
                        $conditionSchema = $filterSchema['properties']['condition'];
                        echo "  $filterName.condition: " . $conditionSchema['type'];
                        
                        if (isset($conditionSchema['enum'])) {
                            echo " enum[" . implode(',', $conditionSchema['enum']) . "]";
                        } else {
                            echo " NO ENUM";
                        }
                        echo PHP_EOL;
                        break; // Just check first one
                    }
                }
            }
            break;
        }
    }
}
