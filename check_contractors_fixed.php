<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Contractors parameters after fix:' . PHP_EOL;

$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    echo 'Total parameters: ' . count($contractorsGet['parameters']) . PHP_EOL . PHP_EOL;
    
    foreach ($contractorsGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        
        echo "- $name: ";
        
        if ($schema['type'] === 'object') {
            echo "object" . PHP_EOL;
            if (isset($schema['properties'])) {
                $propCount = count($schema['properties']);
                echo "  Properties: $propCount" . PHP_EOL;
                
                // Show first few properties
                $count = 0;
                foreach ($schema['properties'] as $propName => $propSchema) {
                    if ($count < 3) {
                        echo "    - $propName: " . (is_array($propSchema['type']) ? implode('|', $propSchema['type']) : $propSchema['type']) . PHP_EOL;
                    }
                    $count++;
                }
                if ($propCount > 3) {
                    echo "    ... and " . ($propCount - 3) . " more" . PHP_EOL;
                }
            }
        } else {
            if (is_array($schema['type'])) {
                echo implode('|', $schema['type']);
            } else {
                echo $schema['type'];
            }
            
            if (isset($schema['format'])) {
                echo "<" . $schema['format'] . ">";
            }
            
            if (isset($schema['enum'])) {
                echo " enum[" . implode(',', array_slice($schema['enum'], 0, 2)) . (count($schema['enum']) > 2 ? '...' : '') . "]";
            }
            
            echo PHP_EOL;
        }
    }
}
