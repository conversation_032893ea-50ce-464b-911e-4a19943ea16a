APP_NAME=M1
APP_ENV=local
APP_KEY=base64:bvQpaGT/N7lB+rxRbMsJw/ZoWQka5tjNselMIHs8Nus=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=https://m1.test

APP_LOCALE=ru
APP_FALLBACK_LOCALE=ru
APP_FAKER_LOCALE=ru_RU

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# DB_CONNECTION=mysql
# DB_PORT=3306
# DB_HOST=localhost
# DB_DATABASE=m1_sql
# DB_USERNAME=root
# DB_PASSWORD=

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=laravel_utf8
DB_USERNAME=postgres
DB_PASSWORD=Dfufapfap223

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=31e81352-1866-4288-89ba-1b91aa786d91:a024b82048104c06a9325610b1edd7fc
AWS_SECRET_ACCESS_KEY=906df5d421cc9a105b0a257f42c115c8
AWS_DEFAULT_REGION=ru-central-1
AWS_BUCKET="images"
AWS_ENDPOINT="https://log-images.s3.cloud.ru"
AWS_USE_PATH_STYLE_ENDPOINT=true

AWS_ACCESS_KEY_ID_2=31e81352-1866-4288-89ba-1b91aa786d91:f0e051ede33240e790f18903baec087a
AWS_SECRET_ACCESS_KEY_2=89f4c0e4e6d28636be029ca35ce6ed4f
AWS_DEFAULT_REGION_2=ru-central-1
AWS_BUCKET_2="docs"
AWS_ENDPOINT_2="https://s3.cloud.ru/"
AWS_USE_PATH_STYLE_ENDPOINT_2=true
DADATA_KEY="26eefbfe227fddc98a709a8599833ebf2134cdfc"
DADATA_SECRET_KEY="bddbf596429382edbb065507d6a669b860d422e5"

VITE_APP_NAME="${APP_NAME}"
