<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

echo 'Filters object details:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        if ($param['name'] === 'filters') {
            if (isset($param['schema']['properties'])) {
                foreach ($param['schema']['properties'] as $filterName => $filterSchema) {
                    echo "  $filterName:" . PHP_EOL;
                    if (isset($filterSchema['properties'])) {
                        foreach ($filterSchema['properties'] as $propName => $propSchema) {
                            echo "    - $propName: ";
                            
                            if (is_array($propSchema['type'])) {
                                echo implode('|', $propSchema['type']);
                            } else {
                                echo $propSchema['type'];
                            }
                            
                            if (isset($propSchema['format'])) {
                                echo "<" . $propSchema['format'] . ">";
                            }
                            
                            if (isset($propSchema['enum'])) {
                                echo " enum[" . implode(',', $propSchema['enum']) . "]";
                            }
                            
                            if ($propSchema['type'] === 'array' && isset($propSchema['items'])) {
                                echo "[" . $propSchema['items']['type'];
                                if (isset($propSchema['items']['format'])) {
                                    echo "<" . $propSchema['items']['format'] . ">";
                                }
                                echo "]";
                            }
                            
                            echo PHP_EOL;
                        }
                    }
                }
            }
            break;
        }
    }
}
