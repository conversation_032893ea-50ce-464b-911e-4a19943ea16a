<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Looking for /internal/contractors endpoint:' . PHP_EOL;

if (isset($data['paths']['/internal/contractors'])) {
    echo 'Found /internal/contractors endpoint!' . PHP_EOL;
    
    $contractorsGet = $data['paths']['/internal/contractors']['get'];
    echo 'Parameters: ' . (isset($contractorsGet['parameters']) ? count($contractorsGet['parameters']) : 0) . PHP_EOL;
    
    if (isset($contractorsGet['parameters'])) {
        foreach ($contractorsGet['parameters'] as $param) {
            $name = $param['name'];
            $schema = $param['schema'];
            echo "- $name: " . (is_array($schema['type']) ? implode('|', $schema['type']) : $schema['type']) . PHP_EOL;
        }
    }
} else {
    echo 'Endpoint /internal/contractors NOT found!' . PHP_EOL;
    echo 'Available paths:' . PHP_EOL;
    foreach (array_keys($data['paths']) as $path) {
        if (str_contains($path, 'contractor')) {
            echo "- $path" . PHP_EOL;
        }
    }
}
