<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

echo 'Fields parameter details:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        if ($param['name'] === 'fields' && $param['schema']['type'] === 'object') {
            echo "fields object properties:" . PHP_EOL;
            if (isset($param['schema']['properties'])) {
                foreach ($param['schema']['properties'] as $propName => $propSchema) {
                    echo "  - $propName: " . $propSchema['type'];
                    if ($propSchema['type'] === 'array' && isset($propSchema['items'])) {
                        echo "[" . $propSchema['items']['type'] . "]";
                    }
                    echo PHP_EOL;
                }
            } else {
                echo "  No properties found" . PHP_EOL;
            }
            break;
        }
    }
}

echo PHP_EOL . 'Filters parameter details:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        if ($param['name'] === 'filters') {
            echo "filters object properties:" . PHP_EOL;
            if (isset($param['schema']['properties'])) {
                foreach ($param['schema']['properties'] as $propName => $propSchema) {
                    echo "  - $propName: " . $propSchema['type'];
                    if ($propSchema['type'] === 'object' && isset($propSchema['properties'])) {
                        echo " (object with " . count($propSchema['properties']) . " properties)";
                    }
                    echo PHP_EOL;
                }
            }
            break;
        }
    }
}
