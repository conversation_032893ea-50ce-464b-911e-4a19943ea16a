<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Acceptances parameters after fix:' . PHP_EOL;

$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

if (isset($acceptancesGet['parameters'])) {
    echo 'Total parameters: ' . count($acceptancesGet['parameters']) . PHP_EOL . PHP_EOL;
    
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        
        echo "- $name: ";
        
        if ($schema['type'] === 'object') {
            echo "object" . PHP_EOL;
            if (isset($schema['properties'])) {
                $propCount = count($schema['properties']);
                echo "  Properties: $propCount" . PHP_EOL;
            }
        } else {
            if (is_array($schema['type'])) {
                echo implode('|', $schema['type']);
            } else {
                echo $schema['type'];
            }
            
            if (isset($schema['format'])) {
                echo "<" . $schema['format'] . ">";
            }
            
            echo PHP_EOL;
        }
    }
}
