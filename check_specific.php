<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

$targetParams = [
    'filters.contractor_owners.value',
    'filters.warehouses.value',
    'filters.legal_entity.value',
    'filters.employee_owners.value',
    'filters.contractors.value',
    'filters.department_owners.value',
    'filters.contractor_groups.value',
    'filters.statuses.value',
    'filters.products.value'
];

echo 'Checking Rule::excludeIf parameters:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        if (in_array($name, $targetParams)) {
            $type = $param['schema']['type'] ?? 'unknown';
            $format = isset($param['schema']['format']) ? " ({$param['schema']['format']})" : '';
            $required = $param['required'] ?? false ? ' [required]' : ' [optional]';
            echo "✅ $name: $type$format$required" . PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Missing parameters:' . PHP_EOL;
$foundParams = [];
if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $foundParams[] = $param['name'];
    }
}

foreach ($targetParams as $target) {
    if (!in_array($target, $foundParams)) {
        echo "❌ $target" . PHP_EOL;
    }
}
