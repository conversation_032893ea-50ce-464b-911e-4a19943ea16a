<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Create instance manually to avoid validation
$entity = app(\App\Entities\ContractorEntity::class);
$request = new \App\Http\Requests\Api\Internal\Contractors\ContractorsIndexRequest($entity);
$rules = $request->rules();

echo "ContractorsIndexRequest fields rules:" . PHP_EOL;

foreach ($rules as $fieldName => $fieldRules) {
    if (str_contains($fieldName, 'fields')) {
        echo "- $fieldName: ";
        
        if (is_array($fieldRules)) {
            foreach ($fieldRules as $rule) {
                if ($rule instanceof \App\Rules\AllowedFieldsRule) {
                    echo "AllowedFieldsRule found! ";
                    
                    // Get allowed fields
                    $allowedFields = $entity->getAllowedFields();
                    echo "Fields count: " . count($allowedFields) . " ";
                    echo "First 5: " . implode(',', array_slice($allowedFields, 0, 5));
                    break;
                } elseif (is_string($rule)) {
                    echo "string rule: $rule ";
                } else {
                    echo "other rule: " . get_class($rule) . " ";
                }
            }
        } else {
            echo "simple rule: $fieldRules";
        }
        
        echo PHP_EOL;
    }
}
