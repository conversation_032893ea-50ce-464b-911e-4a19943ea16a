{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-bcmath": "*", "ext-intl": "*", "ext-simplexml": "*", "dedoc/scramble": "^0.12.23", "hflabs/dadata": "^24.4", "kerigard/laravel-lang-ru": "^2.0", "laravel/framework": "^11.9", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "phpoffice/phpspreadsheet": "^1.29.2", "prog-time/laravel-request-testdata": "^0.1.2"}, "require-dev": {"brianium/paratest": "^7.7", "fakerphp/faker": "^1.23", "larastan/larastan": "^2.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}