<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);

echo 'Checking custom rules for contractors:' . PHP_EOL;

$contractorsGet = $data['paths']['/internal/contractors']['get'];

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        
        if (in_array($name, ['sortField', 'fields'])) {
            echo "- $name: ";

            if (is_array($schema['type'])) {
                echo implode('|', $schema['type']);
            } else {
                echo $schema['type'];
            }

            if (isset($schema['format'])) {
                echo "<" . $schema['format'] . ">";
            }

            // Check for array items enum
            if ($schema['type'] === 'array' && isset($schema['items']['enum'])) {
                echo " array[enum:" . count($schema['items']['enum']) . " values]";
                echo " first 3: " . implode(',', array_slice($schema['items']['enum'], 0, 3));
            } elseif (isset($schema['enum'])) {
                echo " enum[" . implode(',', array_slice($schema['enum'], 0, 5));
                if (count($schema['enum']) > 5) {
                    echo '... +' . (count($schema['enum']) - 5) . ' more';
                }
                echo "]";
            }

            echo PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Checking fields object properties:' . PHP_EOL;

if (isset($contractorsGet['parameters'])) {
    foreach ($contractorsGet['parameters'] as $param) {
        if ($param['name'] === 'fields' && $param['schema']['type'] === 'object') {
            if (isset($param['schema']['properties'])) {
                echo "fields object has properties (should be empty for array fields)" . PHP_EOL;
            } else {
                echo "fields object has no properties (correct for array fields)" . PHP_EOL;
            }
            break;
        }
    }
}
