# Исправление проблемы с кешем при создании кабинета

## Проблема

При создании нового кабинета пользователь сталкивался с тем, что у него не было прав на новый кабинет до тех пор, пока кеш не очищался автоматически по истечению времени (10 минут).

### Причина проблемы

1. **Неправильная последовательность операций**: Кеш очищался в `CabinetCreateHandler` **до** того, как асинхронная задача `CreateCabinetSettingsJob` создавала запись сотрудника и связь `cabinet_employee`.

2. **Асинхронное выполнение**: `CreateCabinetSettingsJob` выполнялся в очереди, что создавало временное окно, когда у пользователя не было прав на новый кабинет.

3. **Временное окно без прав**: Между очисткой кеша и созданием прав проходило время, в течение которого пользователь не мог получить доступ к новому кабинету.

## Решение

### Изменения в коде

1. **Убрана очистка кеша из CabinetCreateHandler**:
   ```php
   // Удалено из CabinetCreateHandler::run()
   Cache::delete("permissions_{$authUser->id}");
   Cache::delete("employees_{$authUser->id}");
   ```

2. **Синхронное выполнение CreateCabinetSettingsJob**:
   ```php
   // В CreateCabinetSettings::handle()
   CreateCabinetSettingsJob::dispatchSync($event->cabinetId, $event->user);
   ```

3. **Очистка кеша после создания прав**:
   ```php
   // В CreateCabinetSettings::handle() после dispatchSync
   Cache::delete("permissions_{$event->user->id}");
   Cache::delete("employees_{$event->user->id}");
   ```

### Новая последовательность операций

1. **Создание кабинета** в базе данных
2. **Отправка события** `CabinetCreated`
3. **Синхронное выполнение** `CreateCabinetSettingsJob`:
   - Создание настроек кабинета
   - Создание департамента по умолчанию
   - **Создание записи сотрудника** для владельца
   - Создание связи `cabinet_employee`
   - Создание других необходимых записей
4. **Очистка кеша** после завершения job

## Преимущества решения

1. **Мгновенный доступ**: Пользователь получает права на новый кабинет сразу после его создания
2. **Атомарность**: Все операции выполняются синхронно в рамках одной транзакции
3. **Надежность**: Исключено временное окно без прав
4. **Простота**: Логика стала более предсказуемой и понятной

## Тестирование

Созданы тесты для проверки корректности работы:

1. **CabinetCreationCacheTest**: Проверяет очистку кеша после создания кабинета
2. **CabinetCreationIntegrationTest**: Интеграционный тест всего процесса создания кабинета

## Потенциальные риски

1. **Увеличение времени ответа**: Создание кабинета теперь выполняется синхронно, что может увеличить время ответа API
2. **Блокировка**: При большой нагрузке синхронное выполнение может создать блокировки

## Мониторинг

Рекомендуется отслеживать:
- Время выполнения операции создания кабинета
- Количество ошибок при создании кабинета
- Производительность API при создании кабинетов

## Откат изменений

В случае проблем можно вернуться к асинхронному выполнению:

```php
// В CreateCabinetSettings::handle()
CreateCabinetSettingsJob::dispatch($event->cabinetId, $event->user);

// И вернуть очистку кеша в CabinetCreateHandler
Cache::delete("permissions_{$authUser->id}");
Cache::delete("employees_{$authUser->id}");
```

Однако это вернет исходную проблему с временным окном без прав.
