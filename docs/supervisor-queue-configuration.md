# Конфигурация Supervisor для Laravel Queue

## Проблема
При неправильной настройке supervisor может запускать несколько процессов для одной и той же очереди, что приводит к:
- Дублированию выполнения задач
- Ошибкам `OSError: [Errno 9] Bad file descriptor`
- Конфликтам при обработке одних и тех же данных

## Правильная конфигурация Supervisor

### 1. Основная конфигурация воркеров

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/logru/src/artisan queue:work --sleep=3 --tries=3 --max-jobs=1000 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker.log
stopwaitsecs=3600
```

### 2. Разделение по очередям

```ini
[program:laravel-worker-default]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/logru/src/artisan queue:work --queue=default --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker-default.log

[program:laravel-worker-high]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/logru/src/artisan queue:work --queue=high --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker-high.log
```

### 3. Для критических задач (одиночные процессы)

```ini
[program:laravel-worker-critical]
process_name=%(program_name)s
command=php /var/www/logru/src/artisan queue:work --queue=critical --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker-critical.log
```

## Команды управления

```bash
# Перезагрузить конфигурацию
sudo supervisorctl reread
sudo supervisorctl update

# Перезапустить воркеры
sudo supervisorctl restart laravel-worker:*

# Проверить статус
sudo supervisorctl status

# Остановить все воркеры
sudo supervisorctl stop laravel-worker:*
```

## Мониторинг

```bash
# Просмотр логов
sudo tail -f /var/log/supervisor/laravel-worker.log

# Проверка процессов
ps aux | grep "queue:work"
```

## Важные параметры

- `numprocs` - количество процессов (не больше 2-3 для одной очереди)
- `stopwaitsecs` - время ожидания graceful shutdown
- `max-jobs` - максимум задач перед перезапуском процесса
- `max-time` - максимальное время работы процесса
- `redirect_stderr=true` - перенаправление ошибок в лог
