# Resource and Collection Implementation Prompt

This prompt provides a comprehensive guide for implementing Laravel API Resources and Collections for controllers that follow the same pattern as ContractorController.

## Prerequisites

Before implementing resources, you need to understand:

1. **Controller Structure**: The controller should have `index()` and `show()` methods that use a service layer
2. **Service Layer**: The service uses handlers that call repository methods
3. **Repository Pattern**: The repository's `get()` method returns paginated data with `data` and `meta` structure
4. **Entity Structure**: The entity defines available fields and relationships using the custom EntityBuilder system

## Step 1: Analyze the Existing Structure

### 1.1 Examine the Controller
```php
// Look for patterns like:
public function index(SomeIndexRequest $request): JsonResponse
{
    return $this->executeAction(function () use ($request) {
        $data = $this->service->index($request->toDTO());
        return $this->successResponse($data);
    });
}

public function show(Request $request, string $id): JsonResponse
{
    return $this->executeAction(function () use ($request, $id) {
        $this->authorizeView($request, $id);
        $data = $this->service->show($id);
        return $this->successResponse($data);
    });
}
```

### 1.2 Examine the Service and Repository
- Check how the service's `index()` method calls the repository
- Verify the repository's `get()` method returns Collection with `data` and `meta`
- Check how the service's `show()` method processes single items

### 1.3 Examine the Entity
```php
// Look for:
public static array $fields = [
    'id',
    'field1',
    'field2',
    // ... other fields
];

// And relationship methods like:
public function relationName(): RelationBuilder
{
    return $this->hasMany(RelatedEntity::class, 'local_key', 'foreign_key');
}
```

## Step 2: Create the Resource Class

### 2.1 Create the Resource File
```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class [EntityName]Resource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // If this is a collection with data and meta structure
        if ($this->resource instanceof \Illuminate\Support\Collection && isset($this->resource['data'])) {
            return $this->resource->toArray();
        }
        
        // If this is a single object (for show method)
        if (is_object($this->resource)) {
            return $this->transformSingleItem($this->resource);
        }
        
        // If this is an array item (from collection)
        return $this->transformItem($this->resource);
    }

    /**
     * Transform a single item from show method
     */
    protected function transformSingleItem($item): array
    {
        $data = (array) $item;
        
        return [
            // Map all fields from the entity's $fields array
            'id' => $data['id'] ?? null,
            'field1' => $data['field1'] ?? null,
            'field2' => $data['field2'] ?? null,
            // ... continue for all fields
            
            // Handle boolean fields with proper casting
            'boolean_field' => (bool)($data['boolean_field'] ?? false),
            
            // Handle related data
            'relation1' => $data['relation1'] ?? null,
            'relation2' => $data['relation2'] ?? [],
            // ... continue for all relations
        ];
    }

    /**
     * Transform an item from collection
     */
    public function transformItem($item): array
    {
        // Support both objects and arrays
        $data = is_array($item) ? $item : (is_object($item) ? (array) $item : []);

        return [
            // Same structure as transformSingleItem
            // This ensures consistency between index and show responses
        ];
    }
}
```

### 2.2 Field Mapping Guidelines

1. **Always include `id` field first**
2. **Map all fields from Entity's `$fields` array**
3. **Cast boolean fields explicitly**: `(bool)($data['field'] ?? false)`
4. **Handle nullable fields**: Use `?? null` for optional fields
5. **Handle arrays/objects**: Use `?? []` for collections, `?? null` for single relations
6. **Maintain consistent structure** between `transformSingleItem` and `transformItem`

## Step 3: Create the Collection Class

### 3.1 Create the Collection File
```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class [EntityName]Collection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = [EntityName]Resource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        // The EntityBuilder returns a Collection with 'data' and 'meta' keys
        if ($this->resource instanceof \Illuminate\Support\Collection && $this->resource->has('data') && $this->resource->has('meta')) {
            $data = $this->resource->get('data');
            $meta = $this->resource->get('meta');
            
            return [
                'data' => collect($data)->map(function ($item) {
                    return (new [EntityName]Resource($item))->transformItem($item);
                }),
                'meta' => $meta,
            ];
        }

        // Fallback for simple collections
        return [
            'data' => $this->collection->map(function ($item) {
                return (new [EntityName]Resource($item))->transformItem($item);
            }),
            'meta' => [
                'current_page' => 1,
                'per_page' => count($this->collection),
                'total' => count($this->collection),
                'last_page' => 1,
            ],
        ];
    }
}
```

## Step 4: Update the Controller

### 4.1 Add Resource Imports
```php
use App\Http\Resources\[EntityName]Collection;
use App\Http\Resources\[EntityName]Resource;
```

### 4.2 Update Index Method
```php
public function index([EntityName]IndexRequest $request): JsonResponse
{
    return $this->executeAction(function () use ($request) {
        $data = $this->service->index($request->toDTO());
        return $this->successResponse(new [EntityName]Collection($data));
    });
}
```

### 4.3 Update Show Method
```php
public function show(Request $request, string $id): JsonResponse
{
    return $this->executeAction(function () use ($request, $id) {
        $this->authorizeView($request, $id);

        $data = $this->service->show($id);
        return $this->successResponse(new [EntityName]Resource($data));
    });
}
```

## Step 5: Testing and Validation

### 5.1 Test Index Endpoint
- Verify pagination metadata is included
- Check that all fields are properly mapped
- Ensure relationships are included when requested

### 5.2 Test Show Endpoint
- Verify single item structure matches collection item structure
- Check that all relationships are properly decoded (especially JSON fields)
- Ensure boolean fields are properly cast

### 5.3 Common Issues and Solutions

1. **Missing Meta Data**: Ensure the collection properly extracts `meta` from the EntityBuilder response
2. **Inconsistent Field Types**: Always cast boolean fields explicitly
3. **Missing Relationships**: Check that the Entity defines relationship methods correctly
4. **JSON Decoding**: For show method, some fields might need JSON decoding (handled in ShowHandler)

## Example Implementation Checklist

- [ ] Analyzed controller structure and data flow
- [ ] Examined entity fields and relationships
- [ ] Created Resource class with proper field mapping
- [ ] Created Collection class with meta data handling
- [ ] Updated controller to use resources
- [ ] Added proper imports
- [ ] Tested index endpoint with pagination
- [ ] Tested show endpoint with single item
- [ ] Verified field consistency between index and show
- [ ] Checked relationship data structure

## Notes

- This pattern works with the custom EntityBuilder system used in the codebase
- The EntityBuilder returns Collections with `data` and `meta` structure for pagination
- Show methods may have additional JSON decoding in the ShowHandler
- Always maintain consistency between index and show response structures
- Boolean fields should be explicitly cast to avoid string/integer confusion
