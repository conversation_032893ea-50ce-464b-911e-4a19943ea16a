<?php

namespace App\Services\Api\Internal\GoodsTransferItemService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Products\GoodsTransferItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\GoodsTransferItemService\Handlers\GoodsTransferItemDeleteHandler;
use App\Services\Api\Internal\GoodsTransferItemService\Handlers\GoodsTransferItemGetHandler;
use App\Services\Api\Internal\GoodsTransferItemService\Handlers\GoodsTransferItemsCreateHandler;
use App\Services\Api\Internal\GoodsTransferItemService\Handlers\GoodsTransferItemShowHandler;
use App\Services\Api\Internal\GoodsTransferItemService\Handlers\GoodsTransferItemsUpdateHandler;
use Exception;
use Illuminate\Support\Collection;

readonly class GoodTransferItemsService implements GoodsTransferItemsServiceContract
{
    public function __construct(
        private GoodsTransferItemsCreateHandler $createHandler,
        private GoodsTransferItemsUpdateHandler $updateHandler,
        private GoodsTransferItemDeleteHandler $deleteHandler,
        private GoodsTransferItemGetHandler $getHandler,
        private GoodsTransferItemShowHandler $showHandler
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
