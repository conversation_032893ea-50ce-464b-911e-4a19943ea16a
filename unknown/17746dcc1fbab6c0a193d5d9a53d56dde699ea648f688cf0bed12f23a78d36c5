<?php

namespace App\Http\Requests\Api\Internal\ShipmentItems;

use App\Services\Api\Internal\Sales\ShipmentItemsService\DTO\ShipmentItemCalculateDTO;
use Illuminate\Foundation\Http\FormRequest;

class ShipmentItemCalculateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'product_id' => 'required|uuid',
            'warehouse_id' => 'required|uuid',
            'date_from' => 'required|date',
            'cabinet_id' => 'required|uuid',
        ];
    }

    public function toDTO(): ShipmentItemCalculateDTO
    {
        $validated = $this->validated();

        return new ShipmentItemCalculateDTO(
            productId: $validated['product_id'],
            warehouseId: $validated['warehouse_id'],
            dateFrom: $validated['date_from'],
            cabinetId: $validated['cabinet_id']
        );
    }
}
