<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * Action для создания отгрузки в Ozon и в нашей системе
 */
class CreateCarriageAction
{
    use HasOrderedUuid;

    /**
     * Создать отгрузку
     */
    public function run(string $deliveryMethodId, string $departureDate): array
    {
        $deliveryMethod = $this->getDeliveryMethodWithIntegration($deliveryMethodId);

        $readyPostings = $this->getReadyToShipPostings($deliveryMethod->integration_id);

        if ($readyPostings->isEmpty()) {
            throw new NotFoundException('No postings with status ready_to_ship found');
        }

        try {
            DB::beginTransaction();

            $ozonCarriage = $this->createCarriageInOzon(
                $deliveryMethod,
                $departureDate
            );

            $carriageId = $this->createCarriageInDatabase(
                $deliveryMethod,
                $ozonCarriage->carriage_id,
                $departureDate
            );

            $this->linkPostingsToCarriage($carriageId, $readyPostings);

            DB::commit();

            return [
                'id' => $carriageId,
                'ozon_carriage_id' => $ozonCarriage->carriage_id,
                'departure_date' => $departureDate,
                'postings_count' => $readyPostings->count(),
                'posting_numbers' => $readyPostings->pluck('posting_number')->toArray()
            ];

        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Получить метод доставки с данными интеграции
     */
    protected function getDeliveryMethodWithIntegration(string $deliveryMethodId): object
    {
        $deliveryMethod = DB::table('ozon_fbs_warehouse_delivery_methods as dm')
            ->join('ozon_integrations as i', 'dm.integration_id', '=', 'i.id')
            ->where('dm.id', $deliveryMethodId)
            ->select([
                'dm.*',
                'i.api_key',
                'i.client_id'
            ])
            ->first();

        if (!$deliveryMethod) {
            throw new NotFoundException('Delivery method not found');
        }

        return $deliveryMethod;
    }

    /**
     * Получить отправления со статусом ready_to_ship
     */
    protected function getReadyToShipPostings(string $integrationId): Collection
    {
        return DB::table('ozon_fbs_orders')
            ->where('integration_id', $integrationId)
            ->where('status', 'ready_to_ship')
            ->select(['id', 'posting_number', 'status'])
            ->get();
    }

    /**
     * Создать отгрузку в Ozon через API
     */
    protected function createCarriageInOzon(object $deliveryMethod, string $departureDate): object
    {
        $api = new API(
            apiKey: decrypt($deliveryMethod->api_key),
            clientId: decrypt($deliveryMethod->client_id)
        );

        $isoDate = Carbon::parse($departureDate)->toISOString();

        return $api->FBS()->createCarriage(
            deliveryMethodId: (int) $deliveryMethod->ozon_delivery_method_id,
            departureDate: $isoDate
        );
    }

    /**
     * Создать отгрузку в нашей базе данных
     */
    protected function createCarriageInDatabase(
        object $deliveryMethod,
        int $ozonCarriageId,
        string $departureDate
    ): string {
        $carriageId = $this->generateUuid();

        DB::table('ozon_carriages')->insert([
            'id' => $carriageId,
            'cabinet_id' => $deliveryMethod->cabinet_id,
            'integration_id' => $deliveryMethod->integration_id,
            'delivery_method_id' => $deliveryMethod->id,
            'ozon_carriage_id' => $ozonCarriageId,
            'ozon_delivery_method_id' => $deliveryMethod->ozon_delivery_method_id,
            'departure_date' => $departureDate,
            'status' => 'new',
            'is_synced' => false,
            'sync_attempts' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return $carriageId;
    }

    /**
     * Связать отправления с отгрузкой
     */
    protected function linkPostingsToCarriage(string $carriageId, Collection $postings): void
    {
        $dataToInsert = [];

        foreach ($postings as $posting) {
            $dataToInsert[] = [
                'id' => $this->generateUuid(),
                'carriage_id' => $carriageId,
                'order_id' => $posting->id,
                'posting_number' => $posting->posting_number,
                'status_at_creation' => $posting->status,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($dataToInsert)) {
            DB::table('ozon_carriage_postings')->insert($dataToInsert);
        }
    }
}
