<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\ProfitTaxRatePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\References\ProfitTaxRatesService\DTO\ProfitTaxRateDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class ProfitTaxRatePolicy implements ProfitTaxRatePolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProfitTaxRateDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id);
        $this->checkEmployeeAndDepartmentIds(
            $dto->employee_id,
            $dto->department_id,
            $dto->cabinet_id
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProfitTaxRateDTO) {
            return;
        }

        $vatRate = $this->authService->validateRelationAccess(
            'profit_tax_rates',
            $dto->id,
        );

        $this->checkEmployeeAndDepartmentIds(
            $dto->employee_id,
            $dto->department_id,
            $vatRate->cabinet_id
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'profit_tax_rates',
            $resourceId,
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'profit_tax_rates',
            $resourceId,
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
