<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseStorageAreaPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\DTO\WarehouseStorageAreaDTO;

readonly class WarehouseStorageAreaPolicy implements WarehouseStorageAreaPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseStorageAreaDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id);

        if ($dto->cells_id) {
            $this->authService->validateResourcesAccess(
                entity: 'warehouse_cells',
                cabinetId: $dto->cabinet_id,
                ids: $dto->cells_id,
                relatedEntity: [
                    'table' => 'warehouses',
                    'children_field' => 'warehouse_id',
                    'field' => 'id',
                ]
            );
        }

        if ($dto->products_id) {
            $this->authService->validateResourcesAccess(
                entity: 'products',
                cabinetId: $dto->cabinet_id,
                ids: $dto->products_id,
            );
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseStorageAreaDTO) {
            return;
        }

        $storageArea = $this->authService->validateRelationAccess(
            entity: 'warehouse_storage_areas',
            entityId: $dto->resourceId,
            relatedEntity: [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $dto->resourceId
            ]
        );

        if ($dto->cells_id) {
            $this->authService->validateResourcesAccess(
                entity: 'warehouse_cells',
                cabinetId: $storageArea->cabinet_id,
                ids: $dto->cells_id,
                relatedEntity: [
                    'table' => 'warehouses',
                    'children_field' => 'warehouse_id',
                    'field' => 'id',
                ]
            );
        }

        if ($dto->products_id) {
            $this->authService->validateResourcesAccess(
                entity: 'products',
                cabinetId: $storageArea->cabinet_id,
                ids: $dto->products_id,
            );
        }
    }

    public function index(string $warehouseId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouses',
            entityId: $warehouseId,
            permission: PermissionNameEnum::WAREHOUSES->value,
            operation: 'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_storage_areas',
            entityId: $resourceId,
            relatedEntity: [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_storage_areas',
            entityId: $resourceId,
            relatedEntity: [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $resourceId
            ]
        );
    }
}
