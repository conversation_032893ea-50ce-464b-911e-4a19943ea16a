<?php

namespace App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WarehousePhonesRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\DTO\WarehousePhoneDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class WarehousePhonesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly WarehousePhonesRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof WarehousePhoneDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $this->repository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
