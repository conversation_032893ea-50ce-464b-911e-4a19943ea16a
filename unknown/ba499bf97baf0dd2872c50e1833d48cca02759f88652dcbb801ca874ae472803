<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class WarehouseDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $name,
        public ?string $resourceId,
        public string $employeeId,
        public string $departmentId,
        public bool $control_free_residuals,
        public ?string $work_schedule_id = null,
        public array $order_scheme = [],
        public array $structure = [],
        public ?string $responsibleEmployeeId = null,
        public bool $isCommon = false,
        public ?string $phone_id = null,
        public ?string $address_id = null,
        public bool $isDefault = false,
        public ?string $groupId = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['id'] ?? null,
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            control_free_residuals: $data['control_free_residuals'] ?? false,
            work_schedule_id: $data['work_schedule_id'] ?? null,
            order_scheme: $data['order_scheme'] ?? [],
            structure: $data['structure'] ?? [],
            responsibleEmployeeId: $data['responsible_employee_id'] ?? null,
            isCommon: $data['is_common'] ?? false,
            phone_id: $data['contacts']['phone_id'] ?? null,
            address_id: $data['contacts']['address_id'] ?? null,
            isDefault: $data['is_default'] ?? false,
            groupId: $data['group_id'] ?? null
        );
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'name' => $this->name,
            'cabinet_id' => $this->cabinet_id,
            'group_id' => $this->groupId,
            'work_schedule_id' => $this->work_schedule_id,
            'control_free_residuals' => $this->control_free_residuals,
            'address_id' => $this->address_id,
            'phone_id' => $this->phone_id,
            'department_id' => $this->departmentId,
            'employee_id' => $this->employeeId,
            'is_common' => $this->isCommon,
            'responsible_employee_id' => $this->responsibleEmployeeId,
            'is_default' => $this->isDefault
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'group_id' => $this->groupId,
            'work_schedule_id' => $this->work_schedule_id,
            'control_free_residuals' => $this->control_free_residuals,
            'address_id' => $this->address_id,
            'phone_id' => $this->phone_id,
            'department_id' => $this->departmentId,
            'employee_id' => $this->employeeId,
            'is_common' => $this->isCommon,
            'responsible_employee_id' => $this->responsibleEmployeeId,
            'is_default' => $this->isDefault
        ];
    }
}
