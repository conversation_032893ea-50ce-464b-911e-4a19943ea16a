<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WorkSchedulesRepositoryContract;
use App\Jobs\GenerateCalendarJob;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\DTO\WorkScheduleDTO;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Helpers\FilterTemplateCleaner;
use App\Traits\HasOrderedUuid;
use http\Exception\InvalidArgumentException;

class WorkSchedulesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly WorkSchedulesRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof WorkScheduleDTO) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }
        $data = $dto->toInsertArray($this->resourceId);

        $cleanedTemplate = FilterTemplateCleaner::cleanAndFilterTemplate($dto->filling_template);
        $data['filling_template'] = json_encode($cleanedTemplate);

        $cleanedTemplate = FilterTemplateCleaner::cleanAndFilterTemplate($dto->holiday_schedule);
        $data['holiday_schedule'] = json_encode($cleanedTemplate);

        $this->repository->insert($data);

        GenerateCalendarJob::dispatch($this->resourceId);

        return $this->resourceId;
    }



}
