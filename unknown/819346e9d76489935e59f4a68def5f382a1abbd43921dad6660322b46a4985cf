<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Contractors\ContractorGroupsPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\DTO\ContractorGroupsDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class ContractorGroupsPolicy implements ContractorGroupsPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ContractorGroupsDTO) {
            return;
        }

        $this->authService->init();

        $this->authService->hasAccessToCabinet($dto->cabinetId);

    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ContractorGroupsDTO) {
            return;
        }

        $this->authService->init();

        $this->authService->validateRelationAccess(
            'contractor_groups',
            $dto->resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->init();

        $this->authService->validateRelationAccess(
            'contractor_groups',
            $resourceId
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->init();

        $this->authService->validateRelationAccess(
            'contractor_groups',
            $resourceId
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->init();

        $this->authService->validateResourcesAccess(
            'contractor_groups',
            $data['cabinet_id'],
            $data['ids']
        );
    }

}
