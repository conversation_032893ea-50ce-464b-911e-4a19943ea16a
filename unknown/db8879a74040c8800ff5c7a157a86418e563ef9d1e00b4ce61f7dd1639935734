<?php

namespace App\Services\Api\Internal\GoodsTransferFifoService;

use App\Contracts\Services\Internal\GoodsTransferFifoServiceContract;
use App\Services\Api\Internal\GoodsTransferFifoService\Handlers\GoodsTransferFifoHandler;

readonly class GoodsTransferFifoService implements GoodsTransferFifoServiceContract
{
    public function __construct(
        private GoodsTransferFifoHandler $fifoHandler
    ) {
    }

    public function handle(string $resourceId, bool $delete = false): void
    {
        $this->fifoHandler->run($resourceId, $delete);
    }
    
    public function handleBulk(array $transferItemIds, bool $delete = false): void
    {
        $this->fifoHandler->handleBulk($transferItemIds, $delete);
    }
}
