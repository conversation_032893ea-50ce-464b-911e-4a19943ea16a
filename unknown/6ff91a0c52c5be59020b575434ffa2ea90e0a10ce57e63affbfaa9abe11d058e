<?php

declare(strict_types=1);

namespace App\Clients\WB\API;

use App\Clients\WB\Exception\ApiClientException;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use GuzzleHttp\Exception\GuzzleException;
use InvalidArgumentException;

/**
 * Базовый абстрактный класс для всех конечных точек API Wildberries
 *
 * Предоставляет общую функциональность для выполнения HTTP-запросов,
 * обработки ошибок и управления ответами API.
 */
abstract class AbstractEndpoint
{
    /** @var string Локаль API (например, 'ru') */
    private string $locale;

    /** @var int Количество попыток выполнения запроса при ошибке 429 */
    private int $attempts = 1;

    /** @var int Задержка между попытками в миллисекундах */
    private int $retryDelay = 0;

    /** @var Client HTTP клиент для выполнения запросов */
    private Client $Client;

    /**
     * Создает новый экземпляр конечной точки API
     *
     * @param string $baseUrl Базовый URL API
     * @param string $key API ключ для авторизации
     * @param string|null $proxy URL прокси-сервера (опционально)
     * @param string|null $locale Локаль API (опционально, по умолчанию 'ru')
     */
    public function __construct(string $baseUrl, string $key, ?string $proxy = null, ?string $locale = null)
    {
        $this->locale = $locale ?? 'ru';
        $this->Client = new Client(rtrim($baseUrl, '/'), $key, $proxy);
        if (method_exists($this, 'middleware')) {
            $this->Client->addMiddleware($this->middleware());
        }
    }

    /**
     * Магический метод для перехвата вызовов методов запросов
     *
     * @param string $method Имя вызываемого метода
     * @param array $parameters Параметры метода
     * @return mixed Результат выполнения метода
     *
     * @throws InvalidArgumentException Если метод не найден или не является методом запроса
     */
    public function __call(string $method, array $parameters): mixed
    {
        if (method_exists($this, $method)
            && in_array($method, ['getRequest', 'postRequest', 'putRequest', 'patchRequest', 'deleteRequest', 'multipartRequest'])
        ) {
            return call_user_func_array([$this, $method], $parameters);
        }
        throw new InvalidArgumentException("Метод {$method} не существует или не является методом запроса");
    }

    /**
     * Проверка подключения к WB API
     *
     * @link https://openapi.wildberries.ru/general/ping/ru/#/paths/~1ping/get
     *
     * @return object {TS: string, status: "OK"}
     */
    public function ping(): object
    {
        return $this->getRequest('/ping');
    }

    /**
     * Выполняет GET-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Параметры строки запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     * @throws GuzzleException
     */
    protected function getRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('GET', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет HTTP-запрос к API и обрабатывает ошибки
     *
     * @param string $method HTTP-метод (GET, POST, PUT, PATCH, DELETE, MULTIPART)
     * @param string $path Путь к ресурсу API
     * @param array $data Параметры запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException|GuzzleException Если действуют временные ограничения
     */
    private function request(string $method, string $path, array $data = [], array $addonHeaders = []): mixed
    {
        $attempt = 1;
        $maxAttempts = $this->attempts;

        while (true) {
            $result = $this->Client->request($method, $path, $data, $addonHeaders);

            // Проверка на временные ограничения в тексте ошибки
            if (
                is_object($result)
                &&
                property_exists($result, 'errorText')
                &&
                $this->responseCode() == 400
                &&
                mb_stripos($result->errorText, 'временные ограничения') !== false
            ) {
                throw new ApiTimeRestrictionsException($result->errorText);
            }

            // Обработка ошибок авторизации
            if ($this->responseCode() == 401) {
                /*
                 * "401 Unauthorized"
                 *
                 * (api-new) can\'t decode supplier key
                 * (api-new) some chars in key are wrong
                 * (api-new) supplier key not found
                 * proxy: invalid token
                 * proxy: unauthorized
                 * request rejected, unathorized
                 */
                if (is_string($result)) {
                    throw new ApiClientException($result, 401);
                }

                if (is_object($result) && property_exists($result, 'errors') && count($result->errors)) {
                    throw new ApiClientException($result->errors[0], 401);
                }

                throw new ApiClientException('Unauthorized', 401);
            }

            // Обработка ошибок превышения лимита запросов
            if ($this->responseCode() == 429) {
                /*
                 * "429 Too Many Requests"
                 *
                 * { errors: ["Технический перерыв до 16:00"] }
                 * { errors: ["(api-new) too many requests"] }
                 * { code: 429, message: "" }
                 */
                $message = 'Too many requests';

                if (property_exists($result, 'errors') && !empty($result->errors)) {
                    $message = $result->errors[0];
                } elseif (property_exists($result, 'message') && !empty($result->message)) {
                    $message = $result->message;
                }

                // Проверка на технический перерыв
                if (mb_stripos($message, 'технический перерыв') !== false) {
                    throw new ApiTimeRestrictionsException($message);
                }

                // Проверка на превышение количества попыток
                if ($attempt >= $maxAttempts) {
                    throw new ApiClientException($message, 429);
                }

                // Ожидание перед следующей попыткой
                usleep($this->retryDelay * 1_000);
                $attempt++;

                continue; // Продолжаем цикл и повторяем запрос
            }

            // Обработка ошибок тайм-аута шлюза
            if ($this->responseCode() == 504) {
                /*
                 * "504 Gateway Time-out"
                 */
                if ($attempt >= $maxAttempts) {
                    throw new ApiClientException('Gateway Time-out', 504);
                }

                // Ожидание перед следующей попыткой
                usleep($this->retryDelay * 1_000);
                $attempt++;

                continue; // Продолжаем цикл и повторяем запрос
            }

            // Если нет ошибок, требующих повторного запроса, возвращаем результат
            return $result;
        }
    }

    /**
     * Возвращает HTTP код ответа последнего запроса
     *
     * @return int HTTP код ответа (200, 404, 500 и т.д.)
     */
    public function responseCode(): int
    {
        return $this->Client->responseCode;
    }

    /**
     * Возвращает текущую локаль API
     *
     * @return string Локаль API (например, 'ru')
     */
    public function locale(): string
    {
        return $this->locale;
    }

    /**
     * Автоматически повторять запросы в случае ответа сервера "429 Too Many Requests"
     *
     * @param int $attempts Количество попыток выполнения запроса
     * @param int $delay Задержка в миллисекундах между попытками
     */
    public function retryOnTooManyRequests(int $attempts = 5, int $delay = 5_000): self
    {
        $this->attempts = $attempts;
        $this->retryDelay = $delay;

        return $this;
    }

    /**
     * Возвращает текстовое описание HTTP кода ответа
     *
     * @return string|null Текстовое описание (OK, Not Found, Internal Server Error и т.д.)
     */
    public function responsePhrase(): ?string
    {
        return $this->Client->responsePhrase;
    }

    /**
     * Возвращает заголовки ответа
     *
     * @return array Массив заголовков ответа
     */
    public function responseHeaders(): array
    {
        return $this->Client->responseHeaders;
    }

    /**
     * Возвращает сырое содержимое ответа
     *
     * @return string|null Сырое содержимое ответа (обычно JSON-строка)
     */
    public function rawResponse(): ?string
    {
        return $this->Client->rawResponse;
    }

    /**
     * Возвращает обработанный ответ
     *
     * @return mixed Обработанный ответ (объект или строка)
     */
    public function response(): mixed
    {
        return $this->Client->response;
    }

    /**
     * Возвращает информацию о лимитах запросов API
     *
     * @return array Массив с информацией о лимитах запросов
     */
    public function responseRate(): array
    {
        return [
            'limit' => $this->Client->rateLimit,         // Максимальное количество запросов
            'remaining' => $this->Client->rateRemaining, // Оставшееся количество запросов
            'reset' => $this->Client->rateReset,         // Время сброса ограничения в секундах
            'retry' => $this->Client->rateRetry,         // Рекомендуемое время ожидания в миллисекундах
        ];
    }

    /**
     * Выполняет POST-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function postRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('POST', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет PUT-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function putRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('PUT', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет PATCH-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function patchRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('PATCH', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет DELETE-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function deleteRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('DELETE', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет мультичастный запрос к API (для загрузки файлов)
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Данные для мультичастного запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiClientException Если произошла ошибка клиента
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function multipartRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('MULTIPART', $path, $data, $addonHeaders);
    }
}
