<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Warehouses;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Ozon\Jobs\LoadFBOWarehousesJob;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

readonly class LoadFBOAction
{
    /**
     * @throws WBSellerException
     * @throws Exception
     * @throws Throwable
     */
    public function run(string $integrationId): void
    {
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->first();

        $apiKey = decrypt($integration->api_key);
        $clientId = decrypt($integration->client_id);

        $job = new LoadFBOWarehousesJob(
            cabinetId: $integration->cabinet_id,
            integrationId: $integrationId,
            apiKey: $apiKey,
            clientId: $clientId,
        );
        $job->handle();
    }
}
