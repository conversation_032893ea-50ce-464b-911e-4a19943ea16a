<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Create instance manually to avoid validation
$entity = app(\App\Entities\AcceptanceEntity::class);
$request = new \App\Http\Requests\Api\Internal\Acceptances\AcceptanceIndexRequest($entity);
$rules = $request->rules();

echo "Rules with excludeIf:" . PHP_EOL;
foreach ($rules as $fieldName => $fieldRules) {
    $containsExcludeIf = false;
    
    if (is_string($fieldRules)) {
        $containsExcludeIf = str_contains($fieldRules, 'excludeIf') || str_ends_with($fieldRules, '|');
    } elseif (is_array($fieldRules)) {
        foreach ($fieldRules as $rule) {
            if ($rule instanceof \Illuminate\Validation\Rules\ExcludeIf) {
                $containsExcludeIf = true;
                break;
            }
            if (is_string($rule) && str_contains($rule, 'excludeIf')) {
                $containsExcludeIf = true;
                break;
            }
        }
    }
    
    if ($containsExcludeIf) {
        echo "- $fieldName" . PHP_EOL;
        echo "  Parts: " . implode(' -> ', explode('.', $fieldName)) . PHP_EOL;
        if (str_ends_with($fieldName, '.*')) {
            echo "  Is array: YES" . PHP_EOL;
        } else {
            echo "  Is array: NO" . PHP_EOL;
        }
        echo PHP_EOL;
    }
}
