<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];
echo 'Parameters: ' . (isset($acceptancesGet['parameters']) ? count($acceptancesGet['parameters']) : 0) . PHP_EOL;

// Group parameters by name to find duplicates
$paramsByName = [];
if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        if (!isset($paramsByName[$name])) {
            $paramsByName[$name] = [];
        }
        $paramsByName[$name][] = $param;
    }
}

echo PHP_EOL . 'Duplicates:' . PHP_EOL;
foreach ($paramsByName as $name => $params) {
    if (count($params) > 1) {
        echo "- $name (" . count($params) . " times)" . PHP_EOL;
        foreach ($params as $i => $param) {
            $type = $param['schema']['type'] ?? 'unknown';
            $format = isset($param['schema']['format']) ? " ({$param['schema']['format']})" : '';
            echo "  [$i] $type$format" . PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Parameters with excludeIf-related names:' . PHP_EOL;
foreach ($paramsByName as $name => $params) {
    if (str_contains($name, 'contractor_owners') || str_contains($name, 'warehouses')) {
        echo "- $name: " . $params[0]['schema']['type'] . PHP_EOL;
    }
}
