{"(and :count more error)": "(и ещё :count оши<PERSON>ка)", "(and :count more errors)": "(и ещё :count ошибк<PERSON>)|(и ещё :count ошибк<PERSON>)|(и ещё :count ошибок)", "A decryption key is required.": "Ключ дешифровки обязателен.", "All rights reserved.": "Все права защищены.", "Encrypted environment file already exists.": "Зашифрованный файл настроек окружения уже существует.", "Encrypted environment file not found.": "Зашифрованный файл настроек окружения не найден.", "Environment file already exists.": "Файл настроек окружения уже существует.", "Environment file not found.": "Файл настроек окружения не найден.", "errors": "ошибки", "Forbidden": "Запрещено", "Go to page :page": "Перейти к :page-й странице", "Hello!": "Здравствуйте!", "If you did not create an account, no further action is required.": "Если Вы не создавали учетную запись, никаких дополнительных действий не требуется.", "If you did not request a password reset, no further action is required.": "Если Вы не запрашивали восстановление пароля, никаких дополнительных действий не требуется.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "Если у Вас возникли проблемы с нажатием кнопки \":actionText\", скопируйте и вставьте приведенный ниже URL-адрес в свой браузер:", "Invalid filename.": "Некорректное имя файла.", "Invalid JSON was returned from the route.": "Маршрут вернул некорректный JSON.", "length": "длина", "Location": "Местоположение", "Login": "Войти", "Logout": "Выйти", "Not Found": "Не найдено", "of": "из", "Page Expired": "Страница устарела", "Pagination Navigation": "Навигация", "Payment Required": "Требуется оплата", "Please click the button below to verify your email address.": "Пожалуйста, нажмите кнопку ниже, чтобы подтвердить свой адрес электронной почты.", "Regards": "С уважением", "Regards,": "С уважением,", "Register": "Регистрация", "Reset Password": "Сбросить пароль", "Reset Password Notification": "Оповещение о сбросе пароля", "results": "результа<PERSON>ов", "Server Error": "Ошибка сервера", "Service Unavailable": "Сервис недоступен", "Showing": "Показано с", "The given data was invalid.": "Указанные данные недействительны.", "The response is not a streamed response.": "Ответ не является потоковым.", "The response is not a view.": "Ответ не является представлением.", "This action is unauthorized.": "Действие не авторизовано.", "This password reset link will expire in :count minutes.": "Срок действия ссылки для сброса пароля истекает через :count минут.", "to": "по", "Toggle navigation": "Переключить навигацию", "Too Many Requests": "Слишком много запросов", "Unauthorized": "Не авторизован", "Verify Email Address": "Подтвердить адрес электронной почты", "Whoops!": "Упс!", "You are receiving this email because we received a password reset request for your account.": "Вы получили это письмо, потому что мы получили запрос на сброс пароля для Вашей учётной записи."}