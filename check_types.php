<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];

echo 'Parameter types and details:' . PHP_EOL;

if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        $name = $param['name'];
        $schema = $param['schema'];
        $required = $param['required'] ?? false ? ' [required]' : ' [optional]';
        
        echo "- $name: ";

        if ($schema['type'] === 'object') {
            echo "object$required" . PHP_EOL;
        } else {
            // Handle array type in schema
            if (is_array($schema['type'])) {
                echo implode('|', $schema['type']);
            } else {
                echo $schema['type'];
            }
            
            // Format
            if (isset($schema['format'])) {
                echo "<" . $schema['format'] . ">";
            }
            
            // Enum values
            if (isset($schema['enum'])) {
                echo " enum[" . implode(',', $schema['enum']) . "]";
            }
            
            // Min/Max
            if (isset($schema['minimum'])) {
                echo " min:" . $schema['minimum'];
            }
            if (isset($schema['maximum'])) {
                echo " max:" . $schema['maximum'];
            }
            
            // Array items
            if ($schema['type'] === 'array' && isset($schema['items'])) {
                echo "[" . $schema['items']['type'];
                if (isset($schema['items']['format'])) {
                    echo "<" . $schema['items']['format'] . ">";
                }
                echo "]";
            }
            
            // Nullable
            if (isset($schema['nullable']) && $schema['nullable']) {
                echo " nullable";
            }
            
            echo $required . PHP_EOL;
        }
    }
}

echo PHP_EOL . 'Total parameters: ' . (isset($acceptancesGet['parameters']) ? count($acceptancesGet['parameters']) : 0) . PHP_EOL;
