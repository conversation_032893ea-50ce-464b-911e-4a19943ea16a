<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Contractor;
use App\Models\VendorOrder;
use App\Models\LegalEntity;
use App\Models\Department;
use Illuminate\Database\Seeder;

class VendorOrdersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create employees and departments first, then store the results for later use
        $employees = Employee::factory()->count(100)->create();
        $departments = Department::factory()->count(50)->create();

        // Create legal entities, contractors, and warehouses
        $legals = LegalEntity::factory()->count(10)->create([
            'employee_id' => $employees->random()->id,
            'department_id' => $departments->random()->id
        ]);

        $contractors = Contractor::factory()->count(10)->create([
            'employee_id' => $employees->random()->id,
            'department_id' => $departments->random()->id
        ]);

        $warehouses = Warehouse::factory()->count(50)->create([
            'employee_id' => $employees->random()->id,
            'department_id' => $departments->random()->id
        ]);

        // Prepare vendor orders for bulk creation
        for ($i = 0; $i < 10000; $i++) {
            VendorOrder::factory()->create([
                'legal_entity_id' => $legals->random()->id,
                'contractor_id' => $contractors->random()->id,
                'warehouse_id' => $warehouses->random()->id,
                'employee_id' => $employees->random()->id,
                'department_id' => $departments->random()->id,
            ]);
        }
    }
}
