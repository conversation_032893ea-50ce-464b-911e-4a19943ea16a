<?php

namespace Database\Seeders;

use App\Models\SalesChannelType;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Seeder;

class SalesChannelTypeSeeder extends Seeder
{
    use HasOrderedUuid;
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $names = ['Мессенджер','Социальная сеть','Маркетплейс','Интернет-магазин','Доска объявлений','Прямые продажи','Розничные продажи','Другое'];

        foreach ($names as $name) {
            SalesChannelType::factory()->create(['name' => $name, 'id' => $this->generateUuid()]);
        }
    }
}
