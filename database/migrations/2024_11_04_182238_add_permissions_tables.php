<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->string('name');
            $table->foreignUuid('cabinet_id')->nullable()->constrained()->cascadeOnDelete();
            $table->boolean('is_system')->default(false);

            $table->unique(['name', 'cabinet_id']);
        });

        Schema::create('permission_groups', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
        });
        Schema::create('permission_categories', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
        });

        Schema::create('permissions', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->boolean('require_scope')->default(false);
            $table->string('guard_name');
            $table->string('operation');
            $table->foreignUuid('group_id')->referenced('id')->on('permission_groups')->cascadeOnDelete();
            $table->foreignUuid('category_id')->referenced('id')->on('permission_categories')->cascadeOnDelete();
            $table->unique(['guard_name', 'operation']);
        });

        Schema::create('role_permissions', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('role_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('permission_id')->constrained()->cascadeOnDelete();

            $table->string('scope')->nullable();
        });

        Schema::create('employee_permissions', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('employee_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('permission_id')->constrained()->cascadeOnDelete();

            $table->string('scope')->nullable();
        });

        Schema::create('department_permissions', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('permission_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('department_id')->constrained()->cascadeOnDelete();

            $table->string('scope')->nullable();
        });

        Schema::table('employees', function ($table) {
            $table->foreignUuid('role_id')->nullable()->constrained()->cascadeOnDelete();
            $table->foreignUuid('department_id')->nullable()->constrained()->cascadeOnDelete();
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('employee_permissions');
        Schema::dropIfExists('department_permissions');
        Schema::table('employees', function ($table) {
            $table->dropColumn(['role_id', 'department_id']);
        });
        Schema::dropIfExists('roles');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('permission_categories');
        Schema::dropIfExists('permission_groups');
    }
};
