<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contractor_accounts', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('contractor_id')->constrained()->cascadeOnDelete();

            $table->boolean('is_main')->default(false);

            $table->string('bik', 9)->nullable();
            $table->string('correspondent_account', 20)->nullable();
            $table->string('payment_account', 34)->nullable();

            $table->decimal('balance')->default(0);

            $table->string('bank')->nullable();
            $table->string('address')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contractor_accounts');
    }
};
