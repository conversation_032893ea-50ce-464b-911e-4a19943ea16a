<?php

use App\Enums\Api\Internal\ContractTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->dateTime('archived_at')->nullable();

            $table->string('number');
            $table->dateTime('date_from');

            $table->foreignUuid('status_id')->nullable()->constrained();

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();

            $table->string('type')->default(ContractTypeEnum::COMISSION->value);
            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');

            $table->string('code')->nullable();
            $table->string('amount')->nullable();

            $table->text('comment')->nullable();

            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();

            $table->boolean('shared_access')->default(false);
            $table->boolean('is_printed')->default(false);
            $table->boolean('is_sended')->default(false);

            $table->foreignUuid('cabinet_id')->constrained();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
