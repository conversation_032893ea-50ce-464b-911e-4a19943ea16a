<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods_transfer_warehouse_items', function (Blueprint $table) {
            $table->foreignUuid('goods_transfer_item_id')->references('id')->on('goods_transfer_items')->cascadeOnDelete();
            $table->foreignUuid('warehouse_item_id')->references('id')->on('warehouse_items')->cascadeOnDelete();
            $table->integer('quantity');
            $table->dateTime('transfer_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_transfer_warehouse_items');
    }
};
