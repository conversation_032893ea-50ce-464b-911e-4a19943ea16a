<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_accounting_features', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained()->cascadeOnDelete();
            $table->string('pack_type')->nullable();                  // Фасовка	                enum
            $table->string('type_accounting')->nullable();          // Тип учёта	            enum
            $table->boolean('accounting_series')->default(false);       // Учет по сериям           boolean
            $table->string('product_siz_name_id')->nullable();      // Наименование для Средства инд защиты - из БД
            $table->string('product_siz_type_id')->nullable();      // Тип для средств инд защиты - из БД
            $table->string('product_type_code')->nullable();        // Код вида продукции       string
            $table->double('container_capacity')->nullable();       // Емкость тары, л.         integer
            $table->double('strength')->nullable();                 // Крепость, максимум 100
            $table->boolean('excise')->default(false);                  // Содержит акцизную марку  boolean
            $table->string('product_type_id')->nullable();          // Тип продукции            enum
            $table->foreignUuid('tnwed_id')->nullable()->references('id')->on('tnwed_full');   // ТН ВЭД    
            $table->string('target_gender')->nullable();            // Целевой пол              enum
            $table->string('type_production')->nullable();          // Тип производства         enum
            $table->string('age_category')->nullable();             // Возрастная категория     enum
            $table->boolean('set')->default(false);                     // Комплект                 boolean
            $table->boolean('partial_sale')->default(false);            // Частичная продажа        boolean
            $table->string('model')->nullable();                    // Модель                   string
            $table->boolean('traceable')->default(false);               // Прослеживаемый
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_accounting_features');
    }
};
