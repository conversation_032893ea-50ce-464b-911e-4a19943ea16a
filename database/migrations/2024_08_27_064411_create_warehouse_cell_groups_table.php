<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_cell_groups', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('address');
            $table->text('description')->nullable();
            $table->foreignUuid('warehouse_id')->constrained()->cascadeOnDelete();
        });

        Schema::table('warehouse_cell_groups', function (Blueprint $table) {
            $table->foreignUuid('parent_id')->nullable()->references('id')->on('warehouse_cell_groups')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_cell_groups');
    }
};
