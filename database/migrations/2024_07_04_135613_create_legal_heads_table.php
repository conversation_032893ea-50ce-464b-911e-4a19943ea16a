<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_heads', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('legal_entity_id')->constrained()->cascadeOnDelete();

            $table->string('head_name')->nullable();
            $table->string('head_position')->nullable();

            $table->string('accountant_name')->nullable();

            /*$table->string('head_signature')->nullable(); //файлы
            $table->string('accountant_signature')->nullable();
            $table->string('stamp')->nullable();*/

            $table->unique('legal_entity_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_heads');
    }
};
