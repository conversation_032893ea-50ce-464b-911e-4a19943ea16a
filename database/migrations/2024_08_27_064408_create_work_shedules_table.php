<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_work_schedules', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->date('date_from');
            $table->date('date_to');
            $table->integer('filling_type');
            $table->integer('cycle_day_lenght')->nullable();
            $table->date('cycle_day_from')->nullable();
            $table->boolean('keep_holidays')->default(false);
            $table->json('holiday_schedule')->nullable();
            $table->json('filling_template')->nullable();
            $table->integer('planning_horizon')->default(0);
        });

        Schema::create('warehouse_calendars', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('schedule_id')->references('id')->on('warehouse_work_schedules')->cascadeOnDelete();
            $table->date('date');
            $table->boolean('is_working_day')->default(false); //Является ли день рабочим
            $table->boolean('is_holiday')->default(false);  // Является ли день праздничным

            // Указывает на шаблон в warehouse_work_schedules
            $table->string('template_reference')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_calendars');
        Schema::dropIfExists('warehouse_work_schedules');
    }
};
