<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods_transfer_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('goods_transfer_id')->references('id')->on('goods_transfers')->cascadeOnDelete();
            $table->foreignUuid('product_id')->references('id')->on('products');
            $table->integer('quantity');
            $table->bigInteger('price')->default(0);
            $table->bigInteger('total_price')->default(0);
            $table->bigInteger('recidual_from')->default(0);
            $table->bigInteger('recidual_to')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_transfer_items');
    }
};
