<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contractor_detail_address', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('contractor_detail_id')->constrained()->cascadeOnDelete();
            $table->unique('contractor_detail_id');

            $table->string('postcode')->nullable();
            $table->string('country')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->string('street')->nullable();
            $table->string('house')->nullable();
            $table->string('office')->nullable();
            $table->string('other')->nullable();
            $table->text('comment')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contractor_detail_address');
    }
};
