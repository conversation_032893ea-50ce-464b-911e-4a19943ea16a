<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vat_rates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('cabinet_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->integer('rate');
            $table->text('description')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_common')->default(false);

            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vat_rates');
    }
};
