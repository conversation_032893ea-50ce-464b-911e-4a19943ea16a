<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cabinet_settings', function (Blueprint $table) {
            $table->foreignUuid('cabinet_id')->constrained()->onDelete('cascade');
            $table->string('numbering_type', 50)->default('only_numbers');
            $table->string('email', 255)->nullable();
            $table->boolean('global_numbering')->default(false);
            $table->boolean('use_cabinet_email')->default(false);
            $table->boolean('check_stock')->default(false);
            $table->boolean('check_min_price')->default(false);
            $table->boolean('use_bin')->default(true);
            $table->boolean('use_product_series')->default(false);
            $table->boolean('auto_update_purchase_price')->default(false);
            $table->string('image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cabinet_settings');
    }
};
