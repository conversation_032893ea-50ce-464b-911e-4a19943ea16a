<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_groups', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
            $table->foreignUuid('cabinet_id')->nullable();
        });

        Schema::table('warehouse_groups', function (Blueprint $table) {
            $table->foreignUuid('parent_id')->nullable()->references('id')->on('warehouse_groups')->cascadeOnDelete();
        });

        Schema::table('warehouses', function (Blueprint $table) {
            $table->foreignUuid('group_id')->nullable()->references('id')->on('warehouse_groups');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_groups');
    }
};
