<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();
            // TODO Возможно нужно добавить id кабинета Озон
            $table->string('offer_id')->unique();                             // Артикул
            $table->string('sku')->unique();                                  // Ozon SKU ID
            $table->string('name');                                             // Название
            $table->string('state_name');                                       // Статус
            $table->string('visible');                                          // Видимость на OZON
            $table->decimal('volume_weight', 8, 2)->nullable()->default(0);     // Объемный вес, кг
            $table->string('barcode')->nullable();                              // штрихкод
            $table->bigInteger('old_price')->nullable()->default(0);            // Цена до скидки, руб.
            $table->bigInteger('price')->nullable()->default(0);                // Текущая цена (со скидкой), руб.
            $table->bigInteger('marketing_seller_price')->nullable()->default(0);// Цена с учетом акции или стратегии, руб.
            $table->bigInteger('marketing_price')->nullable()->default(0);      // Цена до скидки, руб. - Товары и цены
            $table->bigInteger('fbo_min_exp')->nullable();                      // сумма расходов min fbo = acquiring + marketing_seller_price*(1+sales_percent_fbo/100) + fbo_direct_flow_trans_min_amount + fbo_deliv_to_customer_amount
            $table->bigInteger('fbo_max_exp')->nullable();                      // сумма расходов max fbo = acquiring + marketing_seller_price*(1+sales_percent_fbo/100) + fbo_direct_flow_trans_max_amount + fbo_deliv_to_customer_amount
            $table->bigInteger('fbs_min_exp')->nullable();                      // сумма расходов min fbs = acquiring + marketing_seller_price*(1+sales_percent_fbs/100) + fbo_direct_flow_trans_max_amount + fbo_deliv_to_customer_amount + fbs_first_mile_min_amount
            $table->bigInteger('fbs_max_exp')->nullable();                      // сумма расходов max fbs = acquiring + marketing_seller_price*(1+sales_percent_fbs/100) + fbo_direct_flow_trans_max_amount + fbo_deliv_to_customer_amount + fbs_first_mile_max_amount
            $table->bigInteger('acquiring')->nullable()->default(0);            // Эквайринг
            $table->decimal('sales_percent_fbo', 5, 2)->nullable()->default(0);            // Вознаграждение Ozon, FBO, %
            $table->bigInteger('fbo_direct_flow_trans_min_amount')->nullable()->default(0); // Логистика Ozon, минимум, FBO
            $table->bigInteger('fbo_direct_flow_trans_max_amount')->nullable()->default(0); // Логистика Ozon, максимум, FBO
            $table->bigInteger('fbo_deliv_to_customer_amount')->nullable()->default(0);     // Последняя миля, FBO
            $table->decimal('sales_percent_fbs', 5, 2)->nullable()->default(0);            // Вознаграждение Ozon, FBS, %
            $table->bigInteger('fbs_first_mile_min_amount')->nullable()->default(0);        // Обработка отправления, минимум FBS
            $table->bigInteger('fbs_first_mile_max_amount')->nullable()->default(0);        // Обработка отправления, максимум FBS
            $table->bigInteger('fbs_direct_flow_trans_min_amount')->nullable()->default(0); // Логистика Ozon, минимум, FBS
            $table->bigInteger('fbs_direct_flow_trans_max_amount')->nullable()->default(0); // Логистика Ozon, максимум, FBS
            $table->bigInteger('fbs_deliv_to_customer_amount')->nullable()->default(0);     // Последняя миля, FBS
            $table->json('marketing_actions')->nullable();                                  // в каких акциях участвует товар

            $table->timestamps();
        });

        // Создание функции для вычисления поля fbo_min_exp
        DB::unprepared('
                CREATE OR REPLACE FUNCTION update_fbo_min_exp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.fbo_min_exp := NEW.acquiring + NEW.marketing_seller_price * (1 + NEW.sales_percent_fbs / 100) + NEW.fbo_direct_flow_trans_min_amount + NEW.fbo_deliv_to_customer_amount;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            ');

        // Создание функции для вычисления поля fbo_max_exp
        DB::unprepared('
                CREATE OR REPLACE FUNCTION update_fbo_max_exp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.fbo_max_exp := NEW.acquiring + NEW.marketing_seller_price * (1 + NEW.sales_percent_fbs / 100) + NEW.fbo_direct_flow_trans_max_amount + NEW.fbo_deliv_to_customer_amount;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            ');

        // Создание функции для вычисления поля fbs_min_exp
        DB::unprepared('
                CREATE OR REPLACE FUNCTION update_fbs_min_exp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.fbs_min_exp := NEW.acquiring + NEW.marketing_seller_price * (1 + NEW.sales_percent_fbs / 100) + NEW.fbo_direct_flow_trans_max_amount + NEW.fbo_deliv_to_customer_amount + NEW.fbs_first_mile_min_amount;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            ');

        // Создание функции для вычисления поля fbs_max_exp
        DB::unprepared('
                CREATE OR REPLACE FUNCTION update_fbs_max_exp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.fbs_max_exp := NEW.acquiring + NEW.marketing_seller_price * (1 + NEW.sales_percent_fbs / 100) + NEW.fbo_direct_flow_trans_max_amount + NEW.fbo_deliv_to_customer_amount + NEW.fbs_first_mile_max_amount;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            ');

        // Создание функции для вычисления поля marketing_price
        DB::unprepared('
                CREATE OR REPLACE FUNCTION update_marketing_price()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.marketing_price := NEW.old_price - (NEW.old_price - NEW.marketing_seller_price);
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            ');

        // Создание триггера для вызова функций при вставке или обновлении
        DB::unprepared('
                CREATE TRIGGER update_fbo_min_exp_trigger
                BEFORE INSERT OR UPDATE ON ozon_products
                FOR EACH ROW
                EXECUTE FUNCTION update_fbo_min_exp();
            ');

        DB::unprepared('
                CREATE TRIGGER update_fbo_max_exp_trigger
                BEFORE INSERT OR UPDATE ON ozon_products
                FOR EACH ROW
                EXECUTE FUNCTION update_fbo_max_exp();
            ');

        DB::unprepared('
                CREATE TRIGGER update_fbs_min_exp_trigger
                BEFORE INSERT OR UPDATE ON ozon_products
                FOR EACH ROW
                EXECUTE FUNCTION update_fbs_min_exp();
            ');

        DB::unprepared('
                CREATE TRIGGER update_fbs_max_exp_trigger
                BEFORE INSERT OR UPDATE ON ozon_products
                FOR EACH ROW
                EXECUTE FUNCTION update_fbs_max_exp();
            ');


        DB::unprepared('
                CREATE TRIGGER update_marketing_price_trigger
                BEFORE INSERT OR UPDATE ON ozon_products
                FOR EACH ROW
                EXECUTE FUNCTION update_marketing_price();
            ');

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_products');

        DB::unprepared('DROP TRIGGER IF EXISTS update_fbo_min_exp_trigger ON ozon_products');
        DB::unprepared('DROP TRIGGER IF EXISTS update_fbo_max_exp_trigger ON ozon_products');
        DB::unprepared('DROP TRIGGER IF EXISTS update_fbs_min_exp_trigger ON ozon_products');
        DB::unprepared('DROP TRIGGER IF EXISTS update_fbs_max_exp_trigger ON ozon_products');
        DB::unprepared('DROP FUNCTION IF EXISTS update_fbo_min_exp');
        DB::unprepared('DROP FUNCTION IF EXISTS update_fbo_max_exp');
        DB::unprepared('DROP FUNCTION IF EXISTS update_fbs_min_exp');
        DB::unprepared('DROP FUNCTION IF EXISTS update_fbs_max_exp');
        DB::unprepared('DROP FUNCTION IF EXISTS update_marketing_price');

    }
};
