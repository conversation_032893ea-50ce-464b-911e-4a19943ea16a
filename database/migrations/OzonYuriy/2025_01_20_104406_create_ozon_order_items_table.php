<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('ozon_order_id')->constrained()->cascadeOnDelete();
            $table->string('posting_number'); // 'Номер отправления
            $table->string('inner_posting_number')->unique(); // 'Номер отправления наш внутренний
            $table->datetime('in_process_at')->nullable(); // 'Принят в обработку
            $table->datetime('shipment_date')->nullable(); // 'Дата отгрузки
            $table->string('status')->nullable(); // 'Статус
            $table->datetime('delivering_date')->nullable(); // 'Дата доставки
            $table->datetime('delivery_date_end')->nullable(); // 'Фактическая дата передачи в доставку
            $table->decimal('amount', 20, 4)->nullable()->default(0); // 'Сумма отправления
            $table->string('currency_code'); // 'Код валюты отправления
            $table->string('name'); // 'Наименование товара
            $table->string('sku')->nullable(); // 'OZON id
            $table->string('offer_id')->nullable(); // 'Артикул
            $table->decimal('products_price', 20, 4)->nullable()->default(0); // 'Итоговая стоимость товара
            $table->string('currency_code_products')->nullable(); // 'Код валюты товара
            $table->decimal('cost_bayer', 20, 4)->nullable()->default(0); // 'Стоимость товара для покупателя
            $table->string('currency_code_buyer')->nullable(); // 'Код валюты покупателя
            $table->integer('quantity'); // 'Количество
            $table->decimal('delivery_price', 20, 4)->nullable()->default(0); // 'Стоимость доставки
            $table->json('related_postings')->nullable(); // 'Связанные отправления
            $table->string('purchased')->nullable(); // 'Выкуп товара
            $table->decimal('old_price', 20, 4)->nullable()->default(0); // 'Цена товара до скидок
            $table->decimal('total_discount_percent', 5, 2)->nullable()->default(0); // 'Скидка %
            $table->decimal('total_discount_value', 20, 4)->nullable()->default(0); // 'Скидка руб
            $table->json('actions')->nullable(); // 'Акции
            $table->string('floor')->nullable(); // 'Подъем на этаж
            $table->string('upper_barcode')->nullable(); // 'Верхний штрихкод
            $table->string('lower_barcode')->nullable(); // 'Нижний штрихкод
            $table->string('cluster_from')->nullable(); // 'Кластер отгрузки
            $table->string('cluster_to')->nullable(); // 'Кластер доставки
            $table->string('region')->nullable(); // 'Регион доставки
            $table->string('city')->nullable(); // 'Город доставки
            $table->string('delivery_type')->nullable(); // 'Способ доставки
            $table->string('is_premium')->nullable(); // 'Сегмент клиента  //?? boolean
            $table->string('payment_type_group_name')->nullable(); // 'Способ оплаты
            $table->string('is_legal')->nullable(); // 'Юридическое лицо    //?? boolean
            $table->string('client_name')->nullable(); // 'Имя покупателя
            $table->string('client_email')->nullable(); // 'Email покупателя
            $table->string('recipient_name')->nullable(); // 'Имя получателя
            $table->string('recipient_tel')->nullable(); // 'Телефон получателя
            $table->string('delivery_address')->nullable(); // 'Адрес доставки
            $table->string('index')->nullable(); // 'Индекс
            $table->string('warehouse')->nullable(); // 'Склад отгрузки
            $table->string('tpl_provider')->nullable(); // 'Перевозчик
            $table->string('delivery_method_name')->nullable(); // 'Название метода
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_order_items');
    }
};
