<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_fbs_warehouses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->unsignedBigInteger('ozon_company_id');
            $table->string('name')->nullable();
            $table->bigInteger('warehouse_id');
            $table->boolean('is_rfbs')->default(false);
            $table->boolean('is_able_to_set_price')->default(false);
            $table->boolean('has_entrusted_acceptance')->default(false);
            $table->string('first_mile_dropoff_point_id')->nullable();
            $table->integer('first_mile_dropoff_timeslot_id')->nullable();
            $table->boolean('first_mile_is_changing')->default(false);
            $table->string('first_mile_type')->nullable();
            $table->boolean('is_kgt')->default(false);
            $table->boolean('can_print_act_in_advance')->default(false);
            $table->integer('min_working_days')->nullable();
            $table->boolean('is_karantin')->default(false);
            $table->boolean('has_postings_limit')->default(false);
            $table->integer('postings_limit')->nullable();
            $table->json('working_days')->nullable(); // Используем тип JSON для массива
            $table->integer('min_postings_limit')->nullable();
            $table->boolean('is_timetable_editable')->default(false);
            $table->string('status')->nullable();
            $table->boolean('is_economy')->default(false);
            $table->boolean('is_presorted')->default(false);
            $table->unique(['warehouse_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_fbs_warehouses');
    }
};
