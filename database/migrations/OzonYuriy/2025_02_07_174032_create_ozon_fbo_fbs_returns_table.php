<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_fbo_fbs_returns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->unsignedBigInteger('return_id');
            $table->unsignedBigInteger('ozon_company_id');
            $table->string('posting_number');
            $table->string('return_reason_name');
            $table->string('type');
            $table->string('schema');
            $table->unsignedBigInteger('order_id');
            $table->string('order_number');

            // Place fields
            $table->unsignedBigInteger('place_id');
            $table->string('place_name');
            $table->string('place_address');

            // Target place fields
            $table->unsignedBigInteger('target_place_id');
            $table->string('target_place_name');
            $table->string('target_place_address');

            // Storage fields
            $table->string('storage_sum_currency_code')->nullable();
            $table->decimal('storage_sum_price', 10, 2)->default(0);
            $table->dateTime('storage_tariffication_first_date')->nullable();
            $table->dateTime('storage_tariffication_start_date')->nullable();
            $table->dateTime('storage_arrived_moment')->nullable();
            $table->integer('storage_days')->default(0);
            $table->string('storage_utilization_sum_currency_code')->nullable();
            $table->decimal('storage_utilization_sum_price', 10, 2)->default(0);
            $table->dateTime('storage_utilization_forecast_date')->nullable();

            // Product fields
            $table->unsignedBigInteger('product_sku');
            $table->string('product_offer_id');
            $table->string('product_name');
            $table->string('product_price_currency_code');
            $table->decimal('product_price_price', 10, 2);
            $table->string('product_price_without_commission_currency_code');
            $table->decimal('product_price_without_commission_price', 10, 2);
            $table->decimal('product_commission_percent', 5, 2)->default(0);
            $table->string('product_commission_currency_code')->nullable();
            $table->decimal('product_commission_price', 10, 2)->default(0);
            $table->integer('product_quantity');

            // Logistic fields
            $table->dateTime('logistic_technical_return_moment')->nullable();
            $table->dateTime('logistic_final_moment')->nullable();
            $table->dateTime('logistic_cancelled_with_compensation_moment')->nullable();
            $table->dateTime('logistic_return_date')->nullable();
            $table->string('logistic_barcode');

            // Visual fields
            $table->unsignedInteger('visual_status_id');
            $table->string('visual_status_display_name');
            $table->string('visual_status_sys_name');
            $table->dateTime('visual_change_moment')->nullable();

            // Exemplars fields
            $table->unsignedBigInteger('exemplars_id');

            // Additional info fields
            $table->boolean('additional_info_is_opened')->default(false);
            $table->boolean('additional_info_is_super_econom')->default(false);

            // Other fields
            $table->unsignedBigInteger('clearing_id');

            $table->unsignedBigInteger('return_clearing_id');
            $table->unsignedBigInteger('source_id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_fbo_fbs_returns');
    }
};
