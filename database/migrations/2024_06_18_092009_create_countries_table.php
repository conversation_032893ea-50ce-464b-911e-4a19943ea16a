<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('full_name')->nullable();
            $table->string('code')->nullable();
            $table->string('iso2')->nullable();
            $table->string('iso3')->nullable();
            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_common')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
