<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Добавляем поле source_type в группы складов
        Schema::table('warehouse_groups', function (Blueprint $table) {
            $table->string('source_type')->nullable()->after('name')
                  ->comment('Источник группы (ozon, wb, manual, etc.)');
            
            $table->index(['source_type', 'cabinet_id']);
        });

        // Добавляем поле source_type в склады
        Schema::table('warehouses', function (Blueprint $table) {
            $table->string('source_type')->nullable()->after('name')
                  ->comment('Источник склада (ozon, wb, manual, etc.)');
            
            $table->index(['source_type', 'cabinet_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->dropIndex(['source_type', 'cabinet_id']);
            $table->dropColumn('source_type');
        });

        Schema::table('warehouse_groups', function (Blueprint $table) {
            $table->dropIndex(['source_type', 'cabinet_id']);
            $table->dropColumn('source_type');
        });
    }
}; 