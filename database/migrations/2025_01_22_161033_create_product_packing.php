<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_packing', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('product_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('packing_id')->constrained();
            $table->bigInteger('quantity')->default(1);
            $table->foreignUuid('measurement_unit_quantity_id')->references('id')->on('measurement_units');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_packing');
    }
};
