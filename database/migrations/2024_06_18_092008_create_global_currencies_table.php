<?php

use App\Enums\Api\Internal\CurrencyTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('global_currencies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->string('num_code');
            $table->string('char_code');
            $table->string('short_name');
            $table->string('external_id');
            $table->string('name')->nullable();
            $table->decimal('value', 13, 9)->default(1);
            $table->date('currency_date');
            $table->boolean('is_default')->default(false);
            $table->json('pluralization')->nullable();
        });

        Schema::create('global_currencies_history', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->string('external_id');
            $table->string('num_code');
            $table->string('char_code');
            $table->string('short_name');
            $table->string('name')->nullable();
            $table->decimal('value', 10, 6)->default(1);
            $table->date('currency_date');
        });

        Schema::create('cabinet_currencies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('currency_id')->nullable()->references('id')->on('global_currencies');
            $table->foreignUuid('cabinet_id')->constrained()->onDelete('cascade');

            $table->boolean('is_accouting')->default(false);

            $table->string('external_id')->nullable();
            $table->string('num_code')->nullable();
            $table->string('char_code')->nullable();
            $table->string('short_name')->nullable();
            $table->string('name')->nullable();

            $table->integer('type')->default(CurrencyTypeEnum::AUTO);
            $table->decimal('markup')->default(0);
            $table->decimal('nominal')->default(1);
            $table->decimal('value', 10, 6)->default(1);
            $table->boolean('is_reverse')->default(false);

            $table->json('pluralization')->nullable();

            $table->foreignUuid('employee_id')->references('id')->on('employees');
            $table->foreignUuid('department_id')->references('id')->on('departments');
            $table->boolean('is_common')->default(false);
            $table->boolean('is_other')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cabinet_currencies');
        Schema::dropIfExists('global_currencies');
        Schema::dropIfExists('global_currencies_history');
    }
};
