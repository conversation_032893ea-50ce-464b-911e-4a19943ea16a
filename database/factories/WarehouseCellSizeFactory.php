<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\MeasurementUnit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseCellSize>
 */
class WarehouseCellSizeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cabinet = Cabinet::factory()->create();
        return [
            'name' => $this->faker->word,
            'cabinet_id' => $cabinet->id,
            'unlimited_size' => $this->faker->boolean,
            'height' => $this->faker->randomFloat(),
            'width' => $this->faker->randomFloat(),
            'length' => $this->faker->randomFloat(),
            'measurement_unit_size_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id
            ]),
            'volume' => $this->faker->randomFloat(),
            'measurement_unit_volume_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id,
            ]),
            'unlimited_load_capacity' => $this->faker->boolean,
            'load_capacity' => $this->faker->randomFloat(),
            'measurement_unit_load_capacity_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id,
            ])
        ];
    }
}
