<?php

namespace Database\Factories;

use App\Models\PermissionGroup;
use App\Models\PermissionCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Permission>
 */
class PermissionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'require_scope' => $this->faker->boolean,
            'guard_name' => $this->faker->unique()->word,
            'operation' => $this->faker->randomElement(['view','update','delete','all','create']),
            'group_id' => PermissionGroup::factory(),
            'category_id' => PermissionCategory::factory()
        ];
    }
}
