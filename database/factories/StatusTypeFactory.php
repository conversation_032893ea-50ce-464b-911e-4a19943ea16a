<?php

namespace Database\Factories;

use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\StatusType>
 */
class StatusTypeFactory extends Factory
{
    use HasOrderedUuid;

    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
            'name' => $this->faker->word
        ];
    }
}
