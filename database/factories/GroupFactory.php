<?php

namespace Database\Factories;

use App\Models\Group;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

class GroupFactory extends Factory
{
    use HasOrderedUuid;
    protected $model = Group::class;

    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => Cabinet::factory(),
            'title' => $this->faker->title,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()
        ];
    }
}
