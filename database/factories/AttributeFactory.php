<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Attribute;
use App\Models\AttributeGroups;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttributeFactory extends Factory
{
    protected $model = Attribute::class;

    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'attribute_groups_id' => AttributeGroups::factory(),
            'name' => $this->faker->name,
            'description' => $this->faker->text,
            'sort_order' => random_int(0,10000),
            'status' => $this->faker->boolean
        ];
    }
}
