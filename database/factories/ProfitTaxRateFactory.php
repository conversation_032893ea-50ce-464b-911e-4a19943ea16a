<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProfitTaxRate>
 */
class ProfitTaxRateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'rate' => random_int(0, 100),
            'description' => $this->faker->text,
            'is_default' => $this->faker->boolean
        ];
    }
}
