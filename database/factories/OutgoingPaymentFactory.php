<?php

namespace Database\Factories;

use App\Models\Status;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\LegalEntity;
use App\Models\SalesChannel;
use App\Models\CabinetCurrency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OutgoingPayment>
 */
class OutgoingPaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'status_id' => Status::factory(),
            'cabinet_id' => Cabinet::factory(),
            'number' => 'INV-' . $this->faker->unique()->numberBetween(1, 100),
            'date_from' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'held' => $this->faker->boolean,
            'without_closing_documents' => $this->faker->boolean,
            'legal_entity_id' => LegalEntity::factory(),
            'contractor_id' => Contractor::factory(),
            'sales_channel_id' => SalesChannel::factory(),
            'sum' => $this->faker->randomFloat(2, 0, 1000),
            'included_vat' => $this->faker->randomFloat(2, 0, 100),
            'comment' => $this->faker->text,
            'currency_id' => CabinetCurrency::factory(),
            'currency_value' => $this->faker->randomFloat(2, 0, 1000),
            'bounded_sum' => $this->faker->randomFloat(2, 0, 1000),
            'not_bounded_sum' => $this->faker->randomFloat(2, 0, 1000),
            'is_imported' => $this->faker->boolean,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_default' => false
        ];
    }
}
