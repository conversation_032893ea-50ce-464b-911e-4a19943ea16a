<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Acceptance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class WarehouseItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = mt_rand(1, 100000);
        $unitPrice = mt_rand(1, 100000);
        $totalPrice = $quantity * $unitPrice;
        return [
            'warehouse_id' => Warehouse::factory(),
            'cell_id' => null,
            'product_id' => Product::factory(),
            'acceptance_id' => Acceptance::factory(),
            'batch_number' => mt_rand(1, 100000),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'received_at' => $this->faker->date,
            'status' => 'in_stock'
        ];
    }
}
