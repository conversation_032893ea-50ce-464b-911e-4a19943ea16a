<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Cabinet;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class CabinetFactory extends Factory
{
    protected $model = Cabinet::class;

    public function definition(): array
    {
        return [
            'id' => Str::orderedUuid()->toString(),
            'user_id' => User::factory(),
            'name' => $this->faker->word()
        ];
    }
}
