<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Departament;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class ContractorGroupsFactory extends Factory
{
    use HasOrderedUuid;

    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->word(),
            'employee_id' => Employee::factory(),
            'department_id' => Departament::factory(),
        ];
    }
}
