<?php

namespace Database\Factories;

use App\Models\WarehouseWorkSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseCalendar>
 */
class WarehouseCalendarFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $schedule = WarehouseWorkSchedule::factory()->create();
        return [
            'schedule_id' => $schedule->id,
            'date' => $this->faker->date,
            'is_working_day' => $this->faker->boolean,
            'is_holiday' => $this->faker->boolean,
            'template_reference' => json_encode(json_decode($schedule->filling_template, true)[0])
        ];
    }
}
