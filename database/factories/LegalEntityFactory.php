<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class LegalEntityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => \Str::orderedUuid()->toString(),
            'cabinet_id' => Cabinet::factory(),
            'short_name' => $this->faker->name(),
            'code' => mt_rand(1, 100000),
            'phone' => mt_rand(1, 100000),
            'fax' => $this->faker->phoneNumber(),
            'email' => $this->faker->email,
            'discount_card' => $this->faker->creditCardNumber(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()
        ];
    }
}
